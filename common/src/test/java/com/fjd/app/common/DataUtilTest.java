package com.fjd.app.common;

import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import com.fjd.app.common.util.DataUtil;

import org.junit.Test;

public class DataUtilTest {

	// Independent CRC-16/MODBUS (poly 0xA001, init 0xFFFF), returns little-endian bytes
	private static byte[] crc16Modbus(byte[] buf, int len) {
		int crc = 0xFFFF;
		for (int i = 0; i < len; i++) {
			crc ^= (buf[i] & 0xFF);
			for (int j = 0; j < 8; j++) {
				if ((crc & 0x0001) != 0) crc = (crc >>> 1) ^ 0xA001;
				else crc >>>= 1;
			}
		}
		return new byte[]{(byte) (crc & 0xFF), (byte) ((crc >>> 8) & 0xFF)};
	}

	// Independent CRC-16-CCITT (poly 0x1021, init 0xFFFF), returns big-endian bytes
	private static byte[] crc16Ccitt(byte[] data) {
		int crc = 0xFFFF;
		for (byte b : data) {
			crc ^= (b & 0xFF) << 8;
			for (int i = 0; i < 8; i++) {
				crc = ((crc & 0x8000) != 0) ? ((crc << 1) ^ 0x1021) : (crc << 1);
				crc &= 0xFFFF;
			}
		}
		return new byte[]{(byte) ((crc >>> 8) & 0xFF), (byte) (crc & 0xFF)};
	}

	@Test
	public void test_unsignedByteToInt() {
		assertEquals(255, DataUtil.unsignedByteToInt((byte) 0xFF));
		assertEquals(0, DataUtil.unsignedByteToInt((byte) 0x00));
		assertEquals(128, DataUtil.unsignedByteToInt((byte) 0x80));
	}

	@Test
	public void test_sumCheck_basic() {
		byte[] arr = new byte[]{0, 1, 2, 3, 4};
		assertEquals((byte) 10, DataUtil.sumCheck(arr, 0, 5));
		assertEquals((byte) 5, DataUtil.sumCheck(arr, 2, 4));
	}

	@Test
	public void test_checksum_twoBytes() {
		byte[] data = new byte[]{(byte) 0xFF, 0x01, 0x02};
		byte[] cs = DataUtil.checksum(data);
		int sum = (0xFF & 0xFF) + 0x01 + 0x02; // 0x0102
		assertArrayEquals(new byte[]{(byte) (sum & 0xFF), (byte) ((sum >>> 8) & 0xFF)}, cs);
	}

	@Test
	public void test_yongMaoCrc16_matches_reference() {
		byte[] data = new byte[]{0x01, 0x02, 0x03, 0x04};
		assertArrayEquals(crc16Modbus(data, data.length), DataUtil.yongMaoCrc16(data, data.length));
	}

	@Test
	public void test_fjLidarCrc16_matches_reference() {
		byte[] data = new byte[]{0x55, (byte) 0xAA, 0x10, 0x20, 0x30};
		assertArrayEquals(crc16Ccitt(data), DataUtil.fjLidarCrc16(data));
	}

	@Test
	public void test_hex_roundtrip_and_edge_cases() {
		byte[] bytes = new byte[]{0x0a, 0x1f, (byte) 0xff};
		String hex = DataUtil.byte2hex(bytes);
		assertEquals("0a 1f ff", hex.trim());
		byte[] back = DataUtil.hex2Bytes(hex.trim());
		assertArrayEquals(bytes, back);

		// single byte
		assertArrayEquals(new byte[]{(byte) 0xAB}, DataUtil.hex2Bytes("ab"));
		// empty
		assertArrayEquals(new byte[]{}, DataUtil.hex2Bytes(""));

		// invalid length
		try {
			DataUtil.hex2Bytes("abc");
			fail("Expected IllegalArgumentException");
		} catch (IllegalArgumentException ignored) {
		}
		// invalid char
		try {
			DataUtil.hex2Bytes("zz");
			fail("Expected IllegalArgumentException");
		} catch (IllegalArgumentException ignored) {
		}
		// missing space separator
		try {
			DataUtil.hex2Bytes("0a1f");
			fail("Expected IllegalArgumentException");
		} catch (IllegalArgumentException ignored) {
		}
	}

	@Test
	public void test_bytesToHexString_length() {
		byte[] arr = new byte[]{0x0a, 0x1f, (byte) 0xff};
		assertEquals("0A1F", DataUtil.bytesToHexString(arr, 2));
		assertEquals("0A1FFF", DataUtil.bytesToHexString(arr, 10)); // length bigger than array
	}

	@Test
	public void test_int2byte2_and_back() {
		int value = 0xABCD;
		byte[] arr = DataUtil.int2byte2(value);
		assertEquals((byte) 0xAB, arr[0]);
		assertEquals((byte) 0xCD, arr[1]);
		assertEquals(value & 0xFFFF, DataUtil.byte2ToInt(arr, 0));
	}

	@Test
	public void test_signedInt2byte2_roundtrip_with_convertTwoSignInt() {
		int s = -12345; // within 16-bit range
		byte[] arr = DataUtil.signedInt2byte2(s);
		int back = DataUtil.convertTwoSignInt(arr);
		assertEquals(s, (short) back); // cast to short range equivalence
	}
}


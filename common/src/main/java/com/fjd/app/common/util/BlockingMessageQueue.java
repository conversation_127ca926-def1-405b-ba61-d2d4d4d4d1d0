package com.fjd.app.common.util;

import android.os.SystemClock;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 发送数据需要等待回复的消息队列
 *
 * <AUTHOR>
 * @since 2024/3/25
 */
public class BlockingMessageQueue {
	private static final String TAG = "BlockingMessageQueue";
	private final LinkedBlockingQueue<byte[]> queue = new LinkedBlockingQueue<>(10);
	private final Messenger messenger;
	private boolean running;
	private CountDownLatch countDownLatch;
	private Thread senderThread;

	public BlockingMessageQueue(Messenger messenger) {
		this.messenger = messenger;
	}

	public void send(byte[] data) {
		queue.offer(data);
	}

	/**
	 * 接收到回包，可以发送下一包数据
	 */
	public void onDataAck() {
		queue.poll();
		if (countDownLatch != null) {
			countDownLatch.countDown();
		}
	}

	public void start() {
		if (senderThread != null) {
			LogUtils.w(TAG, "start: senderThread already started");
			return;
		}
		senderThread = new SenderThread();
		running = true;
		senderThread.start();
	}

	public void stop() {
		running = false;
		queue.clear();
		if (countDownLatch != null) {
			countDownLatch.countDown();
		}
		senderThread = null;
	}

	public int size() {
		return queue.size();
	}


	public interface Messenger {
		/**
		 * 发送数据的具体实现
		 *
		 * @param data data
		 */
		void onSendData(byte[] data);
	}

	private class SenderThread extends Thread {
		@Override
		public void run() {
			super.run();
			while (running) {
				byte[] data = queue.peek();
				if (data == null) {
					SystemClock.sleep(10);
					continue;
				}
				countDownLatch = new CountDownLatch(1);
				messenger.onSendData(data);
				try {
					if (!countDownLatch.await(5, TimeUnit.SECONDS)) {
						LogUtils.w(TAG, "waiting for ack timeout!");
					}
				} catch (InterruptedException e) {
					LogUtils.e(TAG, "run exception: " + e.getMessage());
				}
			}
		}
	}
}

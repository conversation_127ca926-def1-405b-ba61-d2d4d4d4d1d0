package com.fjd.app.common.motor;

import android.os.Handler;
import android.os.Looper;

import com.fjd.app.common.tcp.client.TcpClient;
import com.fjd.app.common.util.BlockingMessageQueue;
import com.fjd.app.common.util.DataUtil;
import com.fjd.app.common.util.LogUtils;

/**
 * 舵机tcp控制类
 *
 * <AUTHOR>
 * @since 2024/3/21
 */
public class MotorTcpController {
	private static final String TAG = "MotorTcpController";
	private static final int ROTATE_SPEED = 1;
	private final Handler handler = new Handler(Looper.getMainLooper());
	private final BlockingMessageQueue messageQueue;
	private final MotorDataParser parser;
	private TcpClient tcpClient;
	private MotorConfig config;

	private int initialPosition = 2048;
	private int minPosition;
	private int maxPosition;
	/**
	 * 只有第一次连上或者主动断开连接后再重连上时才重置舵机位置
	 */
	private boolean hasInit = false;

	private MotorTcpController() {
		messageQueue = new BlockingMessageQueue(data -> {
			LogUtils.d(TAG, "send: " + DataUtil.byte2hex(data));
			if (tcpClient != null) {
				tcpClient.send(data);
			}
		});

		parser = new MotorDataParser(data -> messageQueue.onDataAck());
	}

	public void setMotorConfig(MotorConfig config) {
		this.config = config;
	}

	public void connect() {
		if (config == null) {
			LogUtils.e(TAG, "config is null!");
			return;
		}
		initialPosition = 1024 * config.getInitialAngle() / 90;
		if (tcpClient == null) {
			int maxOffsetAngle = 1024 * config.getLimitAngle() / 90;
			minPosition = initialPosition - maxOffsetAngle;
			maxPosition = initialPosition + maxOffsetAngle;
			tcpClient = new TcpClient(config.getMotorIp(), 51001, new TcpClient.Callback() {
				@Override
				public void onConnectStatusChanged(boolean connected) {
					LogUtils.i(TAG, "onConnectStatusChanged: " + connected);
					if (connected) {
						handler.removeCallbacksAndMessages(null);
						messageQueue.send(MotorProtocol.Companion.setWorkMode(0));
						if (!hasInit) {
							hasInit = true;
							messageQueue.send(MotorProtocol.Companion.goToPosition(initialPosition, 5));
						}
						messageQueue.send(MotorProtocol.Companion.limitAngle(minPosition, maxPosition));
					} else {
						handler.postDelayed(() -> connect(), 2_000);
					}
				}

				@Override
				public void onDataReceived(byte[] data) {
					LogUtils.d(TAG, "received: " + DataUtil.byte2hex(data));
					parser.addData(data);
				}
			});
			messageQueue.start();
		}
		tcpClient.connect();
	}

	public void disconnect() {
		if (tcpClient != null) {
			tcpClient.disconnect();
			messageQueue.stop();
			tcpClient = null;
			hasInit = false;
		}
	}

	public void startMoving(boolean up) {
		LogUtils.d(TAG, "startMoving: up ->" + up);
		up = !up;
		byte[] data = MotorProtocol.Companion.goToPosition(up ? minPosition : maxPosition, ROTATE_SPEED);
		if (messageQueue.size() > 0) {
			//只有前面指令全部执行完才允许执行控制动作
			LogUtils.w(TAG, "startMoving: messageQueue is not empty!");
			return;
		}
		messageQueue.send(data);
	}

	public void stopMoving() {
		LogUtils.d(TAG, "stopMoving");
		byte[] data = MotorProtocol.Companion.goToPosition(0, 0);
		messageQueue.send(data);
	}

	private static class SingletonHolder {
		private static final MotorTcpController INSTANCE = new MotorTcpController();
	}

	public static MotorTcpController getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public interface MotorConfig {
		String getMotorIp();

		int getInitialAngle();

		int getLimitAngle();
	}
}

package com.fjd.app.common.tcp;

import android.os.SystemClock;

import com.fjd.app.common.util.LogUtils;

import java.io.IOException;
import java.io.InputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 本地TcpServer
 *
 * <AUTHOR>
 */
public class TcpServer {
	private static final String TAG = "TcpServer";
	private final int port;
	private final Callback callback;
	private final boolean singleConnection;
	private final AtomicBoolean clientConnected = new AtomicBoolean(false);
	private ServerSocket serverSocket;

	/**
	 * 构造方法，包含初始化配置
	 *
	 * @param port     端口号
	 * @param callback 接收数据回调
	 */
	public TcpServer(int port, Callback callback) {
		this(port, false, callback);
	}

	/**
	 * 构造方法，包含初始化配置
	 *
	 * @param port             端口号
	 * @param singleConnection 是否限制单客户端连接
	 * @param callback         接收数据回调
	 */
	public TcpServer(int port, boolean singleConnection, Callback callback) {
		this.port = port;
		this.callback = callback;
		this.singleConnection = singleConnection;
	}

	/**
	 * 开启服务
	 */
	public void start() {
		new Thread(() -> {
			try {
				serverSocket = new ServerSocket(port);
				LogUtils.d(TAG, "serve at " + serverSocket);
				while (true) {
					if (singleConnection && clientConnected.get()) {
						SystemClock.sleep(1_000);
						continue;
					}
					Socket socket = serverSocket.accept();
					socket.setSoTimeout(10_000);
					LogUtils.d(TAG, "client connected -> " + socket);
					clientConnected.compareAndSet(false, true);
					startReader(socket);
				}
			} catch (IOException e) {
				LogUtils.e(TAG, "server exception -> " + e.getMessage());
			}
		}).start();
	}

	/**
	 * 停止服务
	 */
	public void stop() {
		if (serverSocket != null) {
			try {
				serverSocket.close();
			} catch (IOException e) {
				LogUtils.e(TAG, "stop exception: " + e.getMessage());
			}
		}
	}

	private void startReader(Socket socket) {
		new Thread(() -> {
			try (InputStream in = socket.getInputStream()) {
				byte[] buffer = new byte[2048];
				for (int len; (len = in.read(buffer)) != -1; ) {
					if (callback != null) {
						byte[] data = new byte[len];
						System.arraycopy(buffer, 0, data, 0, len);
						callback.onDataReceived(data);
					}
				}
			} catch (IOException e) {
				LogUtils.e(TAG, "read exception -> " + e.getMessage());
			} finally {
				LogUtils.d(TAG, "read end at " + socket);
				clientConnected.compareAndSet(true, false);
			}
		}).start();
	}

	public interface Callback {
		void onDataReceived(byte[] data);
	}
}

package com.fjd.app.common.websocket.client;

import com.fjd.app.common.util.LogUtils;
import com.fjd.app.common.websocket.WebSocketListener;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.nio.ByteBuffer;

/**
 * 简单的WebSocketClient
 *
 * <AUTHOR>
 * @since 2024/5/9
 */
public class SimpleWebSocketClient extends WebSocketClient {

	private final String TAG;
	private final WebSocketListener listener;

	public SimpleWebSocketClient(URI serverUri, WebSocketListener listener) {
		this("SimpleWebSocketClient", serverUri, 60, listener);
	}

	/**
	 * 构造方法
	 *
	 * @param logTag                日志打印TAG
	 * @param serverUri             ws服务地址
	 * @param connectionLostTimeout 检测连接是否中断的间隔秒数
	 * @param listener              连接回调
	 */
	public SimpleWebSocketClient(String logTag, URI serverUri, int connectionLostTimeout, WebSocketListener listener) {
		super(serverUri, new Draft_6455(), null, 10_000);
		this.TAG = logTag;
		this.listener = listener;
		setConnectionLostTimeout(connectionLostTimeout);
	}

	@Override
	public void onOpen(ServerHandshake handshake) {
		LogUtils.i(TAG, "onOpen");
		if (listener != null) {
			listener.onConnectStatusChanged(true, 0);
		}
	}

	@Override
	public void onMessage(String message) {
		LogUtils.d(TAG, "onMessage: " + message);
		if (listener != null) {
			listener.onMessage(message);
		}
	}

	@Override
	public void onMessage(ByteBuffer bytes) {
		int len = bytes.remaining();
		LogUtils.d(TAG, "onBytes: " + len);
		if (listener != null) {
			byte[] data = new byte[len];
			bytes.get(data);
			listener.onMessage(data);
		}
	}

	@Override
	public void onClose(int code, String reason, boolean remote) {
		LogUtils.i(TAG, "onClose: code -> " + code + ", reason -> " + reason + ", remote -> " + remote);
		if (listener != null) {
			listener.onConnectStatusChanged(false, code);
		}
	}

	@Override
	public void onError(Exception ex) {
		LogUtils.e(TAG, "onError: " + ex.getMessage());
	}

}

package com.fjd.app.common.util;

import android.util.Log;

public class LogUtils {

	private static boolean logEnabled;
	private static Logger loggerImpl;

	public static void setLogEnabled(boolean enabled) {
		logEnabled = enabled;
	}

	public static void setLogger(Logger logger) {
		loggerImpl = logger;
	}

	public static void v(String tag, String msg) {
		if (!logEnabled) {
			return;
		}
		if (loggerImpl != null) {
			loggerImpl.v(tag, msg);
		} else {
			Log.v(tag, msg);
		}
	}

	public static void d(String tag, String msg) {
		if (!logEnabled) {
			return;
		}
		if (loggerImpl != null) {
			loggerImpl.d(tag, msg);
		} else {
			Log.d(tag, msg);
		}
	}

	public static void i(String tag, String msg) {
		if (!logEnabled) {
			return;
		}
		if (loggerImpl != null) {
			loggerImpl.i(tag, msg);
		} else {
			Log.i(tag, msg);
		}
	}

	public static void w(String tag, String msg) {
		if (!logEnabled) {
			return;
		}
		if (loggerImpl != null) {
			loggerImpl.w(tag, msg);
		} else {
			Log.w(tag, msg);
		}
	}

	public static void e(String tag, String msg) {
		if (!logEnabled) {
			return;
		}
		if (loggerImpl != null) {
			loggerImpl.e(tag, msg);
		} else {
			Log.e(tag, msg);
		}
	}

	public interface Logger {
		void v(String tag, String msg);

		void d(String tag, String msg);

		void i(String tag, String msg);

		void w(String tag, String msg);

		void e(String tag, String msg);
	}
}

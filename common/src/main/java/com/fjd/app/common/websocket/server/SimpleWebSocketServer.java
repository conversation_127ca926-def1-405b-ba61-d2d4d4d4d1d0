package com.fjd.app.common.websocket.server;

import com.fjd.app.common.util.LogUtils;
import com.fjd.app.common.websocket.WebSocketListener;

import org.java_websocket.WebSocket;
import org.java_websocket.handshake.ClientHandshake;
import org.java_websocket.server.WebSocketServer;

import java.net.InetSocketAddress;
import java.nio.ByteBuffer;

/**
 * 简单的WebSocket Server，支持在本地指定端口开启WebSocket服务
 *
 * <AUTHOR>
 * @since 2024/5/9
 */
public class SimpleWebSocketServer extends WebSocketServer {

	private final String TAG;
	private final WebSocketListener listener;

	public SimpleWebSocketServer(int port, WebSocketListener listener) {
		this("SimpleWebSocketServer", port, listener);
	}

	/**
	 * 构造方法
	 *
	 * @param logTag   日志TAG
	 * @param port     指定的端口
	 * @param listener 连接回调
	 */
	public SimpleWebSocketServer(String logTag, int port, WebSocketListener listener) {
		super(new InetSocketAddress(port));
		this.TAG = logTag;
		this.listener = listener;
		setReuseAddr(true);
	}

	@Override
	public void onOpen(WebSocket conn, ClientHandshake handshake) {
		LogUtils.i(TAG, "onOpen");
		if (listener != null) {
			listener.onConnectStatusChanged(true, 0);
		}
	}

	@Override
	public void onClose(WebSocket conn, int code, String reason, boolean remote) {
		LogUtils.i(TAG, "onClose: code -> " + code + ", reason -> " + reason + ", remote -> " + remote);
		if (listener != null) {
			listener.onConnectStatusChanged(false, code);
		}
	}

	@Override
	public void onMessage(WebSocket conn, String message) {
		LogUtils.d(TAG, "onMessage: " + message);
		if (listener != null) {
			listener.onMessage(message);
		}
	}

	@Override
	public void onMessage(WebSocket conn, ByteBuffer bytes) {
		int len = bytes.remaining();
		LogUtils.d(TAG, "onBytes: " + len);
		if (listener != null) {
			byte[] data = new byte[len];
			bytes.get(data);
			listener.onMessage(data);
		}
	}

	@Override
	public void onError(WebSocket conn, Exception ex) {
		LogUtils.e(TAG, "onError: " + ex.getMessage());
	}

	@Override
	public void onStart() {
		LogUtils.i(TAG, "onStart");
	}

}

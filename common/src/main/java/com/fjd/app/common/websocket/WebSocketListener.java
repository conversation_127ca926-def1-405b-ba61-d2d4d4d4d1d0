package com.fjd.app.common.websocket;

/**
 * WebSocket状态监听
 *
 * <AUTHOR>
 * @since 2024/5/11
 */
public interface WebSocketListener {

	/**
	 * 连接状态变化
	 *
	 * @param connected 用在Client时表示是否连接上了服务端，用在Server时表示是否有客户端连接成功
	 * @param code      websocket close状态码{@link org.java_websocket.framing.CloseFrame}
	 */
	void onConnectStatusChanged(boolean connected, int code);

	/**
	 * 接收到文本消息
	 *
	 * @param msg msg
	 */
	void onMessage(String msg);

	/**
	 * 接收到字节流
	 *
	 * @param bytes bytes
	 */
	void onMessage(byte[] bytes);
}

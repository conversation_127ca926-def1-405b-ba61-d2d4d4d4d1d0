package com.fjd.app.common.motor;

import com.fjd.app.common.util.DataUtil;

/**
 * 舵机回包解析工具
 *
 * <AUTHOR>
 * @since 2024/3/25
 */
public class MotorDataParser {
	private static final byte[] HEADER = new byte[]{(byte) 0xff, (byte) 0xff};
	private static final int BUFFER_MAX_LENGTH = 1024;
	private final Callback callback;
	private byte[] buffer = new byte[0];

	public MotorDataParser(Callback callback) {
		this.callback = callback;
	}

	public void addData(byte[] data) {
		if (buffer.length > BUFFER_MAX_LENGTH) {
			buffer = new byte[0];
		}
		buffer = DataUtil.byteMerger(buffer, data);
		int headerIdx;
		while ((headerIdx = findHeader()) >= 0) {
//			Timber.d("buffer: %s", DataUtil.byte2hex(buffer));
			if (buffer.length - headerIdx < 6) {
				//一包数据最小长度是6
				return;
			}
			int dataLen = HEADER.length + 1 + 1 + (buffer[headerIdx + 3] & 0xff);
			if (headerIdx + dataLen > buffer.length) {
				//buffer长度不够，等待下一包数据
				return;
			}
			byte[] bytes = DataUtil.subBytes(buffer, headerIdx, dataLen);
			if (callback != null) {
//				Timber.d("onData: %s", DataUtil.byte2hex(bytes));
				callback.onData(bytes);
			}
			buffer = DataUtil.subBytes(buffer, headerIdx + dataLen + 1, buffer.length - (headerIdx + dataLen));
		}
	}

	private int findHeader() {
		if (buffer.length < HEADER.length) {
			return -1;
		}
		for (int i = 0; i < buffer.length - 1; i++) {
			if (buffer[i] == HEADER[0] && buffer[i + 1] == HEADER[1]) {
				return i;
			}
		}
		return -1;
	}

	public interface Callback {
		void onData(byte[] data);
	}
}

package com.fjd.app.common.motor

import com.fjd.app.common.util.DataUtil
import kotlin.experimental.inv

/**
 * 舵机协议封装
 *
 * <AUTHOR>
 */
class MotorProtocol(
	val motorId: Int,
	val error: Boolean,
	val param: ByteArray,
) {
	companion object {
		/**
		 * 舵机id
		 */
		private const val MOTOR_ID = 0x01

		/**
		 * 协议包头
		 */
		private val header = byteArrayOf(0xff.toByte(), 0xff.toByte())

		private fun build(instruction: Instruction, param: ByteArray): ByteArray {
			val len = param.size + 2
			val bytes: ByteArray =
				byteArrayOf() + MOTOR_ID.toByte() + len.toByte() + instruction.cmd + param
			val sumCheck = DataUtil.sumCheck(bytes, 0, bytes.size).inv()
			return header + bytes + sumCheck
		}

		fun parse(data: ByteArray): MotorProtocol? {
			if (data.size < 5 || data.size < 2 + 1 + data[2].toInt()) {
				return null
			}
			// TODO: 校验和检测

			var param: ByteArray = byteArrayOf()
			if (data[2] > 2) {
				param += data.slice(5..5 + data[2] - 2)
			}
			return MotorProtocol(
				motorId = data[2].toInt(),
				error = (data[4].toInt() != 0),
				param = param,
			)
		}

		/**
		 * 设置舵机工作模式
		 *
		 * @param mode 0-位置伺服模式;1-电机恒速模式;
		 * @return ByteArray
		 */
		fun setWorkMode(mode: Int): ByteArray {
			val param = byteArrayOf(0x21, mode.toByte())
			return build(Instruction.WRITE_DATA, param)
		}

		/**
		 * 电机恒速模式-设置旋转速度和方向
		 *
		 * @param up 是否向上
		 * @param speed 转速
		 * @return ByteArray
		 */
		fun rotate(up: Boolean = true, speed: Int = 1): ByteArray {
			var param = byteArrayOf(0x2e)
			val speedBytes = DataUtil.int2byte2(speed)
			if (up) {
				speedBytes[0] = (speedBytes[0].toInt() and (1 shl 7).inv()).toByte()
			} else {
				speedBytes[0] = (speedBytes[0].toInt() or (1 shl 7)).toByte()
			}
			param += byteArrayOf(speedBytes[1], speedBytes[0])
			return build(Instruction.WRITE_DATA, param)
		}

		/**
		 * 设置角度限位
		 *
		 * @param min 最小角度
		 * @param max 最大角度
		 * @return ByteArray
		 */
		fun limitAngle(min: Int, max: Int): ByteArray {
			var param = byteArrayOf(0x09)
			val minBytes = DataUtil.int2byte2(min)
			val maxBytes = DataUtil.int2byte2(max)
			param += byteArrayOf(minBytes[1], minBytes[0], maxBytes[1], maxBytes[0])
			return build(Instruction.WRITE_DATA, param)
		}

		/**
		 * 位置伺服模式-控制舵机到指定位置
		 *
		 * @param step 位置
		 * @param speed 转速
		 * @return ByteArray
		 */
		fun goToPosition(step: Int, speed: Int): ByteArray {
			var param = byteArrayOf(0x2a)
			val stepBytes = DataUtil.int2byte2(step) //位置
			val timeBytes = DataUtil.int2byte2(0) //时间
			val speedBytes = DataUtil.int2byte2(speed) //速度
			param += byteArrayOf(
				stepBytes[1],
				stepBytes[0],
				timeBytes[1],
				timeBytes[0],
				speedBytes[1],
				speedBytes[0],
			)
			return build(Instruction.WRITE_DATA, param)
		}
	}

	override fun equals(other: Any?): Boolean {
		if (this === other) return true
		if (javaClass != other?.javaClass) return false

		other as MotorProtocol

		if (motorId != other.motorId) return false
		if (error != other.error) return false
		return param.contentEquals(other.param)
	}

	override fun hashCode(): Int {
		var result = motorId
		result = 31 * result + error.hashCode()
		result = 31 * result + param.contentHashCode()
		return result
	}

}

/**
 * 指令类型
 *
 * <AUTHOR>
 */
sealed class Instruction(val cmd: Byte) {
	/**
	 * 查询工作状态，参数长度：0
	 */
	data object PING : Instruction(0x01.toByte())

	/**
	 * 查询控制表里的字符，参数长度：2
	 */
	data object READ_DATA : Instruction(0x02.toByte())

	/**
	 * 往控制表里写入字符，参数长度：>=1
	 */
	data object WRITE_DATA : Instruction(0x03.toByte())

	/**
	 * 类似于WRITE DATA，但是控制字符写入后并不马上动作，直到ACTION指令到达，参数长度：>=2
	 */
	data object REGWRITE_DATA : Instruction(0x04.toByte())

	/**
	 * 触发REG WRITE写入的动作，参数长度：0
	 */
	data object ACTION : Instruction(0x05.toByte())

	/**
	 *用于同时查询多个舵机，参数长度：>=3
	 */
	data object SYCNREAD_DATA : Instruction(0x82.toByte())

	/**
	 * 用于同时控制多个舵机，参数长度：>=2
	 */
	data object SYCNWRITE_DATA : Instruction(0x83.toByte())

	/**
	 * 把控制表复位为出厂值，参数长度：0
	 */
	data object RECOVERY : Instruction(0x06.toByte())

	/**
	 * 重置舵机状态(重置舵机圈数)，参数长度：0
	 */
	data object RESET : Instruction(0x0a.toByte())
}

plugins {
	alias(libs.plugins.android.library)
	alias(libs.plugins.kotlin.android)
}

android {
	namespace 'com.fjd.app.common'
	compileSdk versions.compileSdk

	defaultConfig {
		minSdk versions.minSdk

		testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
		consumerProguardFiles "consumer-rules.pro"
	}

	buildTypes {
		release {
			minifyEnabled false
			proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
		}
	}
	compileOptions {
		sourceCompatibility JavaVersion.VERSION_21
		targetCompatibility JavaVersion.VERSION_21
	}
	kotlinOptions {
		jvmTarget = '21'
	}
}

dependencies {

	implementation libs.androidx.core.ktx
	implementation libs.androidx.appcompat
	implementation libs.google.material
	api libs.java.websocket
	implementation libs.utilcode

	testImplementation libs.junit
	androidTestImplementation libs.androidx.junit
	androidTestImplementation libs.androidx.test.espresso
}

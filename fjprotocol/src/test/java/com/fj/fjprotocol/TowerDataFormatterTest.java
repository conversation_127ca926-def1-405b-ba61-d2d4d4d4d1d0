package com.fj.fjprotocol;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.fjd.app.common.util.DataUtil;

import org.junit.Test;

public class TowerDataFormatterTest {

	@Test
	public void test_format_basic_structure_and_checksum() {
		// Arrange
		TransManager.sReqValue = 0x1234; // deterministic seq
		TowerDataFormatter formatter = new TowerDataFormatter();
		byte[] payload = new byte[]{ProtocolConstants.DISCRETE_COMMAND, 0x01, 0x02};
		formatter.setDataLength(DataUtil.int2byte2(payload.length - 1));
		formatter.setData(payload);

		// Act
		byte[] frame = formatter.getFormattedData();

		// Assert: head 2, version 4, src 4, target 4, seq 2, shouldAck 1, ackSeq 2, dataLen 2 => 21 bytes before data
		// Total before checksum/tail = 21 + dataLen (3) = 24 bytes
		assertTrue(frame.length >= 24 + 1 + 2); // includes checksum(1) + tail(2)
		assertEquals((byte) 0xEB, frame[0]);
		assertEquals((byte) 0x90, frame[1]);

		// seq equals 0x1234 big-endian
		int seqHigh = frame[14];
		int seqLow = frame[15];
		assertEquals(0x12, seqHigh & 0xFF);
		assertEquals(0x34, seqLow & 0xFF);

		// data length 0x0002 (payload length - 1)
		assertEquals(0x00, frame[19] & 0xFF);
		assertEquals(0x02, frame[20] & 0xFF);

		// payload positioned after fixed header (index 21 is first data byte)
		int dataStart = 21;
		assertEquals(ProtocolConstants.DISCRETE_COMMAND, frame[dataStart]);
		assertEquals(0x01, frame[dataStart + 1]);
		assertEquals(0x02, frame[dataStart + 2]);

		// checksum is sum of bytes from index 2 to end of data
		byte expectedChecksum = DataUtil.sumCheck(frame, 2, dataStart + payload.length);
		byte actualChecksum = frame[dataStart + payload.length];
		assertEquals(expectedChecksum, actualChecksum);

		// tail
		int tailStart = dataStart + payload.length + 1;
		assertEquals(0x0D, frame[tailStart]);
		assertEquals(0x0A, frame[tailStart + 1]);
	}
}


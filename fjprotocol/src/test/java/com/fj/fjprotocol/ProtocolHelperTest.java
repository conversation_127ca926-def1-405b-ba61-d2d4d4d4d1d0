package com.fj.fjprotocol;

import static org.junit.Assert.assertEquals;

import com.fj.fjprotocol.data.GeoData;
import com.fjd.app.common.util.DataUtil;

import org.junit.Test;

import java.util.Arrays;

public class ProtocolHelperTest {

	private static class Frame {
		byte cmdSet;
		byte[] data; // full data payload (cmdSet + cmdSpecific)
		int dataLenField; // value from frame (len = data.length - 1)
		byte checksum;
	}

	private Frame parseFrame(byte[] frame) {
		// fixed header length = 21
		assertEquals((byte) 0xEB, frame[0]);
		assertEquals((byte) 0x90, frame[1]);
		int lenHigh = frame[19] & 0xFF;
		int lenLow = frame[20] & 0xFF;
		int dataLenField = (lenHigh << 8) | lenLow; // equals data.length - 1
		int dataStart = 21;
		int dataLength = dataLenField + 1;
		byte[] data = Arrays.copyOfRange(frame, dataStart, dataStart + dataLength);
		byte checksum = frame[dataStart + dataLength];
		// validate checksum matches sumCheck from index 2 to end of data
		byte expected = DataUtil.sumCheck(frame, 2, dataStart + dataLength);
		assertEquals(expected, checksum);
		Frame f = new Frame();
		f.cmdSet = data[0];
		f.data = data;
		f.dataLenField = dataLenField;
		f.checksum = checksum;
		return f;
	}

	@Test
	public void test_heartbeat() {
		TransManager.sReqValue = 0;
		byte[] frame = ProtocolHelper.heartbeat();
		Frame f = parseFrame(frame);
		assertEquals(ProtocolConstants.HEARTBEAT, f.cmdSet);
		assertEquals(0, f.dataLenField);
	}

	@Test
	public void test_queryEcuVersion() {
		byte[] frame = ProtocolHelper.queryEcuVersion();
		Frame f = parseFrame(frame);
		assertEquals(ProtocolConstants.DISCRETE_COMMAND, f.cmdSet);
		assertEquals(1, f.dataLenField); // only cmdId
		assertEquals(ProtocolConstants.ECU_VERSION, f.data[1]);
	}

	@Test
	public void test_setArm_scaling_and_layout() {
		double arm = 12.3456;
		double near = 1.5;
		double far = 2.25;
		byte[] frame = ProtocolHelper.setArm(arm, near, far);
		Frame f = parseFrame(frame);
		assertEquals(ProtocolConstants.DISCRETE_COMMAND, f.cmdSet);
		assertEquals(1 + 12, f.dataLenField); // cmdId + 3*4 bytes
		assertEquals(ProtocolConstants.SET_ARM, f.data[1]);
		// Values are intToByte4(value*10000)
		int armScaled = (int) (arm * 10_000);
		int nearScaled = (int) (near * 10_000);
		int farScaled = (int) (far * 10_000);
		// big-endian 4 bytes each at offsets 2,6,10
		int gotArm = DataUtil.byte4ToInt(Arrays.copyOfRange(f.data, 2, 6), 0);
		int gotNear = DataUtil.byte4ToInt(Arrays.copyOfRange(f.data, 6, 10), 0);
		int gotFar = DataUtil.byte4ToInt(Arrays.copyOfRange(f.data, 10, 14), 0);
		assertEquals(armScaled, gotArm);
		assertEquals(nearScaled, gotNear);
		assertEquals(farScaled, gotFar);
	}

	@Test
	public void test_setWindSpeedWarn() {
		byte[] frame = ProtocolHelper.setWindSpeedWarn(true, 12.3);
		Frame f = parseFrame(frame);
		assertEquals(ProtocolConstants.DISCRETE_COMMAND, f.cmdSet);
		assertEquals(1 + 3, f.dataLenField);
		assertEquals(ProtocolConstants.SET_WIND_SPEED_WARN, f.data[1]);
		assertEquals(1, f.data[2] & 0xFF);
		int gotWarn = ((f.data[3] & 0xFF) << 8) | (f.data[4] & 0xFF);
		assertEquals((int) (12.3 * 100), gotWarn);
	}

	@Test
	public void test_setTrolleyWidth_null_safe() {
		byte[] frame = ProtocolHelper.setTrolleyWidth(null);
		Frame f = parseFrame(frame);
		assertEquals(ProtocolConstants.DISCRETE_COMMAND, f.cmdSet);
		assertEquals(1 + 2, f.dataLenField);
		assertEquals(ProtocolConstants.SET_TROLLEY_WIDTH, f.data[1]);
		int got = ((f.data[2] & 0xFF) << 8) | (f.data[3] & 0xFF);
		assertEquals(0, got);
	}

	@Test
	public void test_setInclinationOffset_signed() {
		double off = -1.23;
		byte[] frame = ProtocolHelper.setInclinationOffset(off);
		Frame f = parseFrame(frame);
		assertEquals(ProtocolConstants.DISCRETE_COMMAND, f.cmdSet);
		assertEquals(1 + 2, f.dataLenField);
		assertEquals(ProtocolConstants.SET_INCLINATION_OFFSET, f.data[1]);
		short got = (short) (((f.data[2] & 0xFF) << 8) | (f.data[3] & 0xFF));
		assertEquals((short) (off * 100), got);
	}

	@Test
	public void test_updateWeatherData_scaling() {
		byte[] frame = ProtocolHelper.updateWeatherData(20.5, 60.0, 270, 3.4, 2.8);
		Frame f = parseFrame(frame);
		assertEquals(ProtocolConstants.DISCRETE_COMMAND, f.cmdSet);
		assertEquals(1 + 10, f.dataLenField);
		assertEquals(ProtocolConstants.UPDATE_WEATHER_STATION_DATA, f.data[1]);
		short temp = (short) (((f.data[2] & 0xFF) << 8) | (f.data[3] & 0xFF));
		int hum = ((f.data[4] & 0xFF) << 8) | (f.data[5] & 0xFF);
		int dir = ((f.data[6] & 0xFF) << 8) | (f.data[7] & 0xFF);
		int ws = ((f.data[8] & 0xFF) << 8) | (f.data[9] & 0xFF);
		int wsa = ((f.data[10] & 0xFF) << 8) | (f.data[11] & 0xFF);
		assertEquals((short) (20.5 * 10), temp);
		assertEquals((int) (60.0 * 10), hum);
		assertEquals(270, dir);
		assertEquals((int) (3.4 * 10), ws);
		assertEquals((int) (2.8 * 10), wsa);
	}

	@Test
	public void test_setObstaclePoints_layout() {
		GeoData g1 = new GeoData(22.5, 113.9, 12.34);
		GeoData g2 = new GeoData(22.6, 113.8, 56.78);
		byte[] frame = ProtocolHelper.setObstaclePoints(2, 1, java.util.Arrays.asList(g1, g2));
		Frame f = parseFrame(frame);
		assertEquals(ProtocolConstants.DISCRETE_COMMAND, f.cmdSet);
		assertEquals(ProtocolConstants.SET_OBSTACLE_POINTS, f.data[1]);
		// size and index
		assertEquals(2, f.data[2] & 0xFF);
		assertEquals(1, f.data[3] & 0xFF);
		// lat bytes are 8 bytes scaled by 1e10, check first point
		long latScaled = (long) (g1.getLat() * Math.pow(10, 10));
		long lngScaled = (long) (g1.getLng() * Math.pow(10, 10));
		int altScaled = (int) (g1.getAlt() * 10_000);
		byte[] latBytes = Arrays.copyOfRange(f.data, 4, 12);
		byte[] lngBytes = Arrays.copyOfRange(f.data, 12, 20);
		byte[] altBytes = Arrays.copyOfRange(f.data, 20, 24);
		assertEquals(latScaled, DataUtil.bytesToLong(latBytes));
		assertEquals(lngScaled, DataUtil.bytesToLong(lngBytes));
		assertEquals(altScaled, DataUtil.byte4ToInt(altBytes, 0));
	}
}


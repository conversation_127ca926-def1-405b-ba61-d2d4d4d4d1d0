package com.fj.fjprotocol.util;

import android.text.TextUtils;

/**
 * The code will be seen by another
 *
 * @author: qian.liu
 * @date: 2022/2/8
 * @description: GGA的解析类
 */
public class GGAUtil {
	public static String getDifferentialAge(byte[] ggaBytes) throws IllegalArgumentException {
		String gga = new String(ggaBytes);
		if (TextUtils.isEmpty(gga)) {
			//noinspection ConstantConditions
			throw new IllegalArgumentException(
				"gga String is null or empty: " + (gga == null ? "null" : "empty"));
		}
		String[] dataprocd = gga.split(",");
		if (dataprocd.length < 15) {
			throw new IllegalArgumentException("gga format is wrong: " + gga);
		}
		return dataprocd[13].replace(",", ".");
	}

	public static int getNumberOfSatellites(byte[] ggaBytes) {
		String gga = new String(ggaBytes);
		if (TextUtils.isEmpty(gga)) {
			//noinspection ConstantConditions
			throw new IllegalArgumentException(
				"gga String is null or empty: " + (gga == null ? "null" : "empty"));
		}
		String[] dataprocd = gga.split(",");
		if (dataprocd.length < 15) {
			throw new IllegalArgumentException("gga format is wrong: " + gga);
		}
		return Integer.parseInt(dataprocd[7]);
	}

	public static String getReferenceStationId(byte[] ggaBytes) {
		String gga = new String(ggaBytes);
		if (TextUtils.isEmpty(gga)) {
			//noinspection ConstantConditions
			throw new IllegalArgumentException(
				"gga String is null or empty: " + (gga == null ? "null" : "empty"));
		}
		String[] dataprocd = gga.split(",");
		if (dataprocd.length < 15) {
			throw new IllegalArgumentException("gga format is wrong: " + gga);
		}
		String[] tempData = dataprocd[14].split("\\*");
		return tempData[0];
	}

	// todo 合法性验证放在一个方法里
}

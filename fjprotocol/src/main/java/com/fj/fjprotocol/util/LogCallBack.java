package com.fj.fjprotocol.util;

public class LogCallBack {

	public static LogUtil logUtil;
	public static boolean RTK_DEBUG = false;
	public static boolean ECU_DEBUG = false;
	public static boolean RTCM_DEBUG = false;

	public interface LogUtil {
		void e(String tag, String msg);

		void d(String tag, String msg);

		void w(String tag, String msg);
	}

	public static void setLogCallBack(LogUtil logUtilCall) {
		logUtil = logUtilCall;
	}

	public static void setRTKDebug(boolean b) {
		RTK_DEBUG = b;
	}

	public static void setECUDebug(boolean b) {
		ECU_DEBUG = b;
	}

	public static void setRTCMDebug(boolean b) {
		RTCM_DEBUG = b;
	}
}

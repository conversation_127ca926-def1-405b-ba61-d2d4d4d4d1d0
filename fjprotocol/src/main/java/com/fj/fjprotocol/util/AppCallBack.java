package com.fj.fjprotocol.util;

import android.content.Context;

import com.fjdynamics.tractorprotocol.rtk.manager.RtkManager;

public class AppCallBack {

	private static boolean IS_RC_MODE = false;
	private static boolean IS_UPDATING = false;
	private static Context mAppContext;

	public static void setRcMode(boolean b) {
		IS_RC_MODE = b;
	}

	public static boolean getRcMode() {
		return IS_RC_MODE;
	}

	public static void setUpdateFlag(boolean b) {
		IS_UPDATING = b;
	}

	public static boolean getUpdateFlag() {
		return IS_UPDATING;
	}

	public static Context getAppContext() {
		return mAppContext;
	}

	public static void setAppContext(Context context) {
		mAppContext = context.getApplicationContext();
		RtkManager.getInstance().setNewRtk(true);
	}
}

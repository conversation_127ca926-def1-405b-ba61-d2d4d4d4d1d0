package com.fj.fjprotocol.ntrip.bean;

/**
 * 源信息 author:Jarven.ding create:2020/3/28
 */
public class NtripSource {
	public String strMountpoint;
	public String strIdentifier;
	public String strFormat;
	public String strFormatDetails;
	public String strCarrier;
	public String strNavSystem;
	public String strNetwork;
	public String strCountry;
	public String strLatitude;
	public String strLongitude;
	public String strSendNMEA;
	public String strSolution;
	public String strGeneraror;
	public String strCompression;
	public String strAuthertication;
	public String strFee;
	public String strBitrate;
	public String strMisc;
	public double distance = Double.MAX_VALUE;
}

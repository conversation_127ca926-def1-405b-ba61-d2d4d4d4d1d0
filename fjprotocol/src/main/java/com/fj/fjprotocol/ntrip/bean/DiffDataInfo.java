package com.fj.fjprotocol.ntrip.bean;

/**
 * 差分信息 author:Jarven.ding create:2020/3/28
 */
public class DiffDataInfo {
	private AddressInfo addressInfo = new AddressInfo();
	private UserInfo userInfo;
	private String sourcePoint = "";

	public DiffDataInfo() {
	}

	public AddressInfo getAddressInfo() {
		return addressInfo;
	}

	@SuppressWarnings("unused")
	public void setAddressInfo(AddressInfo addressInfo) {
		this.addressInfo = addressInfo;
	}

	public UserInfo getUserInfo() {
		return userInfo;
	}

	public void setUserInfo(UserInfo userInfo) {
		this.userInfo = userInfo;
	}

	public String getSourcePoint() {
		return sourcePoint;
	}

	public void setSourcePoint(String sourcePoint) {
		this.sourcePoint = sourcePoint;
	}
}

package com.fj.fjprotocol.ntrip.bean;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 用户信息 author:Jarven.ding create:2020/4/2
 */
public class UserInfo implements Parcelable {

	private String username;
	private String password;

	public UserInfo(String username, String password) {
		this.username = username;
		this.password = password;
	}

	protected UserInfo(Parcel in) {
		username = in.readString();
		password = in.readString();
	}

	public static final Creator<UserInfo> CREATOR =
		new Creator<UserInfo>() {
			@Override
			public UserInfo createFromParcel(Parcel in) {
				return new UserInfo(in);
			}

			@Override
			public UserInfo[] newArray(int size) {
				return new UserInfo[size];
			}
		};

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	@Override
	public int describeContents() {
		return 0;
	}

	@Override
	public void writeToParcel(Parcel dest, int flags) {
		dest.writeString(username);
		dest.writeString(password);
	}
}

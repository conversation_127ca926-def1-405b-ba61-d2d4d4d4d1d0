package com.fj.fjprotocol.ntrip.bean;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * author:Jarven.ding create:2020/3/28
 */
public class AddressInfo implements Parcelable {
	private String ip = "";
	private int port = -1;

	AddressInfo() {
	}

	public AddressInfo(String ip, int port) {
		this.ip = ip;
		this.port = port;
	}

	protected AddressInfo(Parcel in) {
		ip = in.readString();
		port = in.readInt();
	}

	@Override
	public void writeToParcel(Parcel dest, int flags) {
		dest.writeString(ip);
		dest.writeInt(port);
	}

	@Override
	public int describeContents() {
		return 0;
	}

	public static final Creator<AddressInfo> CREATOR =
		new Creator<AddressInfo>() {
			@Override
			public AddressInfo createFromParcel(Parcel in) {
				return new AddressInfo(in);
			}

			@Override
			public AddressInfo[] newArray(int size) {
				return new AddressInfo[size];
			}
		};

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}
}

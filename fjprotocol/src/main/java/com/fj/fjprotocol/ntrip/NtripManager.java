package com.fj.fjprotocol.ntrip;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.SystemClock;
import android.text.TextUtils;

import com.fj.fjprotocol.ProtocolHelper;
import com.fj.fjprotocol.ntrip.bean.AddressInfo;
import com.fj.fjprotocol.ntrip.bean.DiffDataInfo;
import com.fj.fjprotocol.ntrip.bean.NtripSource;
import com.fj.fjprotocol.ntrip.bean.UserInfo;
import com.fj.fjprotocol.ntrip.protocol.CROSConnect;
import com.fj.fjprotocol.ntrip.protocol.CROSConnectCallback;
import com.fj.fjprotocol.ntrip.protocol.GetSourceThread;
import com.fj.fjprotocol.ntrip.util.GeoUtil;
import com.fj.fjprotocol.util.AppCallBack;
import com.fj.fjprotocol.util.LogCallBack;
import com.fjdynamics.tractorprotocol.rtk.manager.RtkManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Ntrip管理类 author:Jarven.ding create:2020/4/1
 */
public class NtripManager {

	private static NtripManager instance;
	private List<NtripSource> ntripRecordList = new ArrayList<>();
	private String sourcePoint = "";
	private NtripState ntripConnectState = NtripState.LINK_NULL;

	private NtripState ntripSourceState = NtripState.NO_SOURCE;
	private CROSConnect crosConnect;
	private AddressInfo addressInfo;
	private UserInfo userInfo;
	private Boolean autoLink = true;
	private List<NtripListener> ntripListeners = new ArrayList<>();
	private String gga;
	private static final String TAG = "NtripManager";
	private boolean hasRtcm = false;
	private Long lastRtcmChangedTime = 0L;
	private long lastRestartTime = 0L;
	private boolean needRestart = true;

	public static synchronized NtripManager getInstance() {
		if (instance == null) {
			instance = new NtripManager();
		}
		return instance;
	}

	public void getSource() {
		setNtripSourceState(NtripState.GETING_SOURCE);
		GetSourceThread getSourceThread =
			new GetSourceThread(addressInfo, getLinkSourceData(), ntripRecordList);
		getSourceThread.setGetSourceListener(
			new GetSourceThread.GetSourceListener() {
				@Override
				public void sourceGetSuccess() {
					LogCallBack.logUtil.e("Ntrip", "getSource Success");
					for (NtripListener ntripListener : new ArrayList<>(ntripListeners)) {
						ntripListener.onGetSource(ntripRecordList);
					}
					setNtripSourceState(NtripState.GET_SOURCE_SUCCESS);
				}

				@Override
				public void sourceGetFailed(int errorCode, String errorMessage) {
					LogCallBack.logUtil.e("Ntrip", "getSource Failed " + errorMessage);
					setNtripSourceState(NtripState.GET_SOURCE_FAILED);
				}
			});
		getSourceThread.start();
	}

	public void linkSource() {
		if (!isNetworkAvailable(AppCallBack.getAppContext())) {
			return;
		}
		if (userInfo == null
			|| TextUtils.isEmpty(sourcePoint)
			|| ntripConnectState == NtripState.LINKING_TO_NODE) {
			return;
		}
		LogCallBack.logUtil.d(TAG, "linkSource");
		if (crosConnect != null) {
			crosConnect.disConnect();
		}
		setNtripConnectState(NtripState.LINKING_TO_NODE);
		DiffDataInfo diffDataInfo = new DiffDataInfo();
		diffDataInfo.setAddressInfo(addressInfo);
		diffDataInfo.setUserInfo(userInfo);
		diffDataInfo.setSourcePoint(sourcePoint);
		crosConnect = new CROSConnect(diffDataInfo);
		crosConnect.connect();
		crosConnect.setCallback(
			new CROSConnectCallback() {
				@Override
				public void onConnected() {
				}

				@Override
				public void onDisconnected() {
					if (crosConnect != null) {
						if (!isNetworkAvailable(AppCallBack.getAppContext())) {
							if (waitForNetThread == null) {
								waitForNetThread =
									new Thread(
										new Runnable() {
											@Override
											public void run() {
												try {
													while (!isNetworkAvailable(
														AppCallBack
															.getAppContext())) {
														LogCallBack.logUtil.d(
															"Ntrip", "网络不通,等一秒");
														SystemClock.sleep(1000);
													}
													crosConnect.connect();
												} catch (Exception e) {
													e.printStackTrace();
												}
											}
										});
								waitForNetThread.start();
							}
						} else {
							if (ntripConnectState != NtripState.LINK_TO_NODE_SUCCESS) {
								crosConnect.connect();
							}
						}
					}
				}

				@Override
				public void onError(String message) {
				}
			});
		LogCallBack.logUtil.d(TAG, "crosConnect.connect()");
	}

	Thread waitForNetThread;

	public static boolean isNetworkAvailable(Context context) {
		if (context == null) {
			return false;
		}
		boolean isConnect = false;
		try {
			ConnectivityManager connectivityManager =
				(ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
			if (connectivityManager != null) {
				// 获取网络连接管理的对象
				NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
				if (networkInfo != null && networkInfo.isConnected()) {
					if (networkInfo.getState() == NetworkInfo.State.CONNECTED) {
						isConnect = true;
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return isConnect;
	}

	public synchronized void sortSourceBySite(double lat, double lon) {
		for (NtripSource ntripSource : ntripRecordList) {
			try {
				double nlat = Double.parseDouble(ntripSource.strLatitude.replace(",", "."));
				double nlon = Double.parseDouble(ntripSource.strLongitude.replace(",", "."));
				ntripSource.distance = GeoUtil.getDistance(lat, lon, nlat, nlon);
			} catch (Exception ignore) {
			}
		}
		Collections.sort(
			ntripRecordList,
			new Comparator<NtripSource>() {
				@Override
				public int compare(NtripSource o1, NtripSource o2) {
					return (int) (o1.distance - o2.distance);
				}
			});
	}

	private void onSourceStateChange() {
		for (NtripListener ntripListener : new ArrayList<>(ntripListeners)) {
			ntripListener.onStateChange(ntripSourceState);
		}
	}

	private void onConnectStateChange() {
		for (NtripListener ntripListener : new ArrayList<>(ntripListeners)) {
			ntripListener.onStateChange(ntripConnectState);
		}
	}

	private String getLinkSourceData() {
		return "GET / HTTP/1.0\r\n"
			+ "User-Agent: NTRIP NtripServerCMD/"
			+ 1.0
			+ "\r\n"
			+ "Accept: */*\r\n"
			+ "Connection: close\r\n"
			+ "Authorization: Basic Og==\r\n\r\n";
	}

	public AddressInfo getAddressInfo() {
		return addressInfo;
	}

	public void setAddressInfo(AddressInfo addressInfo) {
		this.addressInfo = addressInfo;
	}

	public UserInfo getUserInfo() {
		return userInfo;
	}

	public void setUserInfo(UserInfo userInfo) {
		this.userInfo = userInfo;
	}

	public void sendGGA() {
		if (ntripConnectState == NtripState.LINK_TO_NODE_SUCCESS
			&& crosConnect != null
			&& !TextUtils.isEmpty(gga)) {
			if (LogCallBack.RTCM_DEBUG) {
				LogCallBack.logUtil.d(TAG, "sendGga:" + gga);
			}
			crosConnect.sendData(gga.getBytes());
		}
	}

	public void setGGA(String gga) {
		this.gga = gga;
	}

	public synchronized void setDiff(final byte[] bt) {
		hasRtcm = true;
		try {
			lastRtcmChangedTime = System.currentTimeMillis();
			if (RtkManager.getInstance().isNewRtk()) {
				RtkManager.getInstance().sendData(bt);
				return;
			}
			int index = 0;
			while (index + 512 < bt.length) {
				byte[] sendBytes = new byte[512];
				System.arraycopy(bt, index, sendBytes, 0, sendBytes.length);
				//            LogCallBack.logUtil.e("ttt", "flip: " + DataUtil.byte2hex(sendBytes));
				//
				// TransManager.getInstance().sendRtkData(ProtocolHelper.reqQXData(sendBytes));
				RtkManager.getInstance().sendData(ProtocolHelper.reqQXData(sendBytes)); // 简单修改
				index += 512;
			}

			if (index < bt.length) {
				byte[] sendBytes = new byte[bt.length - index];
				System.arraycopy(bt, index, sendBytes, 0, sendBytes.length);
				//            LogCallBack.logUtil.e("ttt", "flip: " + DataUtil.byte2hex(sendBytes));
				//
				// TransManager.getInstance().sendRtkData(ProtocolHelper.reqQXData(sendBytes));
				RtkManager.getInstance().sendData(ProtocolHelper.reqQXData(sendBytes)); // 简单修改
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean isHasRtcm() {
		return hasRtcm;
	}

	public void setHasRtcm(boolean hasRtcm) {
		this.hasRtcm = hasRtcm;
	}

	public void stop() {
		if (crosConnect != null) {
			LogCallBack.logUtil.d(TAG, "停止Ntrip");
			crosConnect.disConnect();
			crosConnect = null;
		}
		hasRtcm = false;
		setNtripConnectState(NtripState.DISABLE);
		setNtripSourceState(NtripState.DISABLE);
	}

	public void start() {
		boolean autoLink = NtripManager.getInstance().getAutoLink();
		NtripState connectState = NtripManager.getInstance().getNtripConnectState();

		// 如果10秒内未收到RTCM，则认为无RTCM
		if (System.currentTimeMillis() - lastRtcmChangedTime > 10_000) {
			hasRtcm = false;
		}
		// 如果超过30s，那么做重连操作，单次重连10s间隔
		if (System.currentTimeMillis() - lastRtcmChangedTime > 30_000
			&& System.currentTimeMillis() - lastRestartTime > 10_000) {
			lastRestartTime = System.currentTimeMillis();
			restartNtrip();
			return;
		}

		if (connectState == NtripState.LINK_TO_NODE_SUCCESS) {
			NtripManager.getInstance().sendGGA();
		} else if (autoLink) {
			NtripState sourceState = NtripManager.getInstance().getNtripSourceState();
			switch (sourceState) {
				case NO_SOURCE:
					String sourcePoint = NtripManager.getInstance().getSourcePoint();
					AddressInfo addressInfo = NtripManager.getInstance().getAddressInfo();
					if (sourcePoint != null && !sourcePoint.isEmpty() && addressInfo != null) {
						NtripManager.getInstance().getSource();
					}
					break;
				case GET_SOURCE_SUCCESS:
					UserInfo userInfo = NtripManager.getInstance().getUserInfo();
					if (userInfo != null) {
						NtripManager.getInstance().setUserInfo(userInfo);
						NtripManager.getInstance().linkSource();
					}
					break;
				case DISABLE:
					if (NtripManager.getInstance().getUserInfo() != null
						&& NtripManager.getInstance().getSourcePoint() != null
						&& NtripManager.getInstance().getAddressInfo() != null) {
						NtripManager.getInstance().linkSource();
					}
					break;
				default:
					break;
			}
		}
	}

	// 用于重连时的Listener
	private NtripListener ntripListener =
		new NtripListener() {
			@Override
			public void onGetSource(List<NtripSource> ntripSources) {
				String defaultSourcePoint = ntripSources.get(0).strMountpoint;
				setSourcePoint(defaultSourcePoint);
				linkSource();
				ntripListeners.remove(ntripListener);
			}

			@Override
			public void onStateChange(NtripState ntripState) {
			}
		};

	public void restartNtrip() {
		try {
			if (!needRestart) {
				return;
			}
			LogCallBack.logUtil.d(TAG, "restart ntrip");
			stop();
			if (TextUtils.isEmpty(sourcePoint)) {
				addNtripListener(ntripListener);
				getSource();
			} else {
				linkSource();
			}
		} catch (Exception e) {
			LogCallBack.logUtil.d(TAG, "restart ntrip error:" + e.getMessage());
		}
	}

	public NtripState getNtripConnectState() {
		return ntripConnectState;
	}

	public void setNtripConnectState(NtripState ntripConnectState) {
		this.ntripConnectState = ntripConnectState;
		onConnectStateChange();
	}

	public NtripState getNtripSourceState() {
		return ntripSourceState;
	}

	public void setNtripSourceState(NtripState ntripSourceState) {
		this.ntripSourceState = ntripSourceState;
		onSourceStateChange();
	}

	public String getSourcePoint() {
		return sourcePoint;
	}

	public void setSourcePoint(String sourcePoint) {
		this.sourcePoint = sourcePoint;
	}

	public interface NtripListener {

		void onGetSource(List<NtripSource> ntripSources);

		void onStateChange(NtripState ntripState);
	}

	public void addNtripListener(NtripListener ntripListener) {
		if (!this.ntripListeners.contains(ntripListener)) {
			this.ntripListeners.add(ntripListener);
		}
	}

	public void removeNtripListener(NtripListener ntripListener) {
		this.ntripListeners.remove(ntripListener);
	}

	public Boolean getAutoLink() {
		return autoLink;
	}

	public void setAutoLink(Boolean autoLink) {
		this.autoLink = autoLink;
	}

	public boolean isNeedRestart() {
		return needRestart;
	}

	public void setNeedRestart(boolean needRestart) {
		this.needRestart = needRestart;
	}

	public enum NtripState {
		DISABLE,
		NO_SOURCE,
		GETING_SOURCE,
		GET_SOURCE_SUCCESS,
		GET_SOURCE_FAILED,
		LINK_NULL,
		LINKING_TO_NODE,
		LINK_TO_NODE_SUCCESS,
		LINK_TO_NODE_FAILED
	}
}

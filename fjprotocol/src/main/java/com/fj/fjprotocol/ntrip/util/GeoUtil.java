package com.fj.fjprotocol.ntrip.util;

/**
 * author:Jarven.ding create:2020/3/28
 */
public class GeoUtil {

	/**
	 * @param degree 度
	 * @return ddmm.mmmm
	 */
	public static double degreeToDdMmmm(double degree) {
		int sign = 1;
		if (degree < 0) {
			sign = -1;
		}
		double degre = Math.abs(degree);
		double dd = Math.floor(degre);
		double mDotm = (degre - dd) * 60;
		return sign * (dd * 100 + mDotm);
	}

	private static double EARTH_RADIUS = 6371.393;

	private static double rad(double d) {
		return d * Math.PI / 180.0;
	}

	/**
	 * 计算两个经纬度之间的距离
	 *
	 * @param lat1
	 * @param lng1
	 * @param lat2
	 * @param lng2
	 * @return
	 */
	public static double getDistance(double lat1, double lng1, double lat2, double lng2) {
		double radLat1 = rad(lat1);
		double radLat2 = rad(lat2);
		double a = radLat1 - radLat2;
		double b = rad(lng1) - rad(lng2);
		double s =
			2
				* Math.asin(
				Math.sqrt(
					Math.abs(
						Math.pow(Math.sin(a / 2), 2)
							+ Math.cos(radLat1)
							* Math.cos(radLat2)
							* Math.pow(Math.sin(b / 2), 2))));
		s = s * EARTH_RADIUS;
		s = Math.round(s * 1000);
		return s;
	}
}

package com.fj.fjprotocol.ntrip.protocol;

import android.util.Base64;

import com.fj.fjprotocol.ntrip.bean.DiffDataInfo;
import com.fj.fjprotocol.ntrip.util.UtilByte;

/**
 * Cros链接 author:Jarven.ding create:2020/3/28
 */
public class CROSConnect extends DiffTcpOperate {

	public CROSConnect(DiffDataInfo diffDataInfo) {
		super(diffDataInfo);
	}

	@Override
	public boolean connect() {
		sendData(getLoginData());
		return super.connect();
	}

	private byte[] getLoginData() {
		StringBuilder command = new StringBuilder();
		command.append("GET /");
		command.append(diffDataInfo.getSourcePoint());
		command.append(" HTTP/1.0\r\n")
			.append("User-Agent: NTRIP GNSSInternetRadio/")
			.append("Accept: */*\r\n")
			.append("Connection: Keep-Alive\r\n")
			.append("Authorization: Basic ");

		String usrpwd =
			diffDataInfo.getUserInfo().getUsername()
				+ ":"
				+ diffDataInfo.getUserInfo().getPassword();
		String encode = UtilByte.getString_UTF8(Base64.encode(usrpwd.getBytes(), Base64.DEFAULT));
		encode = encode.replace("\n", "");
		command.append(encode);
		command.append("\r\n\r\n");
		return command.toString().getBytes();
	}

	@Override
	public void disConnect() {
		super.disConnect();
	}
}

package com.fj.fjprotocol.ntrip.protocol;

import com.fj.fjprotocol.ntrip.bean.AddressInfo;
import com.fj.fjprotocol.ntrip.bean.DiffDataInfo;

import java.util.Vector;

/**
 * 差分操作 author:Jarven.ding create:2020/3/28
 */
public abstract class DiffOperate {

	DiffDataInfo diffDataInfo;
	AddressInfo addressInfo;
	final Vector<byte[]> datas = new Vector<>();

	protected CROSConnectCallback mCallback;

	boolean isRead = false;
	boolean isWrite = false;

	DiffOperate(DiffDataInfo diffDataInfo) {
		this.diffDataInfo = diffDataInfo;
	}

	public void sendData(byte[] data) {
		synchronized (datas) {
			if (datas.size() > 0) {
				datas.clear();
			}
			datas.add(data);
		}
	}

	public CROSConnectCallback getCallback() {
		return mCallback;
	}

	public void setCallback(CROSConnectCallback mCallback) {
		this.mCallback = mCallback;
	}
}

package com.fj.fjprotocol.ntrip.protocol;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;

import com.fj.fjprotocol.BuildConfig;
import com.fj.fjprotocol.ntrip.NtripManager;
import com.fj.fjprotocol.ntrip.bean.DiffDataInfo;
import com.fj.fjprotocol.ntrip.util.UtilByte;
import com.fj.fjprotocol.util.LogCallBack;
import com.fjd.app.common.util.DataUtil;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketAddress;
import java.nio.charset.StandardCharsets;

/**
 * 差分操作 author:Jarven.ding create:2020/3/28
 */
public class DiffTcpOperate extends DiffOperate {

	private Socket socket = null;
	private DataInputStream mIn;
	private DataOutputStream mOut;

	private ThreadWrite mWriteThread;
	private ThreadRead mReadThread;
	ThreadLoginServer threadLoginServer;
	private Handler mHandler;

	private int status = DISCONNECTED;

	private static final int DISCONNECTED = 1;
	private static final int CONNECTING = 2;
	private static final int CONNECTED = 3;

	DiffTcpOperate(DiffDataInfo diffDataInfo) {
		super(diffDataInfo);

		mHandler =
			new Handler(Looper.getMainLooper()) {
				@Override
				public synchronized void handleMessage(Message msg) {
					super.handleMessage(msg);
					switch (msg.what) {
						case 10086:
							if (status != DISCONNECTED) {
								mHandler.removeCallbacksAndMessages(null);
								closeNet();
								mHandler.postDelayed(
									new Runnable() {
										@Override
										public void run() {
											mCallback.onDisconnected();
										}
									},
									3000);

							} else {
								LogCallBack.logUtil.d("Ntrip", "pass");
							}
							break;
						default:
							break;
					}
				}
			};
	}

	public boolean connect() {
		addressInfo = diffDataInfo.getAddressInfo();
		LogCallBack.logUtil.d("Ntrip", "connecting to " + addressInfo.getIp() + ":" + addressInfo.getPort());
		closeNet();
		status = CONNECTING;
		// Start Login Thread
		threadLoginServer = new ThreadLoginServer();
		threadLoginServer.start();
		return true;
	}

	public void disConnect() {
		LogCallBack.logUtil.d("Ntrip", "disconnect");
		closeNet();
	}

	class ThreadLoginServer extends Thread {
		@Override
		public void run() {
			super.run();
			Thread.currentThread().setName("DiffTcpOperateThreadLoginServer");
			try {
				socket = new Socket();
				SocketAddress socAddress =
					new InetSocketAddress(addressInfo.getIp(), addressInfo.getPort());
				if (socket == null) {
					LogCallBack.logUtil.d("Ntrip", "socket null");
					//                    closeNet();
					return;
				}
				socket.connect(socAddress, 5000);
				mIn = new DataInputStream(socket.getInputStream());
				mOut = new DataOutputStream(socket.getOutputStream());
				mCallback.onConnected();
				status = CONNECTED;
			} catch (Exception e) {
				e.printStackTrace();
				saveErrorLog(e);
				//                mCallback.onDisconnected();
				LogCallBack.logUtil.d("Ntrip", "socket error");
				//                closeNet();
				mHandler.sendEmptyMessage(10086);
				NtripManager.getInstance()
					.setNtripConnectState(NtripManager.NtripState.LINK_TO_NODE_FAILED);
				return;
			}
			startReadWrite();
		}
	}

	private void closeNet() {
		status = DISCONNECTED;
		LogCallBack.logUtil.e("Ntrip", "close net");
		try {
			stopReadWrite();
			if (this.socket != null) {
				this.socket.close();
				this.socket = null;
			}
			if (mOut != null) {
				mOut.close();
				mOut = null;
			}
			if (mIn != null) {
				mIn.close();
				mIn = null;
			}
		} catch (IOException e) {
			saveErrorLog(e);
			e.printStackTrace();
		}
	}

	private void startReadWrite() {
		try {
			isRead = true;
			isWrite = true;
			mReadThread = new ThreadRead();
			mReadThread.start();
			mWriteThread = new ThreadWrite();
			mWriteThread.start();
		} catch (Exception e) {
			saveErrorLog(e);
			//            mCallback.onDisconnected();
			e.printStackTrace();
		}
	}

	private void stopReadWrite() {
		isRead = false;
		isWrite = false;
	}

	class ThreadRead extends Thread {
		byte[] bufferBytes = new byte[4096];

		@Override
		public void run() {
			while (isRead) {
				Thread.currentThread().setName("DiffTcpOperateThreadRead");
				try {
					if (socket != null
						&& !socket.isClosed()
						&& socket.isConnected()
						&& !socket.isInputShutdown()) {
						int len = mIn.read(bufferBytes);
						if (len > 0) {
							byte[] bts = UtilByte.get(bufferBytes, 0, len);
							postData(bts);
							SystemClock.sleep(50);
						} else {
							//                            closeNet();
							//                            mCallback.onDisconnected();
							//
							// NtripManager.getInstance().setNtripConnectState(NtripManager.NtripState.LINK_TO_NODE_FAILED);
						}
					} else {
						if (socket != null) {
							boolean closed = socket.isClosed();
							boolean connected = socket.isConnected();
							boolean inputShutdown = socket.isInputShutdown();
							boolean outputShutdown = socket.isOutputShutdown();
							LogCallBack.logUtil.e(
								"Ntrip",
								"socket status: socket closed: "
									+ closed
									+ ", connected: "
									+ connected
									+ ", input shutdown:"
									+ inputShutdown
									+ ", output shutdown: "
									+ outputShutdown);
						} else {
							LogCallBack.logUtil.e("Ntrip", "socket is null");
						}
						LogCallBack.logUtil.d("Ntrip", "read < 0");
						//                        closeNet();
						//                        mCallback.onDisconnected();
						mHandler.sendEmptyMessage(10086);
						NtripManager.getInstance()
							.setNtripConnectState(NtripManager.NtripState.LINK_TO_NODE_FAILED);
					}
				} catch (Exception e) {
					e.printStackTrace();
					saveErrorLog(e);
					LogCallBack.logUtil.d("Ntrip", "read error");
					mHandler.sendEmptyMessage(10086);
					//                    closeNet();
					//                    mCallback.onDisconnected();
					NtripManager.getInstance()
						.setNtripConnectState(NtripManager.NtripState.LINK_TO_NODE_FAILED);
				}
			}
		}

		private void postData(byte[] bt) {
			NtripManager.getInstance().setHasRtcm(false);
			String rec = new String(bt);
			//            Log.i("Ntrip", rec.length() + "");
			String NC_ICY_200_OK = "ICY 200 OK";
			String NC_401_UNAUTHORIZED = "401 Unauthorized";
			if (rec.trim().contains(NC_ICY_200_OK)) {
				LogCallBack.logUtil.e("Ntrip", "NC_ICY_200_OK");
				NtripManager.getInstance()
					.setNtripConnectState(NtripManager.NtripState.LINK_TO_NODE_SUCCESS);
			} else if (rec.trim().contains(NC_401_UNAUTHORIZED)) {
				LogCallBack.logUtil.e("Ntrip", "NC_401_UNAUTHORIZED");
				//                mHandler.sendEmptyMessage(10086);
				if (mCallback != null) {
					mCallback.onError("未授权账号");
				}
				closeNet();
				NtripManager.getInstance()
					.setNtripConnectState(NtripManager.NtripState.LINK_TO_NODE_FAILED);
			} else if (rec.trim().contains("HTTP")) {
				LogCallBack.logUtil.d("Ntrip", rec);
				NtripManager.getInstance()
					.setNtripConnectState(NtripManager.NtripState.LINK_TO_NODE_FAILED);
			} else {
				if (BuildConfig.DEBUG) {
					LogCallBack.logUtil.d("Ntrip", "RTCM流:" + DataUtil.byte2hex(bt));
				}
				LogCallBack.logUtil.d("Ntrip", "receive rtcm length: " + bt.length);
				NtripManager.getInstance().setDiff(bt);
			}
		}
	}

	private void saveErrorLog(Exception ex) {
		try {
			LogCallBack.logUtil.e("Ntrip", "----------- error ---------------" + "\r\n");
			LogCallBack.logUtil.e("Ntrip", ex.getMessage() + "\r\n");
			for (int i = 0; i < ex.getStackTrace().length; i++) {
				LogCallBack.logUtil.e("Ntrip", "****StackTrace" + i + "\r\n");
				LogCallBack.logUtil.e(
					"Ntrip", "行数：" + ex.getStackTrace()[i].getLineNumber() + "\r\n");
				LogCallBack.logUtil.e(
					"Ntrip", "类名：" + ex.getStackTrace()[i].getClassName() + "\r\n");
				LogCallBack.logUtil.e(
					"Ntrip", "文件：" + ex.getStackTrace()[i].getFileName() + "\r\n");
				LogCallBack.logUtil.e(
					"Ntrip", "方法：" + ex.getStackTrace()[i].getMethodName() + "\r\n\r\n");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	class ThreadWrite extends Thread {
		@Override
		public void run() {
			Thread.currentThread().setName("DiffTcpOperateThreadWrite");
			while (isWrite) {
				if (socket == null || mOut == null) {
					return;
				}
				if (datas.size() <= 0) {
					try {
						SystemClock.sleep(100);
						if (!isWrite) {
							return;
						}
					} catch (Exception e) {
						e.printStackTrace();
						saveErrorLog(e);
						continue;
					}
					continue;
				}
				byte[] buffer = null;
				synchronized (datas) {
					if (datas.size() > 0) {
						buffer = datas.remove(0); // get a data
						if (buffer == null || buffer.length < 1) {
							continue;
						}
					} else {
						continue;
					}
				}
				if (isWrite) {
					writes(buffer);
				} else {
					return;
				}
				try {
					sleep(1000);
					if (!isWrite) {
						return;
					}
				} catch (InterruptedException e) {
					saveErrorLog(e);
					e.printStackTrace();
				}
			}
		}

		private int writeFailTimes = 0;

		void writes(byte[] data) {
			if (mOut != null) {
				try {
					LogCallBack.logUtil.d(
						"Ntrip", "write: " + new String(data, StandardCharsets.UTF_8));
					mOut.write(data);
					mOut.flush();
				} catch (Exception e) {
					e.printStackTrace();
					saveErrorLog(e);
					writeFailTimes++;
					if (writeFailTimes > 5) {
						NtripManager.getInstance().stop();
						if (mCallback != null) {
							mCallback.onError("服务器异常");
						}
					}
					//                    mHandler.sendEmptyMessage(10086);
					//                    mCallback.onDisconnected();
				}
			}
		}
	}
}

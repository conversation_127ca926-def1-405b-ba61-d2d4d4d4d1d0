package com.fj.fjprotocol;

import android.util.Log;

import com.fj.fjprotocol.util.LogCallBack;
import com.fj.lib.log.LogCallback;
import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.RtkLogger;
import com.fjdynamics.protocollibrary.CmdManager;
import com.fjdynamics.protocollibrary.transport.TransPort;
import com.fjdynamics.tractorprotocol.common.config.ProtocolLibConfig;
import com.fjdynamics.tractorprotocol.common.config.ProtocolLibManager;
import com.fjdynamics.tractorprotocol.rtk.manager.RtkManager;

/**
 * 数据传输管理类
 *
 * <AUTHOR>
 */
public class TransManager {

	private static final String TAG = "TransManager";

	/**
	 * uart1 ecu串口号
	 */
	private static final String ECU_SERIAL_PATH1 = "/dev/ttyS3";

	/**
	 * uart6 ecu串口号
	 */
	private static final String ECU_SERIAL_PATH2 = "/dev/ttyS2";

	/**
	 * uart1 ecu串口波特率
	 */
	private static final int ECU_BAUD_RATE_230400 = 230400;

	/**
	 * uart6 ecu串口波特率
	 */
	private static final int ECU_BAUD_RATE_115200 = 115200;

	private static final Object UART_1_LOCK = new Object();
	private static final Object UART_6_LOCK = new Object();

	public static int sReqValue = 0;

	/**
	 * uart1 ecu串口
	 */
	private TransPort ecuTransport1;

	/**
	 * uart6 ecu串口
	 */
	private TransPort ecuTransport2;

	private boolean isUpgrading = false;

	private TransManager() {
	}

	private static class SingletonHolder {
		private static final TransManager INSTANCE = new TransManager();
	}

	/**
	 * 获取单例
	 *
	 * @return TransManager
	 */
	public static TransManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	/**
	 * TransManager初始化
	 */
	public void init(EcuDataCallback ecuDataCallback) {
		// 初始化ecu连接
		ecuTransport1 =
			CmdManager.getInstance()
				.startSeirListen(
					ECU_SERIAL_PATH1,
					ECU_BAUD_RATE_230400,
					new TowerProtocolFactory(ecuDataCallback, "uart1"));
		ecuTransport2 =
			CmdManager.getInstance()
				.startSeirListen(
					ECU_SERIAL_PATH2,
					ECU_BAUD_RATE_115200,
					new TowerProtocolFactory(ecuDataCallback, "uart6"));

		// 初始化rtk连接
		ProtocolLibManager.getInstance()
			.setProtocolConfig(
				new ProtocolLibConfig.Builder(false)
					.setRtkLogCallback(
						new LogCallback() {
							@Override
							public void d(String s, String s1) {
								RtkLogger.d(s, s1);
							}

							@Override
							public void i(String s, String s1) {
								RtkLogger.i(s, s1);
							}

							@Override
							public void w(String s, String s1) {
								RtkLogger.w(s, s1);
							}

							@Override
							public void e(String s, String s1) {
								RtkLogger.e(s, s1);
							}

							@Override
							public void e(
								String s, String s1, Throwable throwable) {
								RtkLogger.e(s, s1);
							}
						})
					.build());
		initRtk();
	}

	private void initRtk() {
		LogCallBack.logUtil.d(TAG, "tcp rtk trans port init");
		RtkManager.getInstance().createConnection();
	}

	/**
	 * 使用uart1串口给ecu发数据
	 *
	 * @param data data
	 */
	public void sendEcuDataWithUart1(byte[] data) {
		synchronized (UART_1_LOCK) {
			if (isUpgrading) {
				return;
			}
			if (ecuTransport1 == null) {
				Log.e(TAG, "sendEcuDataWithUart1: ecuSerialTransfer is null");
				return;
			}
			Log.d(TAG, "TowerProtocol send uart1 ecu data: " + DataUtil.byte2hex(data));
			ecuTransport1.SendData(data);
		}
	}

	/**
	 * 使用uart6串口给ecu发数据
	 *
	 * @param data data
	 */
	public void sendEcuDataWithUart6(byte[] data) {
		synchronized (UART_6_LOCK) {
			if (isUpgrading) {
				return;
			}
			if (ecuTransport2 == null) {
				Log.e(TAG, "sendEcuDataWithUart6: ecuSerialTransfer is null");
				return;
			}
			Log.d(TAG, "TowerProtocol send uart6 ecu data: " + DataUtil.byte2hex(data));
			ecuTransport2.SendData(data);
		}
	}

	/**
	 * 发送ota相关数据
	 *
	 * @param data data
	 */
	public void sendOtaData(byte[] data) {
		synchronized (UART_1_LOCK) {
			if (ecuTransport1 == null) {
				Log.e(TAG, "sendOtaData: ecuSerialTransfer is null");
				return;
			}
			Log.d(TAG, "TowerProtocol send ota ecu data: " + DataUtil.byte2hex(data));
			ecuTransport1.SendData(data);
		}
	}

	/**
	 * 设置是否正在升级
	 *
	 * @param upgrading 是否正在升级
	 */
	public void setUpgrading(boolean upgrading) {
		isUpgrading = upgrading;
	}
}

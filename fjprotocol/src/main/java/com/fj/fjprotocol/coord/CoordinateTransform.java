package com.fj.fjprotocol.coord;

import com.fj.coordsystem.algorithm.bean.Ellipsoid;
import com.fj.coordsystem.algorithm.utils.ProjectionTypes;
import com.fj.coordsystem.serialize.projectionparams.IProjectionParamsAdapter;
import com.fj.coordsystem.serialize.projectionparams.ProjectionParamsInfoModel;
import com.fj.coordsystem.serialize.projectionparams.UITransverseMercatorProjection;
import com.fj.coordsystem.unionconvert.CoordCovertUtil;
import com.fj.coordsystem.unionconvert.CoordSysConfig;
import com.fjdynamics.app.logger.Logger;

public class CoordinateTransform {
	private static final String TAG = "CoordinateTransform";

	/**
	 * 东向偏移，单位:m
	 */
	private static final double EAST_OFFSET = 500000;
	/**
	 * 北向偏移，单位:m
	 */
	private static final double NORTH_OFFSET = 0;

	private CoordCovertUtil coordCovertUtil;
	private double centerLng = 1024;
	private double pY;
	private double pX;
	private double pZ;

	public static CoordinateTransform getInstance() {
		return Singleton.INSTANCE;
	}

	public CoordinateTransform() {
		setCenter(centerLng);
	}

	private static class Singleton {
		public static final CoordinateTransform INSTANCE = new CoordinateTransform();
	}

	public synchronized void setCenter(double lng) {
		coordCovertUtil = new CoordCovertUtil();
		centerLng = lng;
		CoordSysConfig coordSysConfig = new CoordSysConfig();
		ProjectionParamsInfoModel projectionParamsInfoModel = new ProjectionParamsInfoModel(ProjectionTypes.PROJECTION_TYPE_TRANSVERSE_MERCATOR);
		Logger.i(TAG, "setCenter lng:" + lng);
		//设置中央经线
		projectionParamsInfoModel.setCentralMeridian(lng);
		projectionParamsInfoModel.setEastAdditiveConstant(EAST_OFFSET);
		projectionParamsInfoModel.setNorthAdditiveConstant(NORTH_OFFSET);
		IProjectionParamsAdapter projection =
			new UITransverseMercatorProjection(projectionParamsInfoModel);
		coordSysConfig.targetEllp = Ellipsoid.WGS84;
		coordSysConfig.projectionStr = projection.toPipelineStr(false);
		coordSysConfig.invProjectionStr = projection.toPipelineStr(true);
		coordCovertUtil.setCoordSysConfig(coordSysConfig);
		coordCovertUtil.buildNewConvertObjs(TAG);
	}

	public synchronized double getCenter() {
		return centerLng;
	}

	private synchronized boolean isCenterUnavailable() {
		return centerLng == 1024;
	}

	public double[] transformBLHToGausXYH(double centerLng, double lat, double lng, double alt) {
		double[] doubles;
		if (isCenterUnavailable()) {
			setCenter(centerLng);
			doubles = coordCovertUtil.BLH2ENU(new double[]{lat, lng, alt});
			if (doubles != null && doubles.length >= 3) {
				// 获取第一个点的坐标为参考点
				pX = doubles[1];
				pY = doubles[0];
				pZ = doubles[2];
				Logger.i(TAG, "update ref point (" + pX + ", " + pY + ", " + pZ + ")");
			}
		}
		doubles = coordCovertUtil.BLH2ENU(new double[]{lat, lng, alt});
		if (doubles != null && doubles.length >= 3) {
			// 第一个坐标点点计算出来的坐标减去参考点即为(0,0,0)，原点
			// 后续坐标点计算出来的坐标减去参考点即为相对于原点的相对位置
			return new double[]{doubles[1] - pX, doubles[0] - pY, doubles[2] - pZ};
		}
		return new double[]{0, 0, 0};
	}

	public double[] transformGausXYHToBLH(double centerLng, double x, double y, double z) {
		if (isCenterUnavailable()) {
			setCenter(centerLng);
		}
		double aX = x + pX;
		double aY = y + pY;
		double aZ = z + pZ;
		//NEH转经纬度
		return coordCovertUtil.ENU2BLH(new double[]{aY, aX, aZ});
	}
}

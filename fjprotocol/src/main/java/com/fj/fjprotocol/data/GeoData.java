package com.fj.fjprotocol.data;

import androidx.annotation.NonNull;

/**
 * Geo数据类型
 *
 * <AUTHOR>
 */
public class GeoData {

	/**
	 * 纬度
	 */
	private double lat;
	/**
	 * 经度
	 */
	private double lng;
	/**
	 * 高程
	 */
	private double alt;
	/**
	 * 北向位置
	 */
	private double north;
	/**
	 * 东向位置
	 */
	private double east;

	public GeoData(double lat, double lng, double alt) {
		this.lat = lat;
		this.lng = lng;
		this.alt = alt;
	}

	public GeoData(double lat, double lng, double alt, double north, double east) {
		this.lat = lat;
		this.lng = lng;
		this.alt = alt;
		this.north = north;
		this.east = east;
	}

	public double getLng() {
		return lng;
	}

	public void setLng(double lng) {
		this.lng = lng;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public double getAlt() {
		return alt;
	}

	public void setAlt(double alt) {
		this.alt = alt;
	}

	public double getNorth() {
		return north;
	}

	public void setNorth(double north) {
		this.north = north;
	}

	public double getEast() {
		return east;
	}

	public void setEast(double east) {
		this.east = east;
	}

	@NonNull
	@Override
	public String toString() {
		return "GeoData{" + "lat=" + lat + ", lng=" + lng + ", alt=" + alt + '}';
	}
}

package com.fj.fjprotocol;

import android.util.Log;

import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.EcuLogger;
import com.fjdynamics.protocollibrary.factory.BaseFactory;

import okio.Buffer;
import okio.ByteString;

/**
 * 塔吊ecu数据接收包封装工厂类
 *
 * <AUTHOR>
 */
public class TowerProtocolFactory extends BaseFactory<TowerProtocol> {

	private final String TAG;
	private static final int MAX_BUFFER_LENGTH = 1024;
	private static final int MIN_FRAME_LENGTH = 25;
	private final Buffer buffer = new Buffer();
	private final ByteString HEADER_BYTES;

	private final EcuDataCallback ecuDataCallback;

	private long lastTimestamp;

	public TowerProtocolFactory(EcuDataCallback ecuDataCallback, String tag) {
		setHeader(TowerProtocol.header);
		setFooter(TowerProtocol.footer);
		setCmdSize(1);
		setCmdDataSize(2);
		setCmdDataSizeIndex(19);
		this.ecuDataCallback = ecuDataCallback;
		TAG = tag;
		HEADER_BYTES = ByteString.of(getHeader());
	}

	@Override
	public void HandleData(byte[] data) {
		if (null == data || data.length == 0) {
			return;
		}
		buffer.write(data);
		processBuffer();
	}

	private void processBuffer() {
		try {
			while (buffer.size() >= MIN_FRAME_LENGTH) {
				long headIndex = buffer.indexOf(HEADER_BYTES);
				if (headIndex == -1) {
					if (buffer.size() > MAX_BUFFER_LENGTH) {
						Log.w(TAG, "buffer overflow, clearing...");
						buffer.clear();
					}
					break;
				}

				if (headIndex > 0) {
					buffer.skip(headIndex);
				}

				if (buffer.size() < MIN_FRAME_LENGTH) {
					Log.w(TAG, "data length too short");
					break;
				}

				byte b1 = buffer.getByte(19);
				byte b2 = buffer.getByte(20);
				int dataLength = ((b1 & 0xFF) << 8) | (b2 & 0xFF);
				int frameLength = 2 + 20 + dataLength + 2 + 1;

				if (buffer.size() < frameLength) {
					break;
				}

				if (buffer.getByte(frameLength - 2) == getFooter()[0]
					&& buffer.getByte(frameLength - 1) == getFooter()[1]) {
					byte[] frame = buffer.readByteArray(frameLength);
					this.GotCmdData(frame);
				} else {
					Log.w(TAG, "footer mismatch, skipping...");
					buffer.skip(HEADER_BYTES.size());
				}
			}
		} catch (Exception e) {
			Log.e(TAG, "processBuffer error: " + e.getMessage(), e);
		}
	}

	@Override
	public void GotCmdData(byte[] bytes) {
		// 对数据进行校验
		byte checksum = DataUtil.sumCheck(bytes, 2, bytes.length - 3);
		if (checksum != bytes[bytes.length - 3]) {
			Log.w(TAG, "TowerProtocol recv ecu data sum check failed");
			return;
		}
		if (bytes[21] != (byte) 0xa2) {
			//按键上报频率太快，只打印非按键上报的数据
			Log.d(TAG, "TowerProtocol recv ecu data: " + DataUtil.byte2hex(bytes));
		}
		TowerProtocol protocol = new TowerProtocol();
		int dataSize = ((bytes[19] & 0xFF) << 8) | (bytes[20] & 0xFF);
		protocol.setDataSize(dataSize);
		protocol.setCmd(bytes[21]);
//		if (protocol.isHighFreq()) {
//			long currentTimestamp = System.currentTimeMillis();
//			if (currentTimestamp - lastTimestamp < 995) {
//				return;
//			}
//			lastTimestamp = currentTimestamp;
//		}
		protocol.setNeedSaveLog((bytes[16] & 0x01) == 1);
		if (protocol.isNeedSaveLog()) {
			//ecu指定需要存储的日志
			EcuLogger.d(TAG, DataUtil.byte2hex(bytes));
		}

		if (dataSize > 0) {
			byte[] data = new byte[protocol.getDataSize()];
			System.arraycopy(bytes, 22, data, 0, protocol.getDataSize());
			protocol.setData(data);
		} else {
			protocol.setData(new byte[0]);
		}

		this.BroadData(protocol);
	}

	@Override
	public void BroadData(TowerProtocol towerProtocol) {
		if (ecuDataCallback != null) {
			ecuDataCallback.onTowerDataReceived(towerProtocol);
		}
	}

}

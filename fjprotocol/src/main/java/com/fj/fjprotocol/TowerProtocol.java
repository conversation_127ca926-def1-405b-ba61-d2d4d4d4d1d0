package com.fj.fjprotocol;

/**
 * 塔吊协议格式
 *
 * <AUTHOR>
 */
public class TowerProtocol {

	/**
	 * 帧头
	 */
	public static final byte[] header = {(byte) 0xeb, (byte) 0x90};

	/**
	 * 帧尾
	 */
	public static final byte[] footer = {(byte) 0x0d, (byte) 0x0a};

	/**
	 * 版本号
	 */
	private final byte[] version = {(byte) 0xff, (byte) 0xff, (byte) 0xff, (byte) 0xff};

	/**
	 * 源地址
	 */
	private final byte[] sourceAddress = {0x00, 0x00, 0x00, 0x00};

	/**
	 * 目标地址
	 */
	private final byte[] targetAddress = {0x00, 0x00, 0x00, 0x00};

	/**
	 * 序列号，初始化随机，单调递增回绕（各自为主体递增）
	 */
	private int seq;

	/**
	 * 是否需要回应（确认位），广播模式不用ACK回复
	 */
	private final byte[] shouldAck = {0x01};

	/**
	 * 确认序列号ACK,回复确认赋值与收到的Seq一致即可
	 */
	private final byte[] ackSeq = {0x00, 0x00};

	/**
	 * 数据体长度
	 */
	private int dataSize;

	/**
	 * 指令
	 */
	private byte cmd;

	/**
	 * 指令数据
	 */
	private byte[] data;

	private boolean needSaveLog;

	public int getDataSize() {
		return dataSize;
	}

	public void setDataSize(int dataSize) {
		this.dataSize = dataSize;
	}

	public byte getCmd() {
		return cmd;
	}

	public void setCmd(byte cmd) {
		this.cmd = cmd;
	}

	public byte[] getData() {
		return data;
	}

	public void setData(byte[] data) {
		this.data = data;
	}

	public boolean isNeedSaveLog() {
		return needSaveLog;
	}

	public void setNeedSaveLog(boolean needSaveLog) {
		this.needSaveLog = needSaveLog;
	}

	/**
	 * 是否是10hz高频日志
	 *
	 * @return true/false
	 */
	public boolean isHighFreq() {
		return cmd == ProtocolConstants.ECU_REALTIME_STATUS
			|| cmd == ProtocolConstants.ECU_LOG;
	}
}

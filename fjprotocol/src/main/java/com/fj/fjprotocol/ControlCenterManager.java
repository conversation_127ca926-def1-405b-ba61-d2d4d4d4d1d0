package com.fj.fjprotocol;

import android.os.Handler;
import android.os.HandlerThread;
import android.text.TextUtils;

import com.fj.fjprotocol.ntrip.NtripManager;
import com.fjdynamics.app.logger.RtkLogger;
import com.fjdynamics.tractorprotocol.common.manager.ProtocolDataManager;
import com.fjdynamics.tractorprotocol.common.transfer.GpggaStickPackageHelper;
import com.fjdynamics.tractorprotocol.rtk.protocol.GgaDataProtocol;

import java.text.DecimalFormat;

public class ControlCenterManager {

	private static final String TAG = "ControlCenterManager";
	private final DecimalFormat decimalFormat = new DecimalFormat("#.##########");

	private static class Singleton {
		private static final ControlCenterManager INSTANCE = new ControlCenterManager();
	}

	public static ControlCenterManager getInstance() {
		return Singleton.INSTANCE;
	}

	public static int sReqValue = 0;

	private final Handler mCmdHandler;
	public static int CONNECT_TYPE = 0; // 0: Android COM4  ,  1: Shell 2235
	public long lastRunningRevTime;
	private boolean hasGGA = false;

	public static final int CHANNEL_TYPE_QX = 1;
	public static final int CHANNEL_TYPE_CORS = 2;
	public static final int CHANNEL_TYPE_CUSTOMIZE = 3;
	/**
	 * 当前 rtk 是否处于 sbas 模式，如果是sbas模式，则需要把 gga 数据通过 eventbus 发送到相关界面进行显示
	 */
	private boolean isSbasMode;

	public boolean isChangingRtk() {
		return isChangingRtk;
	}

	private boolean isChangingRtk;

	public void setSbasMode(boolean sbasMode) {
		isSbasMode = sbasMode;
	}

	public void setChangingRtk(boolean changing) {
		isChangingRtk = changing;
	}

	/**
	 * RTK类型：外置蓝牙
	 */
	public static final int RTK_TYPE_EXT_BLUETOOTH = 2;
	/**
	 * RTK类型：蓝牙
	 */
	public static final int RTK_TYPE_BLUETOOTH = 3;

	/**
	 * RTK类型：电台
	 */
	public static final int RTK_TYPE_RADIO = 0;

	/**
	 * 发生RCM中时间的此时，最多发送3次——主要用于获取卫星的时间，获取成功后，就不再发送了，减少系统开支
	 */
	protected int sendRmcTimeMaxCount = 0;

	public boolean hasValidGGA = false;

	private ControlCenterManager() {
		HandlerThread mCmdHandlerThread = new HandlerThread("cmd-handler-thread");
		mCmdHandlerThread.start();
		mCmdHandler = new Handler(mCmdHandlerThread.getLooper());
		ProtocolDataManager.getInstance()
			.registerProtocolDataCallback(GgaDataProtocol.class, this::onReceiveGgaData);
	}

	private void onReceiveGgaData(GgaDataProtocol ggaDataProtocol) {
		try {
			String gga = new String(ggaDataProtocol.getGgaBytes());
			RtkLogger.d(TAG, "gga:" + gga);
			if (!GpggaStickPackageHelper.isNMEAData(gga, GpggaStickPackageHelper.GGA_PATTERN)
				|| gga.contains("RMC")) {
				return;
			}
			//            GpggaDecodeDelegate.getInstance().putData(gga);
			String[] ggas = gga.split(",");
			if (ggas.length > 10) {
				String lat = ggas[2];
				String lng = ggas[4];
				if (!"".equalsIgnoreCase(lat) && !"".equalsIgnoreCase(lng)) {
					double dlat = Double.parseDouble(lat) / 100.0;
					double dlng = Double.parseDouble(lng) / 100.0;
					//                    if (LogCallBack.RTK_DEBUG || LogCallBack.RTCM_DEBUG) {
					//                        LogCallBack.logUtil.d(TAG, "onReceiveCom4Data>>dlat："
					// + dlat + ",dlng：" + dlng);
					//                    }
					hasValidGGA = dlat != 0 || dlng != 0;
					setHasGGA(true);
					if (isChangingRtk) {
						RtkLogger.d(TAG, "In rtk changing, don't post gga data");
						return;
					}
					if (!isSbasMode && hasValidGGA) {
						//                        LogCallBack.logUtil.d(TAG, "From com4 GGA：" +
						// gga);
						//                        switch (mLanguageListener.getRtkType()) {
						//                            case RTK_TYPE_EXT_BLUETOOTH:
						//
						// ExtBtRtkManager.getInstance().updateGga(gga);
						//                                break;
						//                            case RTK_TYPE_BLUETOOTH:
						//                                BtRtkManager.getInstance().updateGga(gga);
						//                                break;
						//                            case RTK_TYPE_RADIO:
						//                                break;
						//                            default:
						startRtkServer(gga, true, 1);
						//                                break;
					}
				}
			} else {
				RtkLogger.e(TAG, "unexpect gga is: " + gga);
			}
		} catch (Exception e) {
			RtkLogger.e(TAG, "gga NumberFormatException：" + e.getMessage());
		}
	}

	/**
	 * 根据错误码，打印具体的错误信息 Bit 0 : rtk异常: 0正常，1错误 Bit 1: 航向超时: 0正常，1错误 Bit 2: 位置超时: 0正常，1错误 Bit 3: 速度超时:
	 * 0正常，1错误 Bit 4: 电调超时: 0正常，1错误 Bit 5:显控板心跳超时: 0正常，1错误 Bit 6:RTCM流超时: 0正常，1错误 Bit 7: 电机状态异常:
	 * 0正常，1错误 Bit 8：IMU更新超时：0正常，1错误 Bit 9：转角标定错误：0正常，1错误 Bit 10：转向电机位置环参数错误：0正常，1错误 Bit 11：航向对准错误:
	 * 0正常，1错误 Bit 12：AB点距离过近错误: 0正常，1错误 Bit 13：角度传感器测量超出标定范围：0正常，1错误 Bit 14：角度传感器超时错误：0正常 1错误 Bit
	 * 15：加密ID异常：0正常 1错误 Bit 16：482自检状态 0正常 1：异常 Bit 17：482定位状态 0：正常 1：异常 Bit 18：主天线是否断路 0：正常 1：异常
	 * Bit 19：主天线是否短路 0：正常 1：异常 Bit 20：从天线是否断路 0：正常 1：异常 Bit 21：从天线是否短路0：正常 1：异常 Bit 22~24：Uturn故障码
	 * Bit 25：锁车状态 0：解锁 1:锁
	 *
	 * @param errLowBytes 错误码
	 */
	private void printErrorCode(byte[] errLowBytes) {
		StringBuilder bits = new StringBuilder();
		for (byte b : errLowBytes) {
			bits.append(getBit(b));
		}
		String bitsString = bits.toString();
		StringBuilder sb = new StringBuilder("低位错误码解析结果 : ");
		for (int i = 0; i < bitsString.length(); i++) {
			// 因为文档中的高低位是小端的
			int showIndex = bitsString.length() - 1 - i;
			if (showIndex < 22 || showIndex > 24) {
				if (Integer.parseInt(bitsString.substring(showIndex, showIndex + 1)) == 1) {
					String errorMsg = getErrorMsgFromIndex(showIndex);
					if (!TextUtils.isEmpty(errorMsg)) {
						sb.append(errorMsg).append(" + ");
					}
				}
			}
		}
		String uturnErrorCode = bitsString.substring(7, 10);

		String uturnErrorMsg = getUturnErrorMsg(uturnErrorCode);
		if (!TextUtils.isEmpty(uturnErrorMsg)) {
			sb.append(uturnErrorMsg);
		}
		if (!sb.toString().endsWith("低位错误码解析结果 : ")) {
			RtkLogger.d(TAG, sb.toString());
		}
	}

	private String getUturnErrorMsg(String uturnErrorCode) {
		int errorValue = Integer.parseInt(uturnErrorCode, 2);
		switch (errorValue) {
			case 1:
				return "未定义故障";
			case 2:
				return "新曲线启动距离过远";
			case 3:
				return "新曲线启动点位不足";
			case 4:
				return "点阵无效";
			case 5:
				return "点阵间距异常";
			case 6:
				return "点阵平滑异常";
			case 7:
				return "点阵缺点";
		}
		return null;
	}

	private String getBit(byte by) {
		return String.valueOf((by >> 7) & 0x1)
			+ ((by >> 6) & 0x1)
			+ ((by >> 5) & 0x1)
			+ ((by >> 4) & 0x1)
			+ ((by >> 3) & 0x1)
			+ ((by >> 2) & 0x1)
			+ ((by >> 1) & 0x1)
			+ ((by) & 0x1);
	}

	private String getErrorMsgFromIndex(int index) {

		switch (index) {
			case 0:
				return "rtk异常";
			case 1:
				return "航向超时";
			case 2:
				return "位置超时";
			case 3:
				return "速度超时";
			case 4:
				return "电调超时";
			case 5:
				return "显控板心跳超时";
			case 6:
				return "RTCM流超时";
			case 7:
				return "电机状态异常";
			case 8:
				return "IMU更新超时";
			case 9:
				return "转角标定错误";
			case 10:
				return "转向电机位置环参数错误";
			case 11:
				return "航向对准错误";
			case 12:
				return "AB点距离过近错误";
			case 13:
				return "角度传感器测量超出标定范围";
			case 14:
				return "角度传感器超时错误";
			case 15:
				return "加密ID异常";
			case 16:
				return "482自检状态";
			case 17:
				return "482定位状态";
			case 18:
				return "主天线是否断路";
			case 19:
				return "主天线是否短路";
			case 20:
				return "从天线是否断路";
			case 21:
				return "从天线是否短路";
			case 25:
				return "锁车状态1";
		}
		return null;
	}

	public void onEcuReceived(byte[] validateFrameData, int size) {
		try {
			if (validateFrameData[21] != -89
				&& ((validateFrameData[21] & 0xff) != 0xa1)
				&& validateFrameData[21] != -71) {
				//                LogCallBack.logUtil.d(TAG, "bytes size: " + size + "--->data is: "
				// + DataUtil.byte2hex(validateFrameData));
			}
		} catch (Exception e) {
			//            LogCallBack.logUtil.e(TAG, "DataUtil.byte2hex exception: " +
			// e.getMessage());
		}
	}

	/**
	 * @param gga            gga数据
	 * @param isChinese      是否是中文
	 * @param netChannelType 是否
	 */
	private void startRtkServer(String gga, boolean isChinese, int netChannelType) {
		// 塔吊只有网络rtk
		//        if (netChannelType != -1) {
		//            //中文环境，并且选取的是通道1千寻
		//            if (isChinese && netChannelType == 1) {
		//                NtripManager.getInstance().stop();
		//                NewAarQxManager.GGA = gga;
		//                NewAarQxManager.getInstance().startRtcmServer();
		//            } else {
		//                NewAarQxManager.getInstance().stopRtcmServer();
		NtripManager.getInstance().setGGA(gga);
		NtripManager.getInstance().start();
		//            }
		//        }
	}

	public boolean isHasGGA() {
		return hasGGA;
	}

	public void setHasGGA(boolean hasGGA) {
		this.hasGGA = hasGGA;
	}
}

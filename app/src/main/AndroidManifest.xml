<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
	<uses-permission android:name="android.permission.READ_PHONE_STATE" />
	<!--  paho mqtt client需要WAKE_LOCK权限  -->
	<uses-permission android:name="android.permission.WAKE_LOCK" />
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.SET_TIME" />

	<application
		android:name=".TowerApp"
		android:allowBackup="true"
		android:dataExtractionRules="@xml/data_extraction_rules"
		android:fullBackupContent="@xml/backup_rules"
		android:icon="@mipmap/ic_launcher"
		android:label="@string/app_name"
		android:largeHeap="true"
		android:networkSecurityConfig="@xml/network_security_config"
		android:roundIcon="@mipmap/ic_launcher_round"
		android:supportsRtl="true"
		android:theme="@style/AppTheme">

		<!-- Required: set your sentry.io project identifier (DSN) -->
		<meta-data
			android:name="io.sentry.dsn"
			android:value="https://<EMAIL>/4508913243979856" />
		<!-- Add data like request headers, user ip address and device name, see https://docs.sentry.io/platforms/android/data-management/data-collected/ for more info -->
		<meta-data
			android:name="io.sentry.send-default-pii"
			android:value="true" />
		<!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
		<meta-data
			android:name="io.sentry.traces.user-interaction.enable"
			android:value="true" />
		<!-- enable screenshot for crashes -->
		<meta-data
			android:name="io.sentry.attach-screenshot"
			android:value="true" />
		<!-- enable view hierarchy for crashes -->
		<meta-data
			android:name="io.sentry.attach-view-hierarchy"
			android:value="true" />
		<!-- enable the performance API by setting a sample-rate, adjust in production env -->
		<meta-data
			android:name="io.sentry.traces.sample-rate"
			android:value="1.0" />

		<uses-library
			android:name="org.apache.http.legacy"
			android:required="false" />

		<activity
			android:name=".ui.SplashActivity"
			android:configChanges="orientation|keyboardHidden|screenSize"
			android:exported="true"
			android:launchMode="singleTop"
			android:theme="@style/SplashTheme">
			<intent-filter>
				<action android:name="android.intent.action.MAIN" />

				<category android:name="android.intent.category.LAUNCHER" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.HOME" />
			</intent-filter>
		</activity>

		<activity
			android:name=".ui.MainActivity"
			android:configChanges="orientation|keyboardHidden|screenSize"
			android:launchMode="singleTask"
			android:screenOrientation="landscape" />

		<activity
			android:name=".ui.CraneDebugActivity"
			android:screenOrientation="landscape" />
	</application>

</manifest>

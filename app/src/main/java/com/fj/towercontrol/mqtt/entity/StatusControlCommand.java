package com.fj.towercontrol.mqtt.entity;

import androidx.annotation.NonNull;

import com.fj.fjprotocol.data.GeoData;

/**
 * 自动化任务运行命令
 */
public class StatusControlCommand {
	/**
	 * 开始任务命令
	 */
	public static final int COMMAND_START_TASK = 1;
	/**
	 * 装载完成命令
	 */
	public static final int COMMAND_FINISH_A = 2;
	/**
	 * 卸载完成命令
	 */
	public static final int COMMAND_FINISH_B = 3;
	/**
	 * 取消任务命令
	 */
	public static final int COMMAND_CANCEL_TASK = 4;
	/**
	 * 暂停任务命令
	 */
	public static final int COMMAND_PAUSE_TASK = 5;
	/**
	 * 继续任务命令
	 */
	public static final int COMMAND_RESUME_TASK = 6;
	/**
	 * 工作模式-推荐路径
	 */
	public static final int MODE_RECOMMEND_PATH = 1;
	/**
	 * 工作模式-智能规划
	 */
	public static final int MODE_SMART_PLANNING = 2;

	/**
	 * 任务id
	 */
	private String taskId;
	/**
	 * 任务控制 1：开始任务 2：装载完成 3：卸载完成 4：取消任务 5：暂停任务 6：继续任务
	 */
	private String command;
	/**
	 * 吊物类型 1：工字件 2：塔身
	 */
	private int liftingType;
	/**
	 * 吊运任务模式 1：推荐路径（原有的路径采集） 2：智能规划
	 */
	private int mode;
	/**
	 * A点经纬高，只有智能规划模式需要
	 */
	private GeoData pointA;
	/**
	 * B点经纬高，只有智能规划模式需要
	 */
	private GeoData pointB;

	public StatusControlCommand(String command) {
		this.command = command;
	}

	public String getCommand() {
		return command;
	}

	public void setCommand(String command) {
		this.command = command;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public int getLiftingType() {
		return liftingType;
	}

	public void setLiftingType(int liftingType) {
		this.liftingType = liftingType;
	}

	public int getMode() {
		return mode;
	}

	public void setMode(int mode) {
		this.mode = mode;
	}

	public GeoData getPointA() {
		return pointA;
	}

	public void setPointA(GeoData pointA) {
		this.pointA = pointA;
	}

	public GeoData getPointB() {
		return pointB;
	}

	public void setPointB(GeoData pointB) {
		this.pointB = pointB;
	}

	@NonNull
	@Override
	public String toString() {
		return "StatusControlCommand{"
			+ "taskId='"
			+ taskId
			+ '\''
			+ ", command='"
			+ command
			+ '\''
			+ ", liftingType="
			+ liftingType
			+ '}';
	}
}

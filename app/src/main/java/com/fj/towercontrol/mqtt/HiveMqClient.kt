package com.fj.towercontrol.mqtt

import android.annotation.SuppressLint
import android.util.Log
import com.blankj.utilcode.util.GsonUtils
import com.fj.fjprotocol.data.GeoData
import com.fj.towercontrol.BuildConfig
import com.fj.towercontrol.TowerApp
import com.fj.towercontrol.consts.MqttConstants
import com.fj.towercontrol.event.MessageEvent
import com.fj.towercontrol.event.MqttConnectStatusChangedEvent
import com.fj.towercontrol.mqtt.dto.PlatformReplyDTO
import com.fj.towercontrol.mqtt.dto.PropertyReportDTO
import com.fj.towercontrol.mqtt.dto.VersionReportDTO
import com.fj.towercontrol.mqtt.entity.CalibrationReply
import com.fj.towercontrol.mqtt.entity.GetLogReply
import com.fj.towercontrol.mqtt.entity.TowerData
import com.fj.towercontrol.mqtt.entity.Version
import com.fj.towercontrol.util.FileStore
import com.fj.towercontrol.util.SslUtils
import com.hivemq.client.mqtt.MqttClient
import com.hivemq.client.mqtt.datatypes.MqttQos
import org.greenrobot.eventbus.EventBus
import java.util.concurrent.TimeUnit

@SuppressLint("NewApi")
object HiveMqClient {

	private const val TAG = "HiveMqClient"
	private var connected = false

	private val client by lazy {
		// 生成随机六位数
		val rand6 = ((Math.random() * 9 + 1) * 100000).toInt()
		val clientId =
			MqttConstants.PRODUCT_KEY + ":" + TowerApp.VEHICLE_SN + ":" + System.currentTimeMillis() + rand6
		MqttClient.builder()
			.identifier(clientId)
			.serverHost(MqttConstants.IOT_SERVER_URI.host ?: "")
			.serverPort(MqttConstants.IOT_SERVER_URI.port)
			.useMqttVersion5()
			.simpleAuth()
			.username(MqttConstants.PRODUCT_KEY)
			.password(MqttConstants.PRODUCT_SECRET.toByteArray())
			.applySimpleAuth()
			.sslConfig()
			.keyManagerFactory(
				SslUtils.createKeyManagerFactory(
					TowerApp.getContext(),
					MqttConstants.CLIENT_CERT_FILE,
					MqttConstants.CLIENT_KEY_FILE,
				)
			)
			.trustManagerFactory(
				SslUtils.createTrustManagerFactory(
					TowerApp.getContext(),
					MqttConstants.CA_FILE,
				)
			)
			.applySslConfig()
			.automaticReconnect()
			.initialDelay(1, TimeUnit.SECONDS)
			.maxDelay(2, TimeUnit.SECONDS)
			.applyAutomaticReconnect()
			.addConnectedListener {
				Log.i(TAG, "onConnected")
				connected = true
				reportVersionInfo()
				EventBus.getDefault().postSticky(MqttConnectStatusChangedEvent(true))
			}
			.addDisconnectedListener {
				Log.i(TAG, "onDisconnect: ${it.cause.message}")
				connected = false
				EventBus.getDefault().postSticky(MqttConnectStatusChangedEvent(false))
			}
			.buildAsync()
	}

	fun connect() {
		client.connect()
			.whenComplete { _, throwable ->
				if (throwable == null) {
					Log.i(TAG, "connect success")
					subscribe()
				} else {
					Log.e(TAG, "connect failed: ${throwable.message}")
				}
			}
	}

	private fun subscribe() {
		client.subscribeWith()
			.topicFilter(MqttConstants.TOPIC_FUNCTION_INVOKE)
			.qos(MqttQos.AT_LEAST_ONCE)
			.callback { publish ->
				val payloadMsg = String(publish.payloadAsBytes)
				Log.d(TAG, "received: $payloadMsg")
				EventBus.getDefault()
					.post(MessageEvent(MessageEvent.CODE_IOT_MESSAGE_RECEIVED, payloadMsg));
			}
			.send()
			.whenComplete { _, throwable ->
				if (throwable == null) {
					Log.i(TAG, "subscribe success")
				} else {
					Log.w(TAG, "subscribe failed: ${throwable.message}")
				}
			}
	}

	fun disconnect() {
		client.disconnect()
			.whenComplete { _, u ->
				if (u == null) {
					Log.i(TAG, "disconnect success")
				} else {
					Log.w(TAG, "disconnect failed: ${u.message}")
				}
			}
	}

	private fun publish(topic: String, dto: Any, qos: MqttQos = MqttQos.AT_LEAST_ONCE) {
		if (!connected) {
			return
		}
		client.publishWith()
			.topic(topic)
			.payload(GsonUtils.toJson(dto).toByteArray())
			.qos(qos)
			.send()
			.whenComplete { t, u ->
				if (u == null) {
					Log.d(TAG, "sent: ${String(t.publish.payloadAsBytes)}")
				}
			}
	}

	fun reportVersionInfo() {
		val versionInfoDTO =
			VersionReportDTO(
				System.currentTimeMillis(),
				BuildConfig.VERSION_NAME,
				Version(BuildConfig.VERSION_NAME, FileStore.getEcuVersion())
			)
		publish(MqttConstants.TOPIC_FIRMWARE_REPORT, versionInfoDTO)
	}

	fun reportTowerData(towerData: TowerData) {
		val propertyReportDTO = PropertyReportDTO(System.currentTimeMillis(), towerData)
		publish(MqttConstants.TOPIC_PROPERTY_REPORT, propertyReportDTO, MqttQos.AT_MOST_ONCE)
	}

	fun replyConfigChange(msgId: String) {
		reply(
			MqttConstants.FUNCTION_CONFIG_CHANGE,
			msgId,
			GetLogReply("1")
		)
	}

	fun replyGetLog(msgId: String, success: Boolean) {
		reply(
			MqttConstants.FUNCTION_GET_LOG,
			msgId,
			GetLogReply("1"),
			success
		)
	}

	fun replyCalibration(msgId: String, success: Boolean, geoData: GeoData?) {
		reply(
			MqttConstants.FUNCTION_TOWER_CALIBRATION,
			msgId,
			CalibrationReply(if (success) "0" else "1", geoData),
			success
		)
	}

	private fun <T> reply(
		functionId: String,
		msgId: String,
		data: T,
		success: Boolean = true
	) {
		val replyDTO = PlatformReplyDTO<T>()
		replyDTO.messageId = msgId
		replyDTO.functionId = functionId
		replyDTO.timestamp = System.currentTimeMillis()
		replyDTO.output = data
		replyDTO.success = success
		publish(MqttConstants.TOPIC_FUNCTION_INVOKE_REPLY, replyDTO)
	}
}

package com.fj.towercontrol.mqtt

import android.annotation.SuppressLint

/**
 * 连接rtk安全帽后台的mqtt客户端
 *
 * <AUTHOR>
 * @since 2024/3/29
 */
@SuppressLint("NewApi")
object HelmetMqClient {

//	private const val TAG = "HelmetMqClient"
//	private val dataMap = ConcurrentHashMap<String, HelmetDTO>()
//
//	/**
//	 * 最近安全帽
//	 */
//	var nearestHelmet: HelmetDTO? = null
//
//	/**
//	 * 有效安全帽数量
//	 */
//	var helmetCount: Int = 0
//
//	init {
//		thread(true) {
//			while (true) {
//				if (dataMap.isEmpty()) {
//					nearestHelmet = null
//					helmetCount = 0
//					SystemClock.sleep(1_000L)
//					continue
//				}
//				var minDeviceId: String? = null
//				var minDistance = Double.MAX_VALUE
//				var validCount = 0
//				dataMap.forEach { (key, helmet) ->
//					if (System.currentTimeMillis() - helmet.timestamp > 3_000L) {
//						dataMap.remove(key)
//						return@forEach
//					}
//					//统计有效安全帽数量
//					MemoryStore.getInstance().towerConfig?.towerBaseConfig?.let { baseConfig ->
//						if (baseConfig.towerTopPos == null) {
//							return@let
//						}
//						val distanceFromTower = Calculator.calculateDistance(
//							baseConfig.towerTopPos.lat,
//							baseConfig.towerPos.lng,
//							helmet.lat,
//							helmet.lng
//						)
//						Log.d(
//							TAG,
//							"helmetSN: ${helmet.deviceSn}, distanceFromTower: $distanceFromTower, armLength: ${baseConfig.towerArmLength}"
//						)
//						if (distanceFromTower < baseConfig.towerArmLength) {
//							validCount++
//						}
//					}
//					//查找最近安全帽
//					MemoryStore.getInstance().hookPos?.let { hookPos ->
//						val distanceFromHook =
//							Calculator.calculateDistance(hookPos.lat, hookPos.lng, helmet.lat, helmet.lng)
//						Log.d(TAG, "helmetSN: ${helmet.deviceSn}, distanceFromHook: $distanceFromHook")
//						if (distanceFromHook < minDistance) {
//							minDistance = distanceFromHook
//							minDeviceId = key
//						}
//					}
//				}
//				if (minDeviceId != null) {
//					dataMap[minDeviceId]?.let {
//						nearestHelmet = it
//					}
//				} else {
//					nearestHelmet = null
//				}
//				helmetCount = validCount
//				SystemClock.sleep(1_000L)
//			}
//		}
//	}
//
//	private val client by lazy {
//		MqttClient.builder()
//			.identifier(TowerApp.VEHICLE_SN)
//			.serverHost(MqttConstants.HELMET_SERVER_URI.host ?: "")
//			.serverPort(MqttConstants.HELMET_SERVER_URI.port)
//			.useMqttVersion5()
//			.automaticReconnect()
//			.initialDelay(1, TimeUnit.SECONDS)
//			.maxDelay(2, TimeUnit.SECONDS)
//			.applyAutomaticReconnect()
//			.simpleAuth()
//			.username("tower")
//			.password((if (TowerApp.IS_PROD) "towerprod8488" else "tower@123").toByteArray())
//			.applySimpleAuth()
//			.addConnectedListener {
//				Logger.i(TAG, "onConnected")
//			}
//			.addDisconnectedListener {
//				Logger.i(TAG, "onDisconnect: ${it.cause.message}")
//			}
//			.buildAsync()
//	}
//
//	fun connect(deviceIds: List<String>) {
//		if (deviceIds.isEmpty()) {
//			Logger.w(TAG, "no device to subscribe")
//			return
//		}
//		client.connectWith()
//			.cleanStart(true)
//			.send()
//			.whenComplete { _, throwable ->
//				if (throwable == null) {
//					subscribe(deviceIds)
//				} else {
//					Logger.e(TAG, "connect failed: ${throwable.message}")
//				}
//			}
//	}
//
//	private fun subscribe(deviceIds: List<String>) {
//		client.subscribeWith()
//			.addSubscriptions(deviceIds.stream().map {
//				Mqtt5Subscription.builder()
//					.topicFilter("/$it/device/helmetPos")
//					.qos(MqttQos.AT_LEAST_ONCE)
//					.build()
//			})
//			.callback { publish ->
//				val payloadMsg = String(publish.payloadAsBytes)
//				Log.d(TAG, "received: ${publish.topic} -> $payloadMsg")
//				val deviceId = publish.topic.levels[1]
//				val helmetData = GsonUtils.fromJson(payloadMsg, HelmetDTO::class.java)
//					.also { it.timestamp = System.currentTimeMillis() }
//				if (GeoUtils.isGpsValid(helmetData.gpsType)) {
//					dataMap[deviceId] = helmetData
//				}
//			}
//			.send()
//			.whenComplete { _, throwable ->
//				if (throwable == null) {
//					Logger.i(TAG, "subscribe success: $deviceIds")
//				} else {
//					Logger.w(TAG, "subscribe failed: ${throwable.message}")
//				}
//			}
//	}
//
//	fun disconnect() {
//		client.disconnect()
//			.whenComplete { _, u ->
//				if (u == null) {
//					Logger.i(TAG, "disconnect success")
//				} else {
//					Logger.w(TAG, "disconnect failed: ${u.message}")
//				}
//			}
//	}
}

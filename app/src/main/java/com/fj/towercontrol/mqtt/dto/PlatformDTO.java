package com.fj.towercontrol.mqtt.dto;

import java.util.List;

/**
 * Mqtt平台下发消息
 *
 * <AUTHOR>
 */
public class PlatformDTO {
	/**
	 * 数据上传时间戳
	 */
	private long timestamp;
	/**
	 * 指令下发消息ID
	 */
	private String messageId;
	/**
	 * 下发服务服务ID
	 */
	private String functionId;
	/**
	 * 下发服务调用的参数组信息
	 */
	private List<InputsDTO> inputs;
	/**
	 * 固定值"INVOKE_FUNCTION"
	 */
	private String messageType;
	/**
	 * 产品Key
	 */
	private String productKey;
	/**
	 * 模块Key
	 */
	private String moduleKey;
	/**
	 * 设备Sn
	 */
	private String deviceSn;
	/**
	 * 设备在IOT系统的内部ID
	 */
	private String deviceId;
	/**
	 * 消息头信息，后期拓展使用
	 */
	private HeadersDTO headers;

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public String getMessageId() {
		return messageId;
	}

	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}

	public String getFunctionId() {
		return functionId;
	}

	public void setFunctionId(String functionId) {
		this.functionId = functionId;
	}

	public List<InputsDTO> getInputs() {
		return inputs;
	}

	public void setInputs(List<InputsDTO> inputs) {
		this.inputs = inputs;
	}

	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	public String getProductKey() {
		return productKey;
	}

	public void setProductKey(String productKey) {
		this.productKey = productKey;
	}

	public String getModuleKey() {
		return moduleKey;
	}

	public void setModuleKey(String moduleKey) {
		this.moduleKey = moduleKey;
	}

	public String getDeviceSn() {
		return deviceSn;
	}

	public void setDeviceSn(String deviceSn) {
		this.deviceSn = deviceSn;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public HeadersDTO getHeaders() {
		return headers;
	}

	public void setHeaders(HeadersDTO headers) {
		this.headers = headers;
	}
}

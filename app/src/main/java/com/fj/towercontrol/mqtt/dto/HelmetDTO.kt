package com.fj.towercontrol.mqtt.dto

import com.google.gson.annotations.SerializedName

/**
 * rtk头盔数据
 *
 * <AUTHOR>
 * @since 2024/3/29
 */
data class HelmetDTO(
	val battery: Double,
	@SerializedName("dev_type") val devType: Int,
	@SerializedName("sn") val deviceSn: String,
	@SerializedName("gps_type") val gpsType: Int,
	@SerializedName("high") var alt: Double,
	val lat: Double,
	val lng: Double,
	var timestamp: Long,
)

package com.fj.towercontrol.mqtt.dto;

/**
 * Mqtt平台下发消息应答
 *
 * @param <T> 消息具体内容
 */
public class PlatformReplyDTO<T> {

	private String functionId;
	private Long timestamp;
	private String messageId;
	private T output;
	private Boolean success;

	public String getFunctionId() {
		return functionId;
	}

	public void setFunctionId(String functionId) {
		this.functionId = functionId;
	}

	public Long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(Long timestamp) {
		this.timestamp = timestamp;
	}

	public String getMessageId() {
		return messageId;
	}

	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}

	public T getOutput() {
		return output;
	}

	public void setOutput(T output) {
		this.output = output;
	}

	public Boolean getSuccess() {
		return success;
	}

	public void setSuccess(Boolean success) {
		this.success = success;
	}
}

package com.fj.towercontrol.mqtt.entity;

import androidx.annotation.NonNull;

import com.fj.fjprotocol.data.GeoData;

/**
 * 塔顶中心点校准指令回复消息
 *
 * <AUTHOR>
 */
public class CalibrationReply {
	/**
	 * 执行结果<br>
	 * "0"-成功<br>
	 * "1"-失败
	 */
	private final String reply;
	/**
	 * 塔顶准心点校准结果坐标
	 */
	private final GeoData topCenterPoint;

	public CalibrationReply(String reply, GeoData topCenterPoint) {
		this.reply = reply;
		this.topCenterPoint = topCenterPoint;
	}

	@NonNull
	@Override
	public String toString() {
		return "CalibrationReply{" +
			"reply='" + reply + '\'' +
			", towerTopCenter=" + topCenterPoint +
			'}';
	}
}

package com.fj.towercontrol.mqtt.dto;

import com.fj.towercontrol.mqtt.entity.Version;
import com.google.gson.annotations.SerializedName;

/**
 * 固件版本上报
 *
 * <AUTHOR>
 */
public class VersionReportDTO {

	private long timestamp;
	/**
	 * app版本
	 */
	@SerializedName("version")
	private String appVersion;

	/**
	 * 其它版本
	 */
	@SerializedName("properties")
	private Version version;

	public VersionReportDTO(long timestamp, String appVersion, Version version) {
		this.timestamp = timestamp;
		this.appVersion = appVersion;
		this.version = version;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public String getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}

	public Version getVersion() {
		return version;
	}

	public void setVersion(Version version) {
		this.version = version;
	}
}

package com.fj.towercontrol.mqtt.entity;

import androidx.annotation.NonNull;

/**
 * 路径采集命令
 *
 * <AUTHOR>
 */
public class PathControlCommand {
	/**
	 * 开始路径采集命令(到了A点)
	 */
	public static final int COMMAND_COLLECT_A = 1;
	/**
	 * 开始采集B点命令(到了B点)
	 */
	public static final int COMMAND_COLLECT_B = 2;
	/**
	 * 结束采集命令
	 */
	public static final int COMMAND_FINISH_COLLECT = 3;
	/**
	 * 取消采集命令
	 */
	public static final int COMMAND_CANCEL_COLLECT = 4;

	/**
	 * 任务id
	 */
	private String taskId;
	/**
	 * 采集控制 1：路径开始采集 2：卸料点采集 3：路径结束采集 4：放弃路径采集
	 */
	private String command;

	public PathControlCommand(String command) {
		this.command = command;
	}

	public String getCommand() {
		return command;
	}

	public void setCommand(String command) {
		this.command = command;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	@NonNull
	@Override
	public String toString() {
		return "PathControlCommand{"
			+ "taskId='"
			+ taskId
			+ '\''
			+ ", command='"
			+ command
			+ '\''
			+ '}';
	}
}

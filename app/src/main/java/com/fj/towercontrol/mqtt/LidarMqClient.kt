package com.fj.towercontrol.mqtt

import android.util.Log
import com.fj.towercontrol.TowerApp
import com.fj.towercontrol.util.LidarDataParser
import com.fjd.app.common.util.DataUtil
import com.fjdynamics.app.logger.Logger
import com.hivemq.client.mqtt.MqttClient
import com.hivemq.client.mqtt.datatypes.MqttQos
import java.util.concurrent.TimeUnit

/**
 * 连接激光雷达数据的mqtt客户端
 *
 * <AUTHOR>
 * @since 2025/5/19
 */
object LidarMqClient {

	private const val TAG = "LidarMqClient"
	private const val TOPIC_OBSTACLES_DATA = "MAPPING/PUB/RPT/LOCAL_OBST"

	//	private const val TOPIC_GNSS_DATA = "HOOK_RTK/PUB/RPT/GNSS_POSE"
	private const val TOPIC_NMEA_DATA = "HOOK_RTK/PUB/RPT/GNSS_NMEA"

	private val parser: LidarDataParser by lazy {
		LidarDataParser()
	}

	private val client by lazy {
		MqttClient.builder()
			.identifier(TowerApp.VEHICLE_SN)
			.serverHost("*************")
			.serverPort(1883)
			.useMqttVersion3()
			.automaticReconnect()
			.initialDelay(1, TimeUnit.SECONDS)
			.maxDelay(2, TimeUnit.SECONDS)
			.applyAutomaticReconnect()
			.addConnectedListener {
				Logger.i(TAG, "onConnected")
			}
			.addDisconnectedListener {
				Logger.i(TAG, "onDisconnect: ${it.cause.message}")
			}
			.buildAsync()
	}

	fun connect() {
		client.connectWith()
			.send()
			.whenComplete { _, throwable ->
				if (throwable == null) {
					subscribe()
				} else {
					Logger.e(TAG, "connect failed: ${throwable.message}")
				}
			}
	}

	private fun subscribe() {
		client.subscribeWith()
			//订阅障碍物探测结果
			.addSubscription()
			.topicFilter(TOPIC_OBSTACLES_DATA)
			.qos(MqttQos.AT_MOST_ONCE)
			.applySubscription()
			//订阅GNSS定位数据
			.addSubscription()
			.topicFilter(TOPIC_NMEA_DATA)
			.qos(MqttQos.AT_MOST_ONCE)
			.applySubscription()
			.callback { publish ->
				val data = publish.payloadAsBytes
				when (publish.topic.toString()) {
					TOPIC_OBSTACLES_DATA -> {
						Log.d(TAG, "received obst data: ${DataUtil.byte2hex(data)}")
						parser.parse(data)
					}

					TOPIC_NMEA_DATA -> {
						Log.d(TAG, "received nmea data: ${DataUtil.byte2hex(data)}")
						parser.parse(data)
					}
				}
			}
			.send()
			.whenComplete { _, throwable ->
				if (throwable == null) {
					Logger.i(TAG, "subscribe success")
				} else {
					Logger.w(TAG, "subscribe failed: ${throwable.message}")
				}
			}
	}

	fun disconnect() {
		client.disconnect()
			.whenComplete { _, u ->
				if (u == null) {
					Logger.i(TAG, "disconnect success")
				} else {
					Logger.w(TAG, "disconnect failed: ${u.message}")
				}
			}
	}
}

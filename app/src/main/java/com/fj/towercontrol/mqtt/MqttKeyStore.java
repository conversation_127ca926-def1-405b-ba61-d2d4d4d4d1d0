package com.fj.towercontrol.mqtt;

import android.util.Log;

import androidx.annotation.RawRes;

import com.blankj.utilcode.util.EncryptUtils;
import com.fj.towercontrol.TowerApp;

import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.KeyFactory;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.UnrecoverableKeyException;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.ManagerFactoryParameters;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.TrustManagerFactorySpi;
import javax.net.ssl.X509TrustManager;

public final class MqttKeyStore {
	private static final String TAG = "MqttKeyStore";
	private static final String CERT_FACTORY_TYPE = "X.509";
	private static final String KEY_FACTORY_ALGO = "RSA";

	private final int privateKeyFilePath;
	private final int certFilePath;
	private final int caCertFile;

	public MqttKeyStore(@RawRes int keyFilePath, @RawRes int certFilePath, @RawRes int caCertFile) {
		this.privateKeyFilePath = keyFilePath;
		this.certFilePath = certFilePath;
		this.caCertFile = caCertFile;
	}

	public KeyManagerFactory keyManagerFactory() throws NoSuchAlgorithmException,
		KeyStoreException, UnrecoverableKeyException {
		KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
		kmf.init(loadKeyStore(), null);
		return kmf;
	}

	public KeyStore loadKeyStore() {
		KeyStore keyStore = null;
		try {
			keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
			keyStore.load(null, null);

			Key privateKey = loadPrivateKey();
			List<Certificate> certificates = loadCertificateChain(certFilePath);

			String alias = "mqtt";
			keyStore.setKeyEntry(alias, privateKey, null, certificates.toArray(new Certificate[0]));
		} catch (Exception e) {
			Log.w(TAG, "Failed to load keystore:" + e);
		}

		return keyStore;
	}

	public TrustManagerFactory trustManagerFactory() {
		try {
			TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());

			KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
			keyStore.load(null, null);
			List<Certificate> certificates = loadCertificateChain(caCertFile);
			keyStore.setCertificateEntry("mqtt", certificates.get(0));
			tmf.init(keyStore);
			return tmf;
		} catch (Exception e) {
			Log.e(TAG, "trustManagerFactory exception: " + e.getMessage());
		}
		return null;
	}

	public TrustManagerFactory trustAllManagerFactory() {
		return new TrustAllCertsFactory();
	}

	public PrivateKey loadPrivateKey() {
		PrivateKey privateKey = null;
		try (InputStream in = TowerApp.getContext().getResources().openRawResource(privateKeyFilePath);
				 BufferedInputStream bis = getDecryptedStream(in);
				 InputStreamReader isr = new InputStreamReader(bis);
				 BufferedReader reader = new BufferedReader(isr)) {
			PEMParser pemParser = new PEMParser(reader);
			Object obj = pemParser.readObject();

			PrivateKeyInfo keyInfo;
			if (obj instanceof PEMKeyPair) {
				keyInfo = ((PEMKeyPair) obj).getPrivateKeyInfo();
				KeyFactory keyFactory = KeyFactory.getInstance(KEY_FACTORY_ALGO);
				PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(keyInfo.getEncoded());
				privateKey = keyFactory.generatePrivate(privateKeySpec);
			}
		} catch (Exception e) {
			Log.e(TAG, "loadPrivateKey exception: " + e.getMessage());
		}
		return privateKey;
	}

	public List<Certificate> loadCertificateChain(int resId) {
		List<Certificate> certificates = new ArrayList<>();
		certificates.add(loadCertificate(resId));
		certificates.removeIf(Objects::isNull);
		return certificates;
	}

	private Certificate loadCertificate(@RawRes final int certificateResId) {
		Certificate certificate = null;
		try (InputStream in = TowerApp.getContext().getResources().openRawResource(certificateResId);
				 BufferedInputStream bis = getDecryptedStream(in)) {
			CertificateFactory certFactory = CertificateFactory.getInstance(CERT_FACTORY_TYPE);
			certificate = certFactory.generateCertificate(bis);
		} catch (Exception e) {
			Log.e(TAG, "loadCertificate exception: " + e.getMessage());
		}
		return certificate;
	}

	private BufferedInputStream getDecryptedStream(InputStream encryptedStream) {
		byte[] data = getBytesByStream(encryptedStream);
		byte[] decryptedData = EncryptUtils.decryptAES(data, "fjdynamics_2021!".getBytes(StandardCharsets.UTF_8), "AES/CBC/PKCS5Padding", new byte[16]);
		return new BufferedInputStream(new ByteArrayInputStream(decryptedData));
	}

	private byte[] getBytesByStream(InputStream stream) {
		ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
		byte[] buffer = new byte[1024];
		int bytesRead;
		try {
			while ((bytesRead = stream.read(buffer)) != -1) {
				byteArrayOutputStream.write(buffer, 0, bytesRead);
			}
			byte[] byteArray = byteArrayOutputStream.toByteArray();
			stream.close();
			return byteArray;
		} catch (Exception e) {
			throw new IllegalStateException(e);
		}
	}

	private static final class TrustAllCertsFactory extends TrustManagerFactory {

		private static final TrustManagerFactory TMF_DEF = createDefautTrustManagerFactory();

		private static TrustManagerFactory createDefautTrustManagerFactory() {
			try {
				TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
				KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
				keyStore.load(null, null);
				tmf.init(keyStore);
				return tmf;
			} catch (Exception e) {
				throw new IllegalStateException(e);
			}
		}

		private TrustAllCertsFactory() {
			super(new TrustAllCertsFactorySpi(), TMF_DEF.getProvider(), TMF_DEF.getAlgorithm());
		}
	}

	private static class TrustAllCertsFactorySpi extends TrustManagerFactorySpi {

		TrustAllCertsFactorySpi() {
		}

		@Override
		protected void engineInit(KeyStore ks) {
		}

		@Override
		protected void engineInit(ManagerFactoryParameters spec) {
		}

		@Override
		protected TrustManager[] engineGetTrustManagers() {
			return new TrustManager[]{new TrustManagerAllCerts()};
		}
	}

	private static class TrustManagerAllCerts implements X509TrustManager {

		@Override
		public void checkClientTrusted(X509Certificate[] chain, String authType) {
		}

		@Override
		public void checkServerTrusted(X509Certificate[] chain, String authType) {
		}

		@Override
		public X509Certificate[] getAcceptedIssuers() {
			return new X509Certificate[0];
		}
	}
}

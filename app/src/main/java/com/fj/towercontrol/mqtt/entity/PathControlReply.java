package com.fj.towercontrol.mqtt.entity;

import com.fj.fjprotocol.data.GeoData;

/**
 * 路径采集回复
 *
 * <AUTHOR>
 */
public class PathControlReply {

	/**
	 * 0：成功 1：失败
	 */
	private String reply;
	/**
	 * 堆料点地理数据
	 */
	private GeoData stackingPoint;
	/**
	 * 卸料点地理数据
	 */
	private GeoData unloadingPoint;

	public PathControlReply(String reply, GeoData stackingPoint, GeoData unloadingPoint) {
		this.reply = reply;
		this.stackingPoint = stackingPoint;
		this.unloadingPoint = unloadingPoint;
	}

	public String getReply() {
		return reply;
	}

	public void setReply(String reply) {
		this.reply = reply;
	}

	public GeoData getStackingPoint() {
		return stackingPoint;
	}

	public void setStackingPoint(GeoData stackingPoint) {
		this.stackingPoint = stackingPoint;
	}

	public GeoData getUnloadingPoint() {
		return unloadingPoint;
	}

	public void setUnloadingPoint(GeoData unloadingPoint) {
		this.unloadingPoint = unloadingPoint;
	}
}

package com.fj.towercontrol.mqtt.entity;

import androidx.annotation.NonNull;

import com.fj.fjprotocol.data.GeoData;
import com.fj.towercontrol.mqtt.dto.InputsDTO;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 下发任务调用数据类
 */
public class IotTowerConfig {
	/**
	 * 避障距离阈值（橙）
	 */
	private Double warningDistance;
	/**
	 * 避障距离阈值（红）
	 */
	private Double prohibitedDistance;
	/**
	 * 报警风速阈值
	 */
	private Double warningWindSpeed;
	/**
	 * 障碍物8个点的地理数据
	 */
	private List<String> obstaclePoint;
	/**
	 * 塔臂长度
	 */
	@SerializedName("length")
	private Double armLength;
	/**
	 * 塔吊品牌
	 */
	private String brand;
	/**
	 * 吊物信息
	 */
	private List<String> liftingInfo;
	/**
	 * 塔基三维地理数据
	 */
	private GeoData tower;
	/**
	 * 限重表
	 */
	private List<LoadingConfig> loadTable;

	public Double getWarningDistance() {
		return warningDistance;
	}

	public void setWarningDistance(Double warningDistance) {
		this.warningDistance = warningDistance;
	}

	public Double getProhibitedDistance() {
		return prohibitedDistance;
	}

	public void setProhibitedDistance(Double prohibitedDistance) {
		this.prohibitedDistance = prohibitedDistance;
	}

	public Double getWarningWindSpeed() {
		return warningWindSpeed;
	}

	public void setWarningWindSpeed(Double warningWindSpeed) {
		this.warningWindSpeed = warningWindSpeed;
	}

	public List<String> getObstaclePoint() {
		return obstaclePoint;
	}

	public void setObstaclePoint(List<String> obstaclePoint) {
		this.obstaclePoint = obstaclePoint;
	}

	public Double getArmLength() {
		return armLength;
	}

	public void setArmLength(Double armLength) {
		this.armLength = armLength;
	}

	public String getBrand() {
		return brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	public List<String> getLiftingInfo() {
		return liftingInfo;
	}

	public void setLiftingInfo(List<String> liftingInfo) {
		this.liftingInfo = liftingInfo;
	}

	public GeoData getTower() {
		return tower;
	}

	public void setTower(GeoData tower) {
		this.tower = tower;
	}

	public List<LoadingConfig> getLoadTable() {
		return loadTable;
	}

	public void setLoadTable(List<LoadingConfig> loadTable) {
		this.loadTable = loadTable;
	}

	@NonNull
	@Override
	public String toString() {
		return "TowerConfig{"
			+ "避障距离阈值（橙）="
			+ warningDistance
			+ ", 避障距离阈值（红）="
			+ prohibitedDistance
			+ ", 报警风速阈值="
			+ warningWindSpeed
			+ ", 障碍物地理数据="
			+ obstaclePoint
			+ ", 塔臂长度="
			+ armLength
			+ ", 塔吊品牌='"
			+ brand
			+ '\''
			+ ", 吊物信息="
			+ liftingInfo
			+ ", 塔基三维地理数据="
			+ tower
			+ ", 限重配置表="
			+ loadTable
			+ '}';
	}

	/**
	 * 将IOT平台下发的配置解析成TowerConfig对象
	 *
	 * @param inputs 平台下发参数列表
	 * @return TowerConfig
	 */
	public static IotTowerConfig fromInputsDTO(List<InputsDTO> inputs) {
		IotTowerConfig iotTowerConfig = new IotTowerConfig();
		for (InputsDTO inputsDTO : inputs) {
			switch (inputsDTO.getName()) {
				case "warningDistance":
					iotTowerConfig.setWarningDistance((double) inputsDTO.getValue());
					break;
				case "prohibitedDistance":
					iotTowerConfig.setProhibitedDistance((double) inputsDTO.getValue());
					break;
				case "warningWindSpeed":
					iotTowerConfig.setWarningWindSpeed((double) inputsDTO.getValue());
					break;
				case "obstaclePoint":
					List<String> jsonList = (List<String>) inputsDTO.getValue();
					iotTowerConfig.setObstaclePoint(jsonList);
					break;
				case "length":
					iotTowerConfig.setArmLength((double) inputsDTO.getValue());
					break;
				case "brand":
					iotTowerConfig.setBrand((String) inputsDTO.getValue());
					break;
				case "tower":
					Map<String, Double> towerMap = (Map<String, Double>) inputsDTO.getValue();
					Double towerLat = towerMap.get("lat");
					Double towerLng = towerMap.get("lng");
					Double towerAlt = towerMap.get("alt");
					iotTowerConfig.setTower(
						new GeoData(
							towerLat == null ? 0 : towerLat,
							towerLng == null ? 0 : towerLng,
							towerAlt == null ? 0 : towerAlt));
					break;
				case "loadTable":
					List<Map<String, Double>> values =
						(List<Map<String, Double>>) inputsDTO.getValue();
					List<LoadingConfig> loadingConfigList = new ArrayList<>();
					for (Map<String, Double> obstacle : values) {
						Double workRadius = obstacle.get("workRadius");
						Double weightLimit = obstacle.get("weightLimit");
						if (workRadius == null || weightLimit == null) {
							continue;
						}
						loadingConfigList.add(new LoadingConfig(workRadius, weightLimit));
					}
					iotTowerConfig.setLoadTable(loadingConfigList);
					break;
				case "liftingInfo":
					List<String> liftingInfoList = (List<String>) inputsDTO.getValue();
					iotTowerConfig.setLiftingInfo(liftingInfoList);
					break;
				default:
					break;
			}
		}
		return iotTowerConfig;
	}
}

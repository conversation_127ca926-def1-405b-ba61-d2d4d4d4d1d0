package com.fj.towercontrol.mqtt.entity

import android.util.Log
import com.fj.towercontrol.websocket.client.dto.ObstaclesDTO
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * 激光雷达局部障碍物探测数据
 *
 * <AUTHOR>
 * @since 2025/5/19
 */
data class LidarDetectionData(
	//[SYSTEM] 数据同步 时间戳，单位：μs
	var timestamp1: Long = 0L,
	//[STEADY] 数据同步 时间戳，单位：μs
	var timestamp2: Long = 0L,
	//X-Y 平面探测范围内圈半径 radius_min（>=0），单位：m
	var minRadius: Float = 0f,
	//X-Y 平面探测范围外圈半径 radius_max（>=radius_min），单位：m
	var maxRadius: Float = 0f,
	//Z 轴方向探测向下距离（<=0），单位：m
	var minDown: Float = 0f,
	//Z 轴方向探测向上距离（>=0），单位：m
	var minUp: Float = 0f,
	//探测到障碍物距离最小值 dist_min，单位：m
	var minDistance: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(+Z, (  +X  )) 上部分，前方
	var topFront: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(+Z, (  -X  )) 上部分，后方
	var topBack: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(+Z, (  +Y  )) 上部分，左方
	var topLeft: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(+Z, (  -Y  )) 上部分，右方
	var topRight: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(+Z, (+X, +Y)) 上部分，前左
	var topFrontLeft: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(+Z, (+X, -Y)) 上部分，前右
	var topFrontRight: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(+Z, (-X, +Y)) 上部分，后左
	var topBackLeft: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(+Z, (-X, -Y)) 上部分，后右
	var topBackRight: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(-Z, (  +X  )) 下部分，前方
	var bottomFront: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(-Z, (  -X  )) 下部分，后方
	var bottomBack: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(-Z, (  +Y  )) 下部分，左方
	var bottomLeft: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(-Z, (  -Y  )) 下部分，右方
	var bottomRight: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(-Z, (+X, +Y)) 下部分，前左
	var bottomFrontLeft: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(-Z, (+X, -Y)) 下部分，前右
	var bottomFrontRight: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(-Z, (-X, +Y)) 下部分，后左
	var bottomBackLeft: Float = 0f,
	//探测到障碍物距离，单位：m，方向：(-Z, (-X, -Y)) 下部分，后右
	var bottomBackRight: Float = 0f,
) {
	companion object {
		fun parse(bytes: ByteArray): LidarDetectionData {
			val data = LidarDetectionData()
			try {
				ByteBuffer.wrap(bytes).order(ByteOrder.LITTLE_ENDIAN).apply {
					data.timestamp1 = getLong(0)
					data.timestamp2 = getLong(8)
					data.minRadius = getFloat(44)
					data.maxRadius = getFloat(48)
					data.minDown = getFloat(52)
					data.minUp = getFloat(56)
					data.minDistance = getFloat(60)
					data.topFront = getFloat(64)
					data.topBack = getFloat(68)
					data.topLeft = getFloat(72)
					data.topRight = getFloat(76)
					data.topFrontLeft = getFloat(80)
					data.topFrontRight = getFloat(84)
					data.topBackLeft = getFloat(88)
					data.topBackRight = getFloat(92)
					data.bottomFront = getFloat(96)
					data.bottomBack = getFloat(100)
					data.bottomLeft = getFloat(104)
					data.bottomRight = getFloat(108)
					data.bottomFrontLeft = getFloat(112)
					data.bottomFrontRight = getFloat(116)
					data.bottomBackLeft = getFloat(120)
					data.bottomBackRight = getFloat(124)
				}
			} catch (e: Exception) {
				Log.e("LidarDetectionData", "parse error: ${e.message}")
			}

			return data
		}
	}

	fun toObstaclesDTO(): ObstaclesDTO {
		return ObstaclesDTO(
			top = floatArrayOf(
				topFront, topFrontRight, topRight, topBackRight,
				topBack, topBackLeft, topLeft, topFrontLeft
			),
			bottom = floatArrayOf(
				bottomFront, bottomFrontRight, bottomRight, bottomBackRight,
				bottomBack, bottomBackLeft, bottomLeft, bottomFrontLeft
			),
		)
	}

}

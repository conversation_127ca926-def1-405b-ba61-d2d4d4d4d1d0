package com.fj.towercontrol.mqtt.entity

import android.util.Log
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * 激光雷达GNSS数据
 *
 * <AUTHOR>
 * @since 2025/6/10
 */
data class LidarGnssData(
	//[SYSTEM] 数据同步 时间戳，单位：μs
	var timestamp1: Long = 0L,
	//[STEADY] 数据同步 时间戳，单位：μs
	var timestamp2: Long = 0L,
	var fixQuality: Int = 0,
	var gnssTimestamp: Long = 0L,
	var longitude: Double = 0.0,
	var latitude: Double = 0.0,
	var altitude: Double = 0.0,
	//双天线航向角 yaw，单位：0.01°要求：正东为 0°，向北转为正方向
	var yaw: Double = 0.0
) {
	companion object {
		private const val DEGREE_SCALE = 1_000_000_000.0
		private const val ALTITUDE_SCALE = 1_000.0
		private const val YAW_SCALE = 100.0

		fun parse(bytes: ByteArray): LidarGnssData {
			val data = LidarGnssData()
			try {
				ByteBuffer.wrap(bytes).order(ByteOrder.LITTLE_ENDIAN).apply {
					data.timestamp1 = getLong(0)
					data.timestamp2 = getLong(8)
					data.fixQuality = get(44).toUByte().toInt()
					data.gnssTimestamp = getLong(45)
					data.longitude = getLong(53) / DEGREE_SCALE
					data.latitude = getLong(61) / DEGREE_SCALE
					data.altitude = getInt(69) / ALTITUDE_SCALE
					data.yaw = getShort(73) / YAW_SCALE
				}
			} catch (e: Exception) {
				Log.e("LidarGnssData", "parse error: ${e.message}")
			}
			return data
		}
	}
}

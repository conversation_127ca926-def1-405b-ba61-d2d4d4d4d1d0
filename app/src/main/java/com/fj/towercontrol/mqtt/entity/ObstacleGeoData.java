package com.fj.towercontrol.mqtt.entity;

import com.fj.fjprotocol.data.GeoData;

import java.util.List;

/**
 * 障碍物位置信息
 *
 * <AUTHOR>
 */
public class ObstacleGeoData {

	/**
	 * 障碍物名称
	 */
	private String obstacleName;

	private List<GeoData> towerMessageGeographicVOS;

	public String getObstacleName() {
		return obstacleName;
	}

	public void setObstacleName(String obstacleName) {
		this.obstacleName = obstacleName;
	}

	public List<GeoData> getObstaclePoint() {
		return towerMessageGeographicVOS;
	}

	public void setObstaclePoint(List<GeoData> obstaclePoint) {
		this.towerMessageGeographicVOS = obstaclePoint;
	}
}

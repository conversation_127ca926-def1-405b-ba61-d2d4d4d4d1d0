package com.fj.towercontrol.mqtt.dto;

import com.fj.towercontrol.mqtt.entity.TowerData;
import com.google.gson.annotations.SerializedName;

/**
 * 属性数据上报
 *
 * <AUTHOR>
 */
public class PropertyReportDTO {

	private long timestamp;

	@SerializedName("properties")
	private TowerData towerData;

	public PropertyReportDTO(long timestamp, TowerData towerData) {
		this.timestamp = timestamp;
		this.towerData = towerData;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public TowerData getTowerData() {
		return towerData;
	}

	public void setTowerData(TowerData towerData) {
		this.towerData = towerData;
	}
}

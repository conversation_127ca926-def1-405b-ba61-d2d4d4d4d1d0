package com.fj.towercontrol.mqtt;

//import com.blankj.utilcode.util.GsonUtils;
//import com.fj.fjprotocol.data.GeoData;
//import com.fj.towercontrol.R;
//import com.fj.towercontrol.TowerApp;
//import com.fj.towercontrol.consts.MqttConstants;
//import com.fj.towercontrol.event.MessageEvent;
//import com.fj.towercontrol.event.MqttConnectStatusChangedEvent;
//import com.fj.towercontrol.mqtt.dto.EventReportDTO;
//import com.fj.towercontrol.mqtt.dto.PlatformReplyDTO;
//import com.fj.towercontrol.mqtt.dto.PropertyReportDTO;
//import com.fj.towercontrol.mqtt.dto.VersionReportDTO;
//import com.fj.towercontrol.mqtt.entity.CalibrationReply;
//import com.fj.towercontrol.mqtt.entity.GetLogReply;
//import com.fj.towercontrol.mqtt.entity.PathControlReply;
//import com.fj.towercontrol.mqtt.entity.StatusControlReply;
//import com.fj.towercontrol.mqtt.entity.TowerConfigReply;
//import com.fj.towercontrol.mqtt.entity.TowerData;
//import com.fj.towercontrol.mqtt.entity.TraceInfo;
//import com.fj.towercontrol.mqtt.entity.Version;
//import com.fj.towercontrol.util.SSLUtil;
//import com.fjdynamics.app.logger.Logger;
//import com.fjdynamics.mqtt.Builder;
//import com.fjdynamics.mqtt.MqttCallbackAdapter;
//import com.fjdynamics.mqtt.MqttManager;
//
//import org.eclipse.paho.client.mqttv3.MqttMessage;
//import org.greenrobot.eventbus.EventBus;

/**
 * Mqtt客户端
 *
 * <AUTHOR>
 */
public class MqttClient {

//	private static final String TAG = "MqttClient";
//
//	private MqttManager mqttManager;
//	private String topicPropertyReport;
//	private String topicTraceInfoReport;
//	private String topicSubscribeFunctionReply;
//	private String topicFirmwareReport;
//	private String pdKey;
//	private String pdSecret;
//
//	private MqttClient() {
//	}
//
//	private static class SingletonHolder {
//		private static final MqttClient INSTANCE = new MqttClient();
//	}
//
//	public static MqttClient getInstance() {
//		return MqttClient.SingletonHolder.INSTANCE;
//	}
//
//	/**
//	 * mqtt客户端初始化
//	 */
//	public void init() {
//		boolean isProd = TowerApp.IS_PROD;
//		Logger.d(TAG, "mqttClient init, isProd: " + isProd);
//		if (mqttManager != null) {
//			mqttManager.stop();
//		}
//		pdKey = MqttConstants.PRODUCT_KEY;
//		pdSecret = MqttConstants.PRODUCT_SECRET;
//		topicPropertyReport =
//			String.format("/sys/%s/default/%s/properties/report", pdKey, TowerApp.VEHICLE_SN);
//		String topicEventReportBase =
//			String.format("/sys/%s/default/%s/event/", pdKey, TowerApp.VEHICLE_SN);
//		topicTraceInfoReport = topicEventReportBase + MqttConstants.FUNCTION_TRACE_INFO;
//		String topicSubscribeFunction =
//			String.format("/sys/%s/default/%s/function/invoke", pdKey, TowerApp.VEHICLE_SN);
//		topicSubscribeFunctionReply = topicSubscribeFunction + "/reply";
//		topicFirmwareReport =
//			String.format("/ota/%s/default/%s/firmware/report", pdKey, TowerApp.VEHICLE_SN);
//		// 生成随机六位数
//		int rand6 = (int) ((Math.random() * 9 + 1) * 100000);
//		String clientId =
//			pdKey + ":" + TowerApp.VEHICLE_SN + ":" + System.currentTimeMillis() + rand6;
//		String host = MqttConstants.HOST_URL;
//		String[] topics = new String[]{topicSubscribeFunction};
//		int[] qos = new int[]{0};
//		int caCertRaw = isProd ? R.raw.cacert : R.raw.test_cacert;
//		int clientCertRaw = isProd ? R.raw.client_cert : R.raw.test_client_cert;
//		int clientKeyRaw = isProd ? R.raw.client_key : R.raw.test_client_key;
//		try {
//			mqttManager = new Builder(TowerApp.getContext())
//				.client(clientId)
//				.host(host)
//				.account(pdKey)
//				.password(pdSecret)
//				.topic(topics)
//				.qos(qos)
//				.ssl(SSLUtil.getSocketFactory(caCertRaw, clientCertRaw, clientKeyRaw, ""))
//				.autoConnect(true)
//				.debug(true)
//				.create();
//			mqttManager.registerListener(new TowerMqttCallback());
//			mqttManager.start();
//		} catch (Exception e) {
//			Logger.e(TAG, "mqttManager init exception: " + e.getMessage());
//			e.printStackTrace();
//		}
//	}
//
//	public String getProductKey() {
//		return pdKey;
//	}
//
//	public String getProductSecret() {
//		return pdSecret;
//	}
//
//	/**
//	 * 发布消息
//	 *
//	 * @param topic topic
//	 * @param data  要发送的数据实体类
//	 */
//	private void publishMessage(String topic, Object data) {
//		if (mqttManager == null) {
//			Logger.e(TAG, "MqttClient not init!");
//			return;
//		}
//		String json = GsonUtils.toJson(data);
//		MqttMessage mqttMessage = new MqttMessage(json.getBytes());
//		// 这里publish方法有可能会空指针 https://github.com/eclipse/paho.mqtt.android/issues/183
//		try {
//			mqttManager.publish(topic, mqttMessage);
//		} catch (Exception e) {
//			Logger.e(TAG, "publishMessage exception");
//			e.printStackTrace();
//		}
//	}
//
//	/**
//	 * 每次和物联网平台成功连接以后，需要全量上报这些值，若对应状态无指定值，则用""置空传输。当这些值在终端发生变化需同步上传物联网平台，此时仅需传输发生变化的值，而无需全量同步状态值
//	 *
//	 * @param towerData 上报数据
//	 */
//	public void reportProperty(TowerData towerData) {
//		PropertyReportDTO reportDTO = new PropertyReportDTO(System.currentTimeMillis(), towerData);
//		publishMessage(topicPropertyReport, reportDTO);
//	}
//
//	/**
//	 * 终端上报版本号时使用，上报时机由终端决定，推荐每次联网成功后上报一次
//	 *
//	 * @param appVersion app版本号
//	 * @param ecuVersion ecu版本号
//	 */
//	public void reportVersion(String appVersion, String ecuVersion) {
//		VersionReportDTO reportDTO =
//			new VersionReportDTO(
//				System.currentTimeMillis(),
//				appVersion,
//				new Version(appVersion, ecuVersion));
//		publishMessage(topicFirmwareReport, reportDTO);
//	}
//
//	/**
//	 * 任务调用应答
//	 *
//	 * @param messageId 指令下发消息ID
//	 */
//	public void replyTowerConfig(String messageId) {
//		PlatformReplyDTO<TowerConfigReply> replyDTO = new PlatformReplyDTO<>();
//		replyDTO.setMessageId(messageId);
//		replyDTO.setFunctionId(MqttConstants.FUNCTION_TOWER_CONFIG);
//		replyDTO.setTimestamp(System.currentTimeMillis());
//		replyDTO.setOutput(new TowerConfigReply("0"));
//		replyDTO.setSuccess(true);
//		publishMessage(topicSubscribeFunctionReply, replyDTO);
//	}
//
//	/**
//	 * 路径采集应答
//	 *
//	 * @param messageId      指令下发消息ID
//	 * @param stackingPoint  堆料店
//	 * @param unloadingPoint 卸料点
//	 */
//	public void replyPathControl(
//		String messageId, GeoData stackingPoint, GeoData unloadingPoint, boolean success) {
//		PlatformReplyDTO<PathControlReply> replyDTO = new PlatformReplyDTO<>();
//		replyDTO.setMessageId(messageId);
//		replyDTO.setFunctionId(MqttConstants.FUNCTION_PATH_CONTROL);
//		replyDTO.setTimestamp(System.currentTimeMillis());
//		replyDTO.setOutput(
//			new PathControlReply(success ? "0" : "1", stackingPoint, unloadingPoint));
//		replyDTO.setSuccess(success);
//		publishMessage(topicSubscribeFunctionReply, replyDTO);
//	}
//
//	/**
//	 * 自动化任务运行应答
//	 *
//	 * @param messageId 指令下发消息ID
//	 */
//	public void replyStatusControl(String messageId) {
//		PlatformReplyDTO<StatusControlReply> replyDTO = new PlatformReplyDTO<>();
//		replyDTO.setMessageId(messageId);
//		replyDTO.setFunctionId(MqttConstants.FUNCTION_STATUS_CONTROL);
//		replyDTO.setTimestamp(System.currentTimeMillis());
//		replyDTO.setOutput(new StatusControlReply("0"));
//		replyDTO.setSuccess(true);
//		publishMessage(topicSubscribeFunctionReply, replyDTO);
//	}
//
//	/**
//	 * 塔吊配置同步应答
//	 *
//	 * @param messageId 指令下发消息ID
//	 */
//	public void replyTowerConfigDetail(String messageId) {
//		//        TowerConfig towerConfig = FileStore.getTowerConfig();
//		//        PlatformReplyDTO<TowerConfig> replyDTO = new PlatformReplyDTO<>();
//		//        replyDTO.setMessageId(messageId);
//		//        replyDTO.setFunctionId(MqttConstants.FUNCTION_TOWER_CONFIG_DETAIL);
//		//        replyDTO.setTimestamp(System.currentTimeMillis());
//		//        replyDTO.setOutput(towerConfig);
//		//        replyDTO.setSuccess(towerConfig != null);
//		//        publishMessage(topicSubscribeFunctionReply, replyDTO);
//	}
//
//	/**
//	 * 日志上传应答
//	 *
//	 * @param messageId 指令下发消息ID
//	 */
//	public void replyGetLog(String messageId) {
//		PlatformReplyDTO<GetLogReply> replyDTO = new PlatformReplyDTO<>();
//		replyDTO.setMessageId(messageId);
//		replyDTO.setFunctionId(MqttConstants.FUNCTION_GET_LOG);
//		replyDTO.setTimestamp(System.currentTimeMillis());
//		replyDTO.setOutput(new GetLogReply("1"));
//		replyDTO.setSuccess(true);
//		publishMessage(topicSubscribeFunctionReply, replyDTO);
//	}
//
//	/**
//	 * 回复预警配置变更消息
//	 *
//	 * @param messageId 消息id
//	 */
//	public void replyConfigChange(String messageId) {
//		PlatformReplyDTO<GetLogReply> replyDTO = new PlatformReplyDTO<>();
//		replyDTO.setMessageId(messageId);
//		replyDTO.setFunctionId(MqttConstants.FUNCTION_CONFIG_CHANGE);
//		replyDTO.setTimestamp(System.currentTimeMillis());
//		replyDTO.setOutput(new GetLogReply("1"));
//		replyDTO.setSuccess(true);
//		publishMessage(topicSubscribeFunctionReply, replyDTO);
//	}
//
//	/**
//	 * 吊钩轨迹上报
//	 *
//	 * @param traceInfo 轨迹信息
//	 */
//	public void reportTraceInfo(TraceInfo traceInfo) {
//		EventReportDTO<TraceInfo> reportDTO =
//			new EventReportDTO<>(System.currentTimeMillis(), traceInfo);
//		publishMessage(topicTraceInfoReport, reportDTO);
//	}
//
//	/**
//	 * 塔顶中心点校准指令回复
//	 *
//	 * @param messageId 消息id
//	 * @param success   是否成功
//	 * @param geoData   结束校准时的计算结果
//	 */
//	public void replyCalibration(String messageId, boolean success, GeoData geoData) {
//		PlatformReplyDTO<CalibrationReply> replyDTO = new PlatformReplyDTO<>();
//		replyDTO.setMessageId(messageId);
//		replyDTO.setFunctionId(MqttConstants.FUNCTION_TOWER_CALIBRATION);
//		replyDTO.setTimestamp(System.currentTimeMillis());
//		replyDTO.setOutput(new CalibrationReply(success ? "0" : "1", geoData));
//		replyDTO.setSuccess(true);
//		publishMessage(topicSubscribeFunctionReply, replyDTO);
//	}
//
//	private static class TowerMqttCallback extends MqttCallbackAdapter {
//		@Override
//		public void onError(String s, Throwable throwable) {
//			Logger.e(TAG, "TowerMqttCallback onError: " + s);
//			EventBus.getDefault()
//				.postSticky(new MqttConnectStatusChangedEvent(false));
//		}
//
//		@Override
//		public void messageArrived(String topic, String message) {
//			EventBus.getDefault()
//				.post(new MessageEvent(MessageEvent.CODE_IOT_MESSAGE_RECEIVED, message));
//		}
//
//		@Override
//		public void connectComplete(boolean b, String s) {
//			Logger.d(TAG, "TowerMqttCallback connectComplete");
//			EventBus.getDefault()
//				.postSticky(new MqttConnectStatusChangedEvent(true));
//		}
//	}
}

package com.fj.towercontrol.mqtt.entity;

import androidx.annotation.NonNull;

/**
 * 限重表
 *
 * <AUTHOR>
 */
public class LoadingConfig {
	/**
	 * 工作半径
	 */
	private Double radius;
	/**
	 * 限重
	 */
	private Double limitWeight;

	public LoadingConfig(Double radius, Double limitWeight) {
		this.radius = radius;
		this.limitWeight = limitWeight;
	}

	public Double getRadius() {
		return radius;
	}

	public void setRadius(Double radius) {
		this.radius = radius;
	}

	public Double getLimitWeight() {
		return limitWeight;
	}

	public void setLimitWeight(Double limitWeight) {
		this.limitWeight = limitWeight;
	}

	@NonNull
	@Override
	public String toString() {
		return "LoadingConfig{" + "radius=" + radius + ", limitWeight=" + limitWeight + '}';
	}
}

package com.fj.towercontrol.mqtt.entity;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;

/**
 * 云平台配置的吊物信息
 *
 * <AUTHOR>
 */
public class LiftingInfo {

	private String liftingType;

	private Double liftingDistance;

	@SerializedName("objectSize")
	private CargoSize cargoSize;

	public LiftingInfo(String liftingType, Double liftingDistance, CargoSize cargoSize) {
		this.liftingType = liftingType;
		this.liftingDistance = liftingDistance;
		this.cargoSize = cargoSize;
	}

	public String getLiftingType() {
		return liftingType;
	}

	public void setLiftingType(String liftingType) {
		this.liftingType = liftingType;
	}

	public Double getLiftingDistance() {
		return liftingDistance;
	}

	public void setLiftingDistance(Double liftingDistance) {
		this.liftingDistance = liftingDistance;
	}

	public CargoSize getCargoSize() {
		return cargoSize;
	}

	public void setCargoSize(CargoSize cargoSize) {
		this.cargoSize = cargoSize;
	}

	@NonNull
	@Override
	public String toString() {
		return "LiftingInfo{"
			+ "liftingType='"
			+ liftingType
			+ '\''
			+ ", liftingDistance="
			+ liftingDistance
			+ ", cargoSize="
			+ cargoSize
			+ '}';
	}
}

package com.fj.towercontrol.consts;

import android.net.Uri;

import com.fj.towercontrol.R;
import com.fj.towercontrol.TowerApp;

/**
 * 常量定义类
 *
 * <AUTHOR>
 */
public class MqttConstants {
	/**
	 * mqtt server uri
	 */
	public static final Uri IOT_SERVER_URI =
		Uri.parse(TowerApp.IS_PROD ? "mqtts://mqtt-hk.fjdac.com:8884" : "mqtts://emq-iot-test.fjdac.cn:8884");
	public static final Uri HELMET_SERVER_URI =
		Uri.parse(TowerApp.IS_PROD ? "mqtt://mq.fjdac.cn:1883" : "mqtt://mqtt-test.fjdac.cn:1883");
	/**
	 * iot pdKey
	 */
	public static final String PRODUCT_KEY = TowerApp.IS_PROD ? "AHnwMpED" : "5G5xBKZV";
	/**
	 * iot pdSecret
	 */
	public static final String PRODUCT_SECRET = TowerApp.IS_PROD ? "Rx4bAgr8CbJPjjus" : "a77zeiEvZ5VVQP0I";
	/**
	 * 客户端ssl证书文件
	 */
	public static final int CLIENT_CERT_FILE = TowerApp.IS_PROD ? R.raw.client_cert : R.raw.test_client_cert;
	/**
	 * 客户端ssl key文件
	 */
	public static final int CLIENT_KEY_FILE = TowerApp.IS_PROD ? R.raw.client_key : R.raw.test_client_key;
	/**
	 * ca文件
	 */
	public static final int CA_FILE = TowerApp.IS_PROD ? R.raw.cacert : R.raw.test_cacert;

	/**
	 * 吊钩轨迹上报
	 */
	public static final String FUNCTION_TRACE_INFO = "traceInfo";
	/**
	 * 塔吊配置<br>
	 * 历史topic，展会版本时使用的，新版本已不再使用
	 */
	@Deprecated
	public static final String FUNCTION_TOWER_CONFIG = "towerConfig";
	/**
	 * 路径采集
	 */
	public static final String FUNCTION_PATH_CONTROL = "pathControl";
	/**
	 * 自动化任务运行
	 */
	public static final String FUNCTION_STATUS_CONTROL = "statusControl";
	/**
	 * 塔吊配置同步<br>
	 * 历史topic，展会版本时使用的，新版本已不再使用
	 */
	@Deprecated
	public static final String FUNCTION_TOWER_CONFIG_DETAIL = "towerConfigDetail";
	/**
	 * 获取日志
	 */
	public static final String FUNCTION_GET_LOG = "getLog";
	/**
	 * 预警配置发生变化
	 */
	public static final String FUNCTION_CONFIG_CHANGE = "configChange";
	/**
	 * 塔顶位置标定
	 */
	public static final String FUNCTION_TOWER_CALIBRATION = "towerCalibration";

	/**
	 * topic - 方法调用
	 */
	public static final String TOPIC_FUNCTION_INVOKE = String.format(
		"/sys/%s/default/%s/function/invoke",
		PRODUCT_KEY,
		TowerApp.VEHICLE_SN
	);
	/**
	 * topic - 方法调用结果回复
	 */
	public static final String TOPIC_FUNCTION_INVOKE_REPLY = TOPIC_FUNCTION_INVOKE + "/reply";
	/**
	 * topic - 固件版本上报
	 */
	public static final String TOPIC_FIRMWARE_REPORT = String.format(
		"/ota/%s/default/%s/firmware/report",
		PRODUCT_KEY,
		TowerApp.VEHICLE_SN
	);
	/**
	 * topic - 塔吊属性上报
	 */
	public static final String TOPIC_PROPERTY_REPORT = String.format(
		"/sys/%s/default/%s/properties/report",
		PRODUCT_KEY,
		TowerApp.VEHICLE_SN
	);
}

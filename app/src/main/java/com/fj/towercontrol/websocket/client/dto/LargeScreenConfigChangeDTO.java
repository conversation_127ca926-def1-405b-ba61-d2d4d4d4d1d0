package com.fj.towercontrol.websocket.client.dto;

import androidx.annotation.NonNull;

/**
 * 通知大屏配置变更DTO
 *
 * <AUTHOR>
 */
public class LargeScreenConfigChangeDTO {
	/**
	 * 数据类型
	 */
	private String type;
	/**
	 * 变更类型，目前只发1<br>
	 * 1:大屏配置变更<br>
	 * 2:告警配置变更<br>
	 * 3:塔吊属性配置变更
	 */
	private int changeType;

	public LargeScreenConfigChangeDTO(int changeType) {
		this.type = "configChange";
		this.changeType = changeType;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public int getChangeType() {
		return changeType;
	}

	public void setChangeType(int changeType) {
		this.changeType = changeType;
	}

	@NonNull
	@Override
	public String toString() {
		return "LargeScreenConfigChangeDTO{" +
			"type='" + type + '\'' +
			", changeType=" + changeType +
			'}';
	}
}

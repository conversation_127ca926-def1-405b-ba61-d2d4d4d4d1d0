package com.fj.towercontrol.websocket.client.dto

/**
 * 发送给大屏后台的障碍物探测结果DTO
 *
 * <AUTHOR>
 * @since 2025/6/10
 */
data class ObstaclesDTO(
	val top: FloatArray,
	val bottom: FloatArray,
	val type: String = "obstacles"
) {
	override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
		if (this === other) return true
		if (javaClass != other?.javaClass) return false

		other as ObstaclesDTO

		if (!top.contentEquals(other.top)) return false
		if (!bottom.contentEquals(other.bottom)) return false
		if (type != other.type) return false

		return true
	}

	override fun hashCode(): Int {
		var result = top.contentHashCode()
		result = 31 * result + bottom.contentHashCode()
		result = 31 * result + type.hashCode()
		return result
	}
}

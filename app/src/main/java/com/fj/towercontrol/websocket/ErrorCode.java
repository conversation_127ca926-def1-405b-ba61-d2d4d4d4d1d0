package com.fj.towercontrol.websocket;

/**
 * Websocket消息错误码
 *
 * <AUTHOR>
 */
public enum ErrorCode {

	/**
	 * 当前工作状态不是：远程手动模式+工作状态,不可以执行任务采集
	 */
	TOWER_STATUS_NOT_WAITING(1000),
	/**
	 * 当前塔吊状态不为空闲状态,无法开始任务
	 */
	TOWER_REPORT_NOT_FREE(1001),
	/**
	 * 轨迹透传失败
	 */
	TOWER_TASK_POINTS_SEND_FAILED(1002),
	/**
	 * 未找到历史轨迹
	 */
	TOWER_TASK_POINTS_NOT_FOUND(1003),
	/**
	 * 轨迹文件下载失败
	 */
	TOWER_TASK_HISTORY_DOWNLOAD_FAILED(1004),
	/**
	 * RTK非固定解
	 */
	RTK_STATUS_ERROR(1005),
	/**
	 * ECU数据异常
	 */
	ECU_STATUS_ERROR(1006),
	/**
	 * 当前工作模式不是“任务托管”,无法开始任务
	 */
	TOWER_MODE_NOT_SEMIAUTOMATIC(1007),
	/**
	 * 当前塔吊非自动托管空闲状态
	 */
	TOWER_START_TASK_FAILED(1008),
	/**
	 * 未知错误
	 */
	UNKNOWN(1),
	/**
	 * 成功
	 */
	SUCCESS(0);

	private final int code;

	ErrorCode(int code) {
		this.code = code;
	}

	public int getCode() {
		return code;
	}
}

package com.fj.towercontrol.websocket.client;

import android.os.Handler;
import android.os.Looper;

import com.blankj.utilcode.util.GsonUtils;
import com.fj.towercontrol.event.MessageEvent;
import com.fjd.app.common.websocket.WebSocketListener;
import com.fjd.app.common.websocket.client.SimpleWebSocketClient;
import com.fjdynamics.app.logger.Logger;

import org.greenrobot.eventbus.EventBus;

import java.net.URI;

/**
 * 管理和ai-lift WebSocket Server通信
 *
 * <AUTHOR>
 */
public class AiLiftWebSocketManager {
	private static final String TAG = "AiLiftWebSocketManager";
	private final Handler handler = new Handler(Looper.getMainLooper());
	private SimpleWebSocketClient webSocketClient;

	private AiLiftWebSocketManager() {
	}

	private static class SingletonHolder {
		private static final AiLiftWebSocketManager INSTANCE = new AiLiftWebSocketManager();
	}

	public static AiLiftWebSocketManager getInstance() {
		return AiLiftWebSocketManager.SingletonHolder.INSTANCE;
	}

	/**
	 * 建立ws连接
	 */
	public void connect() {
		webSocketClient = new SimpleWebSocketClient(TAG, URI.create(getWsUrl()), 15, new WebSocketListener() {
			@Override
			public void onConnectStatusChanged(boolean connected, int code) {
				EventBus.getDefault().post(new MessageEvent(MessageEvent.AI_LIFT_WS_STATUS_UPDATED, connected));
				handler.removeCallbacksAndMessages(null);
				if (!connected) {
					//自动尝试重连
					handler.postDelayed(() -> connect(), 2_000);
				}
			}

			@Override
			public void onMessage(String msg) {
				EventBus.getDefault().post(new MessageEvent(MessageEvent.SERVER_WS_MESSAGE, msg));
			}

			@Override
			public void onMessage(byte[] bytes) {
			}
		});

		webSocketClient.connect();
	}

	/**
	 * 断开ws连接
	 */
	public void disconnect() {
		if (webSocketClient != null) {
			webSocketClient.close();
		}
	}

	private String getWsUrl() {
//		return StringUtils.format(
//			"ws://%s:8070/monitor/mini_ctrl/%s",
//			MemoryStore.getInstance().getSystemConfig().getAiLiftUrl().getHost(),
//			TowerApp.IS_PROD ? "prod" : "test"
//		);
		return "";
	}

	/**
	 * 发送数据到大屏
	 *
	 * @param data data
	 */
	public void sendData(Object data) {
		if (data == null || webSocketClient == null || !webSocketClient.isOpen()) {
			return;
		}
		String json = GsonUtils.toJson(data);
		if (json == null) {
			return;
		}
		Logger.d(TAG, "send: " + json);
		webSocketClient.send(json);
	}
}

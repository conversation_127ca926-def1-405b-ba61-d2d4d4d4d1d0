package com.fj.towercontrol.websocket.client.dto;

/**
 * 大屏推送的吊物半径更新消息
 *
 * <AUTHOR>
 */
public class RadiusUpdateDTO {
	/**
	 * 消息类型，固定为"radiusUpdate"
	 */
	private String type;
	/**
	 * 吊物半径(m)
	 */
	private double loadRadius;

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public double getLoadRadius() {
		return loadRadius;
	}

	public void setLoadRadius(double loadRadius) {
		this.loadRadius = loadRadius;
	}
}

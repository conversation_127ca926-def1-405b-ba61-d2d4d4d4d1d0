package com.fj.towercontrol.websocket;

/**
 * 和司机中控平板通信的消息模型
 *
 * <AUTHOR>
 */
public class WebSocketMessage<T> {

	/**
	 * 回复消息messageType后缀
	 */
	public static final String SUFFIX_REPLY = "_reply";

	public static final String PROPERTY_REPORT = "propertyReport";
	public static final String TRACE_INFO = "traceInfo";
	public static final String TOWER_CONFIG = "towerConfig";
	public static final String PATH_CONTROL = "pathControl";
	public static final String STATUS_CONTROL = "statusControl";
	public static final String TOWER_CONFIG_DETAIL = "towerConfigDetail";
	public static final String VERSION_REPORT = "versionReport";
	public static final String TASK_STATUS = "taskStatus";
	public static final String DESCENT_HEIGHT = "descentHeight";
	/**
	 * 登录状态上报，0:未登录 1:登录成功 2:持续人脸检测失败
	 */
	public static final String LOGIN_STATUS_UPDATE = "loginStatusUpdate";
	/**
	 * 塔吊属性上报
	 */
	public static final String TOWER_DATA = "towerData";
	/**
	 * 缓停延迟配置
	 */
	public static final String CONFIG_SLOWDOWN_DELAY = "delayConfig";
	/**
	 * 座椅按键状态
	 */
	public static final String BUTTONS = "buttons";
	/**
	 * 请求执行半自动任务
	 */
	public static final String START_AUTO_TASK = "SEMIAUTOMATIC_TASK";
	/**
	 * 请求更新半自动任务状态
	 */
	public static final String UPDATE_TASK_STATUS = "UPDATE_TASK_STATUS";
	/**
	 * 司机中控请求采点
	 */
	public static final String COLLECT_POINT = "COLLECT_POINT";
	/**
	 * 舵机控制指令
	 */
	public static final String MOTOR_CONTROL = "MOTOR_CONTROL";
	/**
	 * 开始自动规划的路径任务
	 */
	public static final String START_PLANNED_PATH_TASK = "START_PLANNED_PATH_TASK";
	/**
	 * uuid
	 */
	private String messageId;
	/**
	 * 消息类型
	 * 属性上报 -propertyReport<br>
	 * 吊钩轨迹上报 - traceInfo<br>
	 * 塔吊配置 - towerConfig<br>
	 * 路径采集 - pathControl<br>
	 * 自动化任务运行 - statusControl<br>
	 * 塔吊配置同步 - towerConfigDetail<br>
	 * 固件版本上报 - versionReport<br>
	 * <p>
	 * 回复消息统一带上“_reply“后缀
	 */
	private String messageType;
	/**
	 * 消息发送的时间戳
	 */
	private long timestamp;
	/**
	 * 数据
	 */
	private T data;
	/**
	 * 错误码
	 *
	 * @see ErrorCode
	 */
	private int code;

	public String getMessageId() {
		return messageId;
	}

	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}

	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}
}

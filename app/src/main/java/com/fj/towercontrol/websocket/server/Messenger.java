package com.fj.towercontrol.websocket.server;

import com.fj.towercontrol.data.entity.MotorControlCmd;
import com.fj.towercontrol.data.entity.SemiautomaticTask;
import com.fj.towercontrol.mqtt.entity.PathControlReply;
import com.fj.towercontrol.mqtt.entity.StatusControlReply;
import com.fj.towercontrol.mqtt.entity.TaskStatus;
import com.fj.towercontrol.mqtt.entity.TowerData;
import com.fj.towercontrol.websocket.ErrorCode;
import com.fj.towercontrol.websocket.WebSocketMessage;

import java.util.List;
import java.util.UUID;

/**
 * 对塔吊地面显控APP回复消息体封装工具类
 *
 * <AUTHOR>
 */
public class Messenger {

	/**
	 * 任务采集回复
	 *
	 * @param messageId 消息id
	 * @param code      错误码
	 * @return 封装后的msg
	 */
	public static WebSocketMessage<PathControlReply> replyPathControl(
		String messageId, ErrorCode code) {
		WebSocketMessage<PathControlReply> msg = new WebSocketMessage<>();
		msg.setMessageId(messageId);
		msg.setTimestamp(System.currentTimeMillis());
		msg.setMessageType(WebSocketMessage.PATH_CONTROL + WebSocketMessage.SUFFIX_REPLY);
		msg.setCode(code.getCode());
		msg.setData(
			new PathControlReply(
				code.getCode() == ErrorCode.SUCCESS.getCode() ? "0" : "1", null, null));
		return msg;
	}

	/**
	 * 任务开始回复
	 *
	 * @param messageId 消息id
	 * @param code      错误码
	 * @return msg
	 */
	public static WebSocketMessage<StatusControlReply> replyStatusControl(
		String messageId, ErrorCode code) {
		WebSocketMessage<StatusControlReply> msg = new WebSocketMessage<>();
		msg.setMessageId(messageId);
		msg.setTimestamp(System.currentTimeMillis());
		msg.setMessageType(WebSocketMessage.STATUS_CONTROL + WebSocketMessage.SUFFIX_REPLY);
		msg.setCode(code.getCode());
		msg.setData(
			new StatusControlReply(code.getCode() == ErrorCode.SUCCESS.getCode() ? "0" : "1"));
		return msg;
	}

	/**
	 * 任务状态上报
	 *
	 * @param status 当前状态
	 * @return msg
	 */
	public static WebSocketMessage<TaskStatus> reportTaskStatus(int status) {
		WebSocketMessage<TaskStatus> msg = new WebSocketMessage<>();
		msg.setMessageId(UUID.randomUUID().toString().replaceAll("-", ""));
		msg.setTimestamp(System.currentTimeMillis());
		msg.setMessageType(WebSocketMessage.TASK_STATUS);
		msg.setData(new TaskStatus(status));
		return msg;
	}

	/**
	 * 上报吊钩下降高度
	 *
	 * @param height 吊钩下降高度
	 * @return msg
	 */
	public static WebSocketMessage<Double> reportDecentHeight(double height) {
		WebSocketMessage<Double> msg = new WebSocketMessage<>();
		msg.setMessageId(UUID.randomUUID().toString().replaceAll("-", ""));
		msg.setTimestamp(System.currentTimeMillis());
		msg.setMessageType(WebSocketMessage.DESCENT_HEIGHT);
		msg.setData(height);
		return msg;
	}

	/**
	 * 上报塔吊属性
	 *
	 * @param towerData 塔吊属性
	 * @return msg
	 */
	public static WebSocketMessage<TowerData> reportTowerData(TowerData towerData) {
		WebSocketMessage<TowerData> msg = new WebSocketMessage<>();
		msg.setMessageId(UUID.randomUUID().toString().replaceAll("-", ""));
		msg.setTimestamp(System.currentTimeMillis());
		msg.setMessageType(WebSocketMessage.TOWER_DATA);
		msg.setData(towerData);
		return msg;
	}

	/**
	 * 上报座椅按键
	 *
	 * @param buttons 塔吊属性
	 * @return msg
	 */
	public static WebSocketMessage<List<Integer>> reportButtonStatus(List<Integer> buttons) {
		WebSocketMessage<List<Integer>> msg = new WebSocketMessage<>();
		msg.setMessageId(UUID.randomUUID().toString().replaceAll("-", ""));
		msg.setTimestamp(System.currentTimeMillis());
		msg.setMessageType(WebSocketMessage.BUTTONS);
		msg.setData(buttons);
		return msg;
	}

	public static WebSocketMessage<Object> replyDelayConfig(String msgId, boolean success) {
		WebSocketMessage<Object> msg = new WebSocketMessage<>();
		msg.setMessageId(msgId);
		msg.setTimestamp(System.currentTimeMillis());
		msg.setMessageType(WebSocketMessage.CONFIG_SLOWDOWN_DELAY);
		msg.setCode(success ? ErrorCode.SUCCESS.getCode() : ErrorCode.UNKNOWN.getCode());
		return msg;
	}

	public static WebSocketMessage<Integer> updateSemiAutoTaskStatus(String msgId, int status) {
		WebSocketMessage<Integer> msg = new WebSocketMessage<>();
		msg.setMessageId(msgId);
		msg.setTimestamp(System.currentTimeMillis());
		msg.setMessageType(WebSocketMessage.START_AUTO_TASK + WebSocketMessage.SUFFIX_REPLY);
		msg.setCode(ErrorCode.SUCCESS.getCode());
		msg.setData(status);
		return msg;
	}

	public static WebSocketMessage<SemiautomaticTask> replyCollectPoint(String msgId, ErrorCode errorCode, SemiautomaticTask task) {
		WebSocketMessage<SemiautomaticTask> msg = new WebSocketMessage<>();
		msg.setMessageId(msgId);
		msg.setTimestamp(System.currentTimeMillis());
		msg.setMessageType(WebSocketMessage.COLLECT_POINT + WebSocketMessage.SUFFIX_REPLY);
		msg.setCode(errorCode.getCode());
		msg.setData(task);
		return msg;
	}

	public static WebSocketMessage<Integer> sendMotorControlCmd(MotorControlCmd cmd) {
		WebSocketMessage<Integer> msg = new WebSocketMessage<>();
		msg.setMessageId(UUID.randomUUID().toString().replaceAll("-", ""));
		msg.setTimestamp(System.currentTimeMillis());
		msg.setMessageType(WebSocketMessage.MOTOR_CONTROL);
		msg.setData(cmd.getCmd());
		return msg;
	}
}

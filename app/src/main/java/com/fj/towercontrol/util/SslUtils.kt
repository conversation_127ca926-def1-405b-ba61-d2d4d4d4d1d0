package com.fj.towercontrol.util

import android.content.Context
import androidx.annotation.RawRes
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo
import org.bouncycastle.openssl.PEMKeyPair
import org.bouncycastle.openssl.PEMParser
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter
import java.io.InputStreamReader
import java.security.KeyPair
import java.security.KeyStore
import java.security.cert.Certificate
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import javax.crypto.Cipher
import javax.crypto.CipherInputStream
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import javax.net.ssl.KeyManagerFactory
import javax.net.ssl.TrustManagerFactory

/**
 * 用于加载IOT平台SSL证书的工具类
 *
 * <AUTHOR>
 * @since 2025/7/21
 */
object SslUtils {

	@JvmStatic
	fun createTrustManagerFactory(context: Context, @RawRes caCertRes: Int): TrustManagerFactory {
		val caCertificate = createDecryptedStream(context, caCertRes).use {
			CertificateFactory.getInstance("X.509").generateCertificate(it)
		} as X509Certificate

		val trustStore = KeyStore.getInstance(KeyStore.getDefaultType()).apply {
			load(null, null)
			setCertificateEntry("ca-certificate", caCertificate)
		}

		return TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
			.apply { init(trustStore) }
	}

	@JvmStatic
	fun createKeyManagerFactory(
		context: Context,
		@RawRes clientCertRes: Int,
		@RawRes clientKeyRes: Int,
		clientKeyPassword: String? = null,
	): KeyManagerFactory {
		val clientCertificate: X509Certificate =
			createDecryptedStream(context, clientCertRes).use {
				CertificateFactory.getInstance("X.509").generateCertificate(it)
			} as X509Certificate

		val keyObject = createDecryptedStream(context, clientKeyRes).use {
			PEMParser(InputStreamReader(it)).readObject()
		}

		val converter = JcaPEMKeyConverter()
		val keyPair: KeyPair = when (keyObject) {
			is PEMKeyPair -> converter.getKeyPair(keyObject)
			is PrivateKeyInfo -> {
				val privateKey = converter.getPrivateKey(keyObject)
				KeyPair(clientCertificate.publicKey, privateKey)
			}

			else -> throw IllegalArgumentException("Unsupported key type: ${keyObject.javaClass.name}")
		}

		val password = clientKeyPassword?.toCharArray() ?: CharArray(0)
		val keyStore = KeyStore.getInstance(KeyStore.getDefaultType()).apply {
			load(null, null)
			setKeyEntry(
				"client-key",
				keyPair.private,
				password,
				arrayOf<Certificate?>(clientCertificate)
			)
		}

		val keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm())
		keyManagerFactory.init(keyStore, password)

		return keyManagerFactory
	}

	private fun createDecryptedStream(
		context: Context,
		@RawRes encryptedRes: Int,
		decryptKey: String = "fjdynamics_2021!",
	): CipherInputStream {
		val rawInputStream = context.resources.openRawResource(encryptedRes)
		val secretKey = SecretKeySpec(decryptKey.toByteArray(Charsets.UTF_8), "AES")
		val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding").apply {
			init(Cipher.DECRYPT_MODE, secretKey, IvParameterSpec(ByteArray(16)))
		}

		return CipherInputStream(rawInputStream, cipher)
	}
}

package com.fj.towercontrol.util;

import android.app.backup.BackupManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.LocaleList;

import com.fj.towercontrol.TowerApp;
import com.fjdynamics.app.logger.Logger;

import java.lang.reflect.Method;
import java.util.Locale;

/**
 * Everyday is another day, keep going. Created by ramo.wu email: <EMAIL> date:
 * 2020/6/12 13:47 desc: 多语言管理
 *
 * <AUTHOR>
 */
public class MultiLanguageManager {
	private static final String TAG = "MultiLanguageManager";

	/**
	 * 切换系统语言环境
	 *
	 * @param locale 目标语言
	 */
	public static void switchLocale(Locale locale) {
		if (locale != null) {
			try {
				if (!PermissionUtil.hasPermission(
					TowerApp.getContext(), "android.permission.CHANGE_CONFIGURATION")) {
					return;
				}
				if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
					Locale sysLocale = Resources.getSystem().getConfiguration().getLocales().get(0);
					if (locale.getLanguage().equalsIgnoreCase(sysLocale.getLanguage())
						&& locale.getCountry().equalsIgnoreCase(sysLocale.getCountry())) {
						return;
					}
				} else {
					if (Resources.getSystem()
						.getConfiguration()
						.locale
						.getLanguage()
						.equalsIgnoreCase(locale.getLanguage())
						&& Resources.getSystem()
						.getConfiguration()
						.locale
						.getCountry()
						.equalsIgnoreCase(locale.getCountry())) {
						return;
					}
				}
				Class classActivityManagerNative =
					Class.forName("android.app.ActivityManagerNative");
				Method getDefault = classActivityManagerNative.getDeclaredMethod("getDefault");
				Object objIActivityManager = getDefault.invoke(classActivityManagerNative);
				Class classIActivityManager = Class.forName("android.app.IActivityManager");
				Method getConfiguration =
					classIActivityManager.getDeclaredMethod("getConfiguration");
				Configuration config = (Configuration) getConfiguration.invoke(objIActivityManager);
				Class[] clzParams = {Configuration.class};
				Method updateConfiguration;
				if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
					LocaleList localeList = new LocaleList(locale);
					config.setLocales(localeList);
					updateConfiguration =
						classIActivityManager.getDeclaredMethod(
							"updatePersistentConfiguration", clzParams);
				} else {
					config.setLocale(locale);
					Class clzConfig = Class.forName("android.content.res.Configuration");
					java.lang.reflect.Field userSetLocale = clzConfig.getField("userSetLocale");
					userSetLocale.set(config, true);
					updateConfiguration =
						classIActivityManager.getDeclaredMethod(
							"updateConfiguration", clzParams);
				}
				updateConfiguration.invoke(objIActivityManager, config);
				BackupManager.dataChanged("com.android.providers.settings");
			} catch (Exception e) {
				Logger.e(TAG, "changeSystemLanguage: ");
			}
		}
	}

	/**
	 * 获取当前的语言 默认为Locale.SIMPLIFIED_CHINESE.getLanguage()
	 *
	 * @return getDisplayName
	 */
	public static String getCurLocale() {
		return Locale.getDefault().getLanguage();
	}

	public static String getCurLanguage() {
		String languageString = getCurLocale();
		if (languageString.equals("zh")) {
			languageString = languageString + "-" + Locale.getDefault().getCountry();
		}
		return languageString;
	}

	// 维吾尔语
	public static final String UYGHUR = "ug";

	/**
	 * 系统语言是否是中文( 国内用 千寻SDK 国外用 Ntrip )
	 *
	 * @return
	 */
	// 台湾的getLanguage和中文一致
	public static boolean isChinese() {
		return (Locale.SIMPLIFIED_CHINESE
			.getLanguage()
			.equalsIgnoreCase(MultiLanguageManager.getCurLocale())
			&& Locale.getDefault()
			.getCountry()
			.equalsIgnoreCase(Locale.SIMPLIFIED_CHINESE.getCountry()))
			|| isUyghur();
	}

	// 是中文但不是维吾尔语
	public static boolean isChineseNotUyghur() {
		return Locale.SIMPLIFIED_CHINESE
			.getLanguage()
			.equalsIgnoreCase(MultiLanguageManager.getCurLocale())
			&& !isUyghur();
	}

	// 是否是维语
	public static boolean isUyghur() {
		return UYGHUR.equalsIgnoreCase(MultiLanguageManager.getCurLocale());
	}

	// 是否是繁体中文
	public static boolean isTaiWan() {
		return Locale.TAIWAN.getLanguage().equalsIgnoreCase(MultiLanguageManager.getCurLocale())
			&& Locale.TAIWAN.getCountry().equalsIgnoreCase(Locale.getDefault().getCountry());
	}

	// 简体中文
	public static boolean isSimpleChinese() {
		return Locale.TAIWAN.getLanguage().equalsIgnoreCase(MultiLanguageManager.getCurLocale())
			&& Locale.SIMPLIFIED_CHINESE
			.getCountry()
			.equalsIgnoreCase(Locale.getDefault().getCountry());
	}

	public static boolean isJapan() {
		return Locale.JAPAN.getLanguage().equalsIgnoreCase(MultiLanguageManager.getCurLocale());
	}
}

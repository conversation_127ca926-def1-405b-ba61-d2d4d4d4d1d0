package com.fj.towercontrol.util

import android.util.Log
import com.fj.towercontrol.event.MessageEvent
import com.fj.towercontrol.mqtt.entity.LidarDetectionData
import org.greenrobot.eventbus.EventBus
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.concurrent.Executors

/**
 * 激光雷达数据解析器
 *
 * <AUTHOR>
 * @since 2025/5/19
 */
class LidarDataParser() {
	private val executor = Executors.newSingleThreadExecutor()

	fun parse(bytes: ByteArray) {
		executor.execute {
			val buffer = ByteBuffer.wrap(bytes)

			if (buffer.remaining() < 22) {
				Log.w(TAG, "data incomplete: ${bytes.size}")
				return@execute
			}
			if (buffer.get(0) != 0xaa.toByte() || buffer.get(1) != 0x02.toByte()) {
				Log.w(TAG, "protocol mismatch")
				return@execute
			}

			buffer.order(ByteOrder.LITTLE_ENDIAN)
			val dataLen = buffer.getInt(16)
			if (dataLen <= 0) {
				Log.w(TAG, "data length not valid: $dataLen")
				return@execute
			}

			val totalLen = 20 + dataLen + 2
			if (buffer.remaining() < totalLen) {
				Log.w(TAG, "expect data length: $totalLen, actual: ${buffer.remaining()}")
				return@execute
			}

			val cmdId1 = buffer.get(6)
			val cmdId2 = buffer.get(7)

			if (cmdId1 == 0x1b.toByte() && cmdId2 == 0x06.toByte()) {
				//局部障碍物探测结果
				val obstaclesBytes = ByteArray(dataLen).apply {
					buffer.position(20)
					buffer.get(this)
				}
				val obstacles = LidarDetectionData.parse(obstaclesBytes)
				EventBus.getDefault()
					.post(MessageEvent(MessageEvent.CODE_LIDAR_DETECTION_UPDATE, obstacles))
			} else if (cmdId1 == 0x20.toByte() && cmdId2 == 0x09.toByte()) {
				//GNSS定位数据
				val nmeaLen = dataLen - 4
				if (nmeaLen <= 0) {
					Log.w(TAG, "nmea length error: $nmeaLen")
					return@execute
				}

				val nmeaBytes = ByteArray(nmeaLen).apply {
					buffer.position(24)
					buffer.get(this)
				}
				EventBus.getDefault()
					.post(MessageEvent(MessageEvent.CODE_LIDAR_NMEA_DATA_UPDATE, nmeaBytes))
			}
		}
	}

	companion object {
		private const val TAG = "LidarDataParser"
	}
}

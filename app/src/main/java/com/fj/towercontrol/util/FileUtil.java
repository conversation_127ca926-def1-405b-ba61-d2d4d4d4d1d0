package com.fj.towercontrol.util;

import android.text.TextUtils;

import com.fjdynamics.app.logger.Logger;

import java.io.BufferedInputStream;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;

public class FileUtil {

	private static final int MAX_LOG_FILE_SIZE = 2 * 1024 * 1024;
	private static String TAG = "FileUtil";

	/**
	 * 创建文件
	 *
	 * @param filePath
	 * @param fileName
	 * @return
	 */
	public static File mkFile(String filePath, String fileName) {
		File fileDir = new File(filePath); // 如果没有filePath文件夹则新建该文件夹
		if (!fileDir.exists()) {
			fileDir.mkdirs();
		}

		File file = new File(filePath, fileName);
		return file;
	}

	/**
	 * 文件写入信息
	 *
	 * @param filePath
	 * @param fileName
	 * @param msg
	 */
	//    public static void writeMsgToFile(String filePath, String fileName, String msg) {
	//        File file = mkFile(filePath, fileName);
	//
	//        if (file != null) {
	//            try {
	//                // 后面这个参数代表是不是要接上文件中原来的数据，不进行覆盖
	//                FileWriter filerWriter = new FileWriter(file, true);
	//                BufferedWriter bufWriter = new BufferedWriter(filerWriter);
	//                bufWriter.write(msg);
	//                bufWriter.newLine();
	//                bufWriter.close();
	//                filerWriter.close();
	//            } catch (IOException e) {
	//                e.printStackTrace();
	//            }
	//        }
	//        if (file.length() > MAX_LOG_FILE_SIZE) {
	//            try {
	//                ZipUtils.ZipFolder(file.getAbsolutePath(),
	// file.getAbsolutePath().replace("txt", "gz"));
	//                file.delete();
	//            } catch (Exception e) {
	//                e.printStackTrace();
	//            }
	//            SharedPreferenceUtil.getInstance().updateLogTime();
	//            deleteOverdueLogFile();
	//        }
	//    }

	/**
	 * 最多保存50个日志
	 */
	//    private static boolean deleteOverdueLogFile() {
	//        File mfolder = new File(LogUtil.LOG_PATH); //打开目录文件夹
	//        if (mfolder.isDirectory()) {
	//            File[] AllFiles = mfolder.listFiles(); //列出目录下的所有文件
	//            ArrayList<String> mFilesList = new ArrayList<String>();  //存放Log 下的所有文件
	//            for (int i = 0; i < AllFiles.length; i++) {
	//                File mFile = AllFiles[i]; //得到文件
	//                String name = mFile.getName(); //得到文件的名字
	//                if (name == null || name.length() < 1) {
	//                    return false;
	//                }
	//                if (name.endsWith(".txt") || name.endsWith(".gz")) {  //筛选出log
	//                    mFilesList.add(name); // 把文件名添加到链表里
	//                }
	//            }
	//            Collections.sort(mFilesList);   // 将文件按自然排序升序排列
	//            //判断日志文件如果大于50，就要处理
	//            for (int i = 0; i < mFilesList.size() - 49; i++) {
	//                String name = mFilesList.get(i); // 得到链表最早的文件名
	//                File mFile = new File(mfolder, name);  // 得到最早的文件
	//                LogUtil.d(TAG, "will delect file : " + mFile.getName());
	//                mFile.delete(); //删除
	//            }
	//        }
	//        return true;
	//    }
	public static byte[] getBytesByStream(InputStream inputStream) {
		try {
			// 获取输入流
			// 新的 byte 数组输出流，缓冲区容量1024byte
			ByteArrayOutputStream bos = new ByteArrayOutputStream(1024);
			// 缓存
			byte[] b = new byte[1024];
			int n;
			while ((n = inputStream.read(b)) != -1) {
				bos.write(b, 0, n);
			}
			inputStream.close();
			// 改变为byte[]
			byte[] data = bos.toByteArray();
			//
			bos.close();
			return data;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static byte[] getBytesByFile(File file) {
		try {
			// 获取输入流
			FileInputStream fis = new FileInputStream(file);

			// 新的 byte 数组输出流，缓冲区容量1024byte
			ByteArrayOutputStream bos = new ByteArrayOutputStream(1024);
			// 缓存
			byte[] b = new byte[1024];
			int n;
			while ((n = fis.read(b)) != -1) {
				bos.write(b, 0, n);
			}
			fis.close();
			// 改变为byte[]
			byte[] data = bos.toByteArray();
			//
			bos.close();
			return data;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * byte[] 转 file
	 *
	 * @param bytes
	 * @param path
	 * @return
	 * @see [类、类#方法、类#成员]
	 */
	public static void byteToFile(byte[] bytes, String path) {
		try {
			// 根据绝对路径初始化文件
			File localFile = new File(path);
			if (localFile.exists()) {
				localFile.delete();
			}
			localFile.createNewFile();
			// 输出流
			OutputStream os = new FileOutputStream(localFile);
			os.write(bytes);
			os.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 复制单个文件
	 *
	 * @param oldPath String 原文件路径 如：c:/fqf.txt
	 * @param newPath String 复制后路径 如：f:/fqf.txt
	 * @return boolean
	 */
	public static void copyFile(String oldPath, String newPath) {
		try {
			int bytesum = 0;
			int byteread = 0;
			File oldfile = new File(oldPath);
			if (oldfile.exists()) { // 文件存在时
				InputStream inStream = new FileInputStream(oldPath); // 读入原文件
				FileOutputStream fs = new FileOutputStream(newPath);
				byte[] buffer = new byte[1444];
				int length;
				while ((byteread = inStream.read(buffer)) != -1) {
					bytesum += byteread; // 字节数 文件大小
					System.out.println(bytesum);
					fs.write(buffer, 0, byteread);
				}
				inStream.close();
			}
		} catch (Exception e) {
			System.out.println("复制单个文件操作出错");
			e.printStackTrace();
		}
	}

	/**
	 * 复制整个文件夹内容
	 *
	 * @param oldPath String 原文件路径 如：c:/fqf
	 * @param newPath String 复制后路径 如：f:/fqf/ff
	 * @return boolean
	 */
	public static void copyFolder(String oldPath, String newPath) {

		try {
			(new File(newPath)).mkdirs(); // 如果文件夹不存在 则建立新文件夹
			File a = new File(oldPath);
			String[] file = a.list();
			File temp = null;
			for (int i = 0; i < file.length; i++) {
				if (oldPath.endsWith(File.separator)) {
					temp = new File(oldPath + file[i]);
				} else {
					temp = new File(oldPath + File.separator + file[i]);
				}

				if (temp.isFile()) {
					FileInputStream input = new FileInputStream(temp);
					FileOutputStream output =
						new FileOutputStream(newPath + "/" + (temp.getName()).toString());
					byte[] b = new byte[1024 * 5];
					int len;
					while ((len = input.read(b)) != -1) {
						output.write(b, 0, len);
					}
					output.flush();
					output.close();
					input.close();
				}
				if (temp.isDirectory()) { // 如果是子文件夹
					copyFolder(oldPath + "/" + file[i], newPath + "/" + file[i]);
				}
			}
		} catch (Exception e) {
			System.out.println("复制整个文件夹内容操作出错");
			e.printStackTrace();
		}
	}

	/**
	 * 创建文件所在目录
	 *
	 * @param filePath 完整文件名
	 * @return filePath 指向的文件对象
	 */
	public static File createFileParentDir(String filePath) {
		File file = new File(filePath);
		File dir = file.getParentFile();
		if (!dir.exists()) dir.mkdirs();
		return file;
	}

	/**
	 * 文件是否存在
	 *
	 * @param filePath 文件路径
	 * @return
	 */
	public static boolean isFileExists(String filePath) {
		return isFileExists(new File(filePath));
	}

	/**
	 * 文件是否存在
	 *
	 * @param file 文件
	 * @return
	 */
	public static boolean isFileExists(File file) {
		return file != null && file.exists();
	}

	/**
	 * 创建文件所在目录
	 *
	 * @param file 文件对象
	 * @return 创建成功返回true, 否则返回false
	 */
	public static boolean createFileParentDir(File file) {
		File dir = file.getParentFile();
		if (!dir.exists()) return dir.mkdirs();
		return false;
	}

	/**
	 * 创建文件
	 */
	public static boolean createOrExistsFile(String filePath) {
		if (TextUtils.isEmpty(filePath)) {
			return false;
		}
		return createOrExistsFile(new File(filePath));
	}

	/**
	 * 创建文件
	 */
	public static boolean createOrExistsFile(final File file) {
		if (file == null) {
			return false;
		}
		if (file.exists()) {
			return file.isFile();
		}
		if (!createOrExistsDir(file.getParentFile())) {
			return false;
		}
		try {
			return file.createNewFile();
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * 创建文件夹
	 */
	public static boolean createOrExistsDir(final File file) {
		return file != null && (file.exists() ? file.isDirectory() : file.mkdirs());
	}

	/**
	 * 删除文件
	 */
	public static boolean delete(final File file) {
		if (file == null) {
			return false;
		}
		if (file.isDirectory()) {
			return deleteDir(file);
		}
		return deleteFile(file);
	}

	public static boolean delete(final String filename) {
		File file = new File(filename);
		if (file == null || !file.exists()) {
			return false;
		}
		if (file.isDirectory()) {
			return deleteDir(file);
		}
		return deleteFile(file);
	}

	/**
	 * 删除文件
	 */
	public static boolean deleteFile(final File file) {
		return file != null && (!file.exists() || file.isFile() && file.delete());
	}

	/**
	 * 删除文件夹
	 */
	public static boolean deleteDir(final String dirPath) {
		if (TextUtils.isEmpty(dirPath)) {
			return false;
		}
		return deleteDir(new File(dirPath));
	}

	/**
	 * 删除文件夹
	 */
	public static boolean deleteDir(final File dir) {
		try {
			if (dir == null) {
				return false;
			}
			if (!dir.exists()) {
				return true;
			}
			if (!dir.isDirectory()) {
				return false;
			}
			File[] files = dir.listFiles();
			if (files != null && files.length != 0) {
				for (File file : files) {
					if (file.isFile()) {
						if (!file.delete()) {
							return false;
						}
					} else if (file.isDirectory()) {
						if (!deleteDir(file)) {
							return false;
						}
					}
				}
			}
			return dir.delete();
		} catch (Exception e) {
			Logger.e(TAG, "deleteDir error,", e);
		}
		return false;
	}

	/**
	 * 文件写入信息 测试用，不换行，外部调用换行
	 *
	 * @param filePath
	 * @param fileName
	 * @param msg
	 */
	public static void writeTestMsgToFile(String filePath, String fileName, String msg) {
		File file = mkFile(filePath, fileName);

		if (file != null) {
			try {
				// 后面这个参数代表是不是要接上文件中原来的数据，不进行覆盖
				FileWriter filerWriter = new FileWriter(file, true);
				BufferedWriter bufWriter = new BufferedWriter(filerWriter);
				bufWriter.write(msg);
				//                bufWriter.newLine();
				bufWriter.close();
				filerWriter.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 写入文件
	 *
	 * @param file    文件路径
	 * @param content 文件内容
	 */
	public static boolean writeFile(String file, String content) {
		FileOutputStream _fos = null;
		FileChannel _fc = null;
		try {
			File _file = new File(file);
			if (_file.exists()) {
				// 文件存在
				_file.delete();
			}

			File _parentFile = _file.getParentFile();
			if (null != _parentFile && !_parentFile.exists()) {
				_parentFile.mkdirs();
			}

			_fos = new FileOutputStream(_file);
			_fc = _fos.getChannel();
			ByteBuffer _buffer = StandardCharsets.UTF_8.encode(content);
			while ((_fc.write(_buffer)) != 0) {
				// 写入文件
			}
			return true;

		} catch (Exception e) {
			e.printStackTrace();

		} finally {
			closeSafely(_fc, _fos);
		}
		return false;
	}

	/**
	 * 读文件
	 *
	 * @param file 文件路径
	 * @return 返回文件内容
	 */
	public static String readFile(String file) {
		File _file = new File(file);
		if (!_file.exists()) {
			// 文件不存在
			return null;
		}

		FileInputStream _fis = null;
		FileChannel _fc = null;
		try {
			_fis = new FileInputStream(_file);
			_fc = _fis.getChannel();
			ByteBuffer _bf = ByteBuffer.allocate(1024);
			int _len = -1;
			StringBuilder _sb = new StringBuilder("");
			while ((_len = _fc.read(_bf)) != -1) {
				_bf.clear();
				_sb.append(new String(_bf.array(), 0, _len));
			}

			return _sb.toString();

		} catch (Exception e) {
			e.printStackTrace();

		} finally {
			closeSafely(_fc, _fis);
		}

		return null;
	}

	/**
	 * 下载文件
	 *
	 * @param file 文件本地地址
	 * @param url  服务端地址
	 * @return 是否下载成功，true表示下载成功
	 */
	public static boolean downloadFile(String file, String url) {
		HttpURLConnection _connection = null;
		FileOutputStream _fos = null;
		InputStream _is = null;
		BufferedInputStream _bfi = null;
		try {
			File _patchFile = new File(file);
			if (_patchFile.exists()) {
				// 已存在，删除文件
				_patchFile.delete();
			}

			if (null != _patchFile.getParentFile() && !_patchFile.getParentFile().exists()) {
				_patchFile.getParentFile().mkdirs();
			}

			URL _url = new URL(url);
			_connection = (HttpURLConnection) _url.openConnection();
			_connection.setConnectTimeout(4000);
			_connection.setRequestMethod("GET");
			_connection.setRequestProperty("Charset", "utf-8");
			_connection.connect();

			int _code = _connection.getResponseCode();
			if (_code == HttpURLConnection.HTTP_OK) {
				_is = _connection.getInputStream();
				int _contentLength = _connection.getContentLength();
				_bfi = new BufferedInputStream(_is);

				File _file = new File(file);
				_fos = new FileOutputStream(_file);

				int _len;
				int _total = 0;
				byte[] bytes = new byte[1024];
				while ((_len = _bfi.read(bytes)) != -1) {
					_total += _len;
					_fos.write(bytes, 0, _len);
				}

				return true;
			}

		} catch (Exception e) {
			e.printStackTrace();

		} finally {
			closeSafely(_fos, _is, _bfi);

			if (_connection != null) {
				_connection.disconnect();
			}
		}

		return false;
	}

	/**
	 * 关闭
	 *
	 * @param params 需要关闭的对象
	 */
	public static void closeSafely(Closeable... params) {
		if (null == params || params.length <= 0) {
			return;
		}

		for (Closeable closeable : params) {
			try {
				if (null != closeable) {
					closeable.close();
				}

			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public static void deleteFileOrDirectory(File file) {
		if (!file.exists()) {
			return;
		}
		if (file.isFile()) {
			file.delete();
		}
		if (file.isDirectory()) {
			File[] files = file.listFiles();
			for (File file1 : files) {
				deleteFileOrDirectory(file1);
			}
		}
	}

	public static boolean copyFile(File source, String targetPath, String target) {
		InputStream is = null;
		FileOutputStream fos = null;
		try {
			if (!source.exists()) {
				return false;
			}
			File parent = new File(targetPath);
			if (!parent.exists()) {
				parent.mkdirs();
			}
			File file = new File(parent, target);
			if (file.exists()) {
				return false;
			}
			file.createNewFile();
			is = new FileInputStream(source);
			fos = new FileOutputStream(file);
			byte[] buffer = new byte[1024];
			int byteCount;
			while ((byteCount = is.read(buffer)) != -1) {
				fos.write(buffer, 0, byteCount);
			}
			source.delete();
			return true;
		} catch (Exception e) {
			Logger.e(TAG, "copyFile error", e);
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			if (fos != null) {
				try {
					fos.flush();
					fos.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return false;
	}

	public static boolean copyUsbFile(File source, String targetPath, String target) {
		InputStream is = null;
		FileOutputStream fos = null;
		try {
			if (!source.exists()) {
				return false;
			}
			File parent = new File(targetPath);
			if (!parent.exists()) {
				parent.mkdirs();
			}
			File file = new File(parent, target);
			if (file.exists()) {
				file.delete();
			}
			file = new File(parent, target);
			file.createNewFile();
			is = new FileInputStream(source);
			fos = new FileOutputStream(file);
			byte[] buffer = new byte[1024];
			int byteCount;
			while ((byteCount = is.read(buffer)) != -1) {
				fos.write(buffer, 0, byteCount);
			}
			return true;
		} catch (Exception e) {
			Logger.e(TAG, "copyFile error", e);
		} finally {
			closeSafely(is);
			closeSafely(fos);
		}
		return false;
	}
}

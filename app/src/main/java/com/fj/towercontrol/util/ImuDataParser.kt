package com.fj.towercontrol.util

import android.util.Log
import com.fj.towercontrol.data.entity.ImuData
import okio.Buffer
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.concurrent.Executors

/**
 * 倾角仪数据解析
 *
 * <AUTHOR>
 * @since 2025/8/15
 */
class ImuDataParser(
	private val onDataParsed: (ImuData) -> Unit
) {

	private val executor = Executors.newSingleThreadExecutor()
	private val buffer = Buffer()

	fun addData(data: ByteArray) {
		executor.execute {
			buffer.write(data)
			//处理粘包
			while (buffer.size >= FRAME_LENGTH) {
				val frameBytes = buffer.readByteArray(FRAME_LENGTH.toLong())
				val imuData = parseFrame(frameBytes)
				if (imuData != null) {
					onDataParsed(imuData)
				}
			}
		}
	}

	private fun parseFrame(frame: ByteArray): ImuData? {
		if (frame.size < FRAME_LENGTH) {
			return null
		}

		return with(ByteBuffer.wrap(frame)) {
			val frameInfo = get()
			val dlc = frameInfo.toInt() and 0x0F
			if ((frameInfo.toInt() and 0xF0) != 0x80 || dlc != 8) {
				//不是扩展数据帧，可能是心跳包或者地址声明
				return null
			}

			val canId = getInt()
			val sourceAddress = canId and 0xFF
			if (sourceAddress != IMU_SOURCE_ADDRESS) {
				//不是倾角仪数据
				return null
			}

			val dataPage = (canId ushr 24) and 0x01
			val pduFormat = (canId ushr 16) and 0xFF
			val pduSpecific = (canId ushr 8) and 0xFF
			val pgn = if (pduFormat >= 0xF0) {
				(dataPage shl 16) or (pduFormat shl 8) or pduSpecific
			} else {
				(dataPage shl 16) or (pduFormat shl 8)
			}
//			Log.d(TAG, "dataPage: $dataPage, pduFormat: $pduFormat, pduSpecific: $pduSpecific, pgn: $pgn")
			if (pgn != PGN_SSI2) {
				//不是SSI2数据
				return null
			}
			val payload = slice().order(ByteOrder.LITTLE_ENDIAN)
			val rawPitch = (payload.get(0).toInt() and 0xFF) or
				((payload.get(1).toInt() and 0xFF) shl 8) or
				((payload.get(2).toInt() and 0xFF) shl 16)
			val rawRoll = (payload.get(3).toInt() and 0xFF) or
				((payload.get(4).toInt() and 0xFF) shl 8) or
				((payload.get(5).toInt() and 0xFF) shl 16)
			val pitch = (rawPitch * (1.0 / 32768.0)) - 250.0
			val roll = (rawRoll * (1.0 / 32768.0)) - 250.0
			Log.d(TAG, "pitch: $pitch, roll: $roll")
			ImuData(pitch, roll)
		}
	}

	companion object {
		private const val TAG = "ImuDataParser"
		private const val FRAME_LENGTH = 13
		private const val IMU_SOURCE_ADDRESS = 0x81
		private const val PGN_SSI2 = 0xF029
	}
}

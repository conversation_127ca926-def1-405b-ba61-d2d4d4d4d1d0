package com.fj.towercontrol.util;

/**
 * 耗时任务执行结果包装
 *
 * <AUTHOR>
 */
public class Result<T> {
	private final Status status;
	private final T data;
	private final String errorMsg;

	private Result(Status status, T data, String errorMsg) {
		this.status = status;
		this.data = data;
		this.errorMsg = errorMsg;
	}

	public static <T> Result<T> loading() {
		return new Result<>(Status.LOADING, null, "");
	}

	public static <T> Result<T> success(T data) {
		return new Result<>(Status.SUCCESS, data, "");
	}

	public static <T> Result<T> fail(String errorMsg) {
		return new Result<>(Status.FAIL, null, errorMsg);
	}

	public Status getStatus() {
		return status;
	}

	public T getData() {
		return data;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public enum Status {
		LOADING,
		SUCCESS,
		FAIL
	}
}

package com.fj.towercontrol.util;

import android.annotation.SuppressLint;

import java.lang.reflect.Method;

/**
 * 设备相关工具
 *
 * <AUTHOR>
 */
public class DeviceUtil {

	private DeviceUtil() {
	}

	/**
	 * 获取设备SN
	 *
	 * @return 设备sn, 获取不到时返回"NA"
	 */
	public static String getVehicleSn() {
		return getSysProperty("ro.boot.serialno");
	}

	@SuppressLint("PrivateApi")
	private static String getSysProperty(String key) {
		String value = "NA";
		try {
			Class<?> clazz = Class.forName("android.os.SystemProperties");
			Method method = clazz.getDeclaredMethod("get", String.class);
			value = (String) (method.invoke(clazz, key));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return value;
	}
}

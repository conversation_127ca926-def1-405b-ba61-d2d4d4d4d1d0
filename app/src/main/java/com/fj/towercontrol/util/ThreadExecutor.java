package com.fj.towercontrol.util;

import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadExecutor {
	private static ThreadExecutor mExecutor;
	private static ThreadExecutor mMapExecutor;
	private static ThreadExecutor mAreaExecutor;
	private ThreadPoolExecutor threadPoolExecutor;
	private AtomicInteger atomicInteger = new AtomicInteger();
	public static final int TYPE_NORMAL = 0;
	public static final int TYPE_MAP = 1;

	public static ThreadExecutor getInstance() {
		if (mExecutor == null) {
			synchronized (ThreadExecutor.class) {
				mExecutor = new ThreadExecutor(TYPE_NORMAL, 10, 20);
			}
		}
		return mExecutor;
	}

	public static ThreadExecutor getMapInstance() {
		if (mMapExecutor == null) {
			synchronized (ThreadExecutor.class) {
				mMapExecutor = new ThreadExecutor(TYPE_MAP, 2, 4);
			}
		}
		return mMapExecutor;
	}

	public static ThreadExecutor getAreaInstance() {
		if (mAreaExecutor == null) {
			synchronized (ThreadExecutor.class) {
				mAreaExecutor = new ThreadExecutor(TYPE_NORMAL, 2, 4);
			}
		}
		return mAreaExecutor;
	}

	ThreadExecutor(int type, int coreSize, int poolSize) {
		threadPoolExecutor =
			new ThreadPoolExecutor(
				coreSize,
				poolSize,
				10,
				TimeUnit.MILLISECONDS,
				new LinkedBlockingDeque<Runnable>(),
				new ThreadFactory() {
					@Override
					public Thread newThread(Runnable r) {
						Thread thread =
							new Thread(
								r,
								"ThreadExecutor-"
									+ atomicInteger.incrementAndGet());
						if (type == TYPE_MAP) {
							thread.setPriority(Thread.MAX_PRIORITY);
						} else {
							thread.setPriority(Thread.NORM_PRIORITY);
						}
						return thread;
					}
				},
				new ThreadPoolExecutor.DiscardOldestPolicy());
	}

	public void executor(Runnable runnable) {
		threadPoolExecutor.execute(runnable);
		//
		// LogUtil.d("TAG",threadPoolExecutor.getActiveCount()+","+threadPoolExecutor.getCorePoolSize()+","+threadPoolExecutor.getPoolSize()+","+threadPoolExecutor.getQueue().size());
	}

	public void delete(Runnable runnable) {
	}
}

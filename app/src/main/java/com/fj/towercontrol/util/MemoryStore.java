package com.fj.towercontrol.util;

import com.fj.fjprotocol.data.GeoData;
import com.fj.towercontrol.data.entity.AbsoluteEncoderData;
import com.fj.towercontrol.data.entity.BatteryInfo;
import com.fj.towercontrol.data.entity.HistoryPoint;
import com.fj.towercontrol.data.entity.SemiautomaticTask;
import com.fj.towercontrol.data.entity.SystemConfig;
import com.fj.towercontrol.data.entity.TowerConfig;
import com.fj.towercontrol.data.entity.TowerCraneData;
import com.fj.towercontrol.data.entity.WeatherStationData;

import java.util.ArrayList;
import java.util.List;

/**
 * 内存缓存
 *
 * <AUTHOR>
 */
public class MemoryStore {
	/**
	 * 采集模式：A->B
	 */
	public static final int COLLECT_MODE_A_TO_B = 0;
	/**
	 * 采集模式：B->A
	 */
	public static final int COLLECT_MODE_B_TO_A = 1;
	/**
	 * 采集状态：空闲
	 */
	public static final int COLLECT_STATUS_IDLE = 0;
	/**
	 * 采集状态：采集中
	 */
	public static final int COLLECT_STATUS_COLLECTING = 1;
	/**
	 * 轨迹点集合
	 */
	private final List<HistoryPoint> historyPointList = new LimitedArrayList<>(2_000);
	/**
	 * 当前任务id
	 */
	private String taskId;
	/**
	 * 任务采集 1：路径开始采集 2：卸料点采集 3：路径结束采集
	 */
	private int pathControlCommand;
	/**
	 * 当前塔顶中心点校准命令<br>
	 * 1:开始<br>
	 * 2:结束
	 */
	private int towerCalibrationCommand;
	/**
	 * 结束校准命令的iot messageId
	 */
	private String towerCalibrationMessageId;
	/**
	 * 读取到的吊钩电池信息
	 */
	private BatteryInfo batteryInfo;
	/**
	 * 大屏锁屏状态<br>
	 * 0:锁屏<br>
	 * 1:未锁屏<br>
	 */
	private int loginStatus;
	/**
	 * 登录用户uid
	 */
	private Long uid;
	/**
	 * 从塔机读取到的数据
	 */
	private TowerCraneData towerCraneData;
	/**
	 * ecu上报的按键状态，存下来用于对比下一次数据更新时判断按键有没有发生变化
	 */
	private List<Integer> buttons = new ArrayList<>();
	/**
	 * 底部雷达据吊物距离
	 */
	private double radarDistance;
	/**
	 * 雷达起始过滤角度
	 */
	private double radarStartAngle;
	/**
	 * 雷达截止过滤角度
	 */
	private double radarEndAngle;
	/**
	 * 环向雷达安装时的倾斜角度
	 */
	private double radarInstallAngle;
	/**
	 * 吊物半径
	 */
	private double cargoRadius;
	/**
	 * 吊钩离地高度
	 */
	private double hookHeight;
	/**
	 * 吊钩据地面距离与障碍物距地面距离高程查阈值
	 */
	private double groundHeightThreshold;
	/**
	 * 吊物与障碍物水平距离阈值
	 */
	private double liftHorizontalThreshold;
	/**
	 * 吊物与障碍物竖直方向距离阈值
	 */
	private double liftVerticalThreshold;
	/**
	 * 四周雷达到吊物垂直距离
	 */
	private double radarToLiftDistance;
	/**
	 * 当前轨迹采集状态
	 * <p>
	 * 0:未开始<br/>
	 * 1:采集中<br/>
	 */
	private int pathCollectStatus;
	/**
	 * 当前轨迹采集模式
	 * <p>
	 * 0:A->B<br/>
	 * 1:B->A<br/>
	 */
	private int pathCollectMode;
	/**
	 * 塔上控制信号
	 */
	private Boolean cutoff;
	/**
	 * 风标指示灯信号
	 */
	private Boolean lightOn;
	/**
	 * 气象站数据
	 */
	private WeatherStationData weatherStationData;
	/**
	 * 智能吊钩定位
	 */
	private GeoData hookPos;
	/**
	 * 当前吊钩高程与大地水准面差距
	 */
	private double hookGeoidalHeight;
	/**
	 * 上报的吊钩椭球高度
	 */
	private double uploadHookAltitude;
	/**
	 * 发生333告警时是否限控
	 */
	private boolean limitControlBy333;

	private SystemConfig systemConfig;
	/**
	 * 判定安全帽数据是否有效的距吊钩最远距离
	 */
	private double helmetValidRange;
	/**
	 * 平台配置的塔吊属性
	 */
	private TowerConfig towerConfig;
	/**
	 * 临时存储的半自动吊运任务，仅用于本地ecu调试
	 */
	private SemiautomaticTask semiautomaticTask;
	private AbsoluteEncoderData absoluteEncoderData;

	private MemoryStore() {
		radarDistance = FileStore.getRadarDistanceLimit();
		radarStartAngle = FileStore.getRadarStartAngle();
		radarEndAngle = FileStore.getRadarEndAngle();
		cargoRadius = FileStore.getCargoRadius();
		radarInstallAngle = FileStore.getRadarInstallAngle();
		groundHeightThreshold = FileStore.getGroundHeightThreshold();
		liftHorizontalThreshold = FileStore.getLiftHorizontalThreshold();
		liftVerticalThreshold = FileStore.getLiftVerticalThreshold();
		radarToLiftDistance = FileStore.getRadarToLiftDistance();
		limitControlBy333 = FileStore.getLimitControlBy333();
		systemConfig = FileStore.getSystemConfig();
		helmetValidRange = FileStore.getHelmetValidRange();
		towerConfig = FileStore.getTowerConfig();
	}

	private static class SingletonHolder {
		private static final MemoryStore INSTANCE = new MemoryStore();
	}

	public static MemoryStore getInstance() {
		return MemoryStore.SingletonHolder.INSTANCE;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public int getPathControlCommand() {
		return pathControlCommand;
	}

	public void setPathControlCommand(int pathControlCommand) {
		this.pathControlCommand = pathControlCommand;
	}

	public BatteryInfo getBatteryInfo() {
		return batteryInfo;
	}

	public void setBatteryInfo(BatteryInfo batteryInfo) {
		this.batteryInfo = batteryInfo;
	}

	public int getLoginStatus() {
		return loginStatus;
	}

	public void setLoginStatus(int loginStatus) {
		this.loginStatus = loginStatus;
	}

	public TowerCraneData getTowerCraneData() {
		return towerCraneData;
	}

	public void setTowerCraneData(TowerCraneData towerCraneData) {
		this.towerCraneData = towerCraneData;
	}

	public int getTowerCalibrationCommand() {
		return towerCalibrationCommand;
	}

	public void setTowerCalibrationCommand(int towerCalibrationCommand) {
		this.towerCalibrationCommand = towerCalibrationCommand;
	}

	public String getTowerCalibrationMessageId() {
		return towerCalibrationMessageId;
	}

	public void setTowerCalibrationMessageId(String towerCalibrationMessageId) {
		this.towerCalibrationMessageId = towerCalibrationMessageId;
	}

	public List<Integer> getButtons() {
		return buttons;
	}

	public void setButtons(List<Integer> buttons) {
		this.buttons = buttons;
	}

	public double getRadarDistance() {
		return radarDistance;
	}

	public void setRadarDistance(double radarDistance) {
		this.radarDistance = radarDistance;
	}

	public double getRadarStartAngle() {
		return radarStartAngle;
	}

	public void setRadarStartAngle(double radarStartAngle) {
		this.radarStartAngle = radarStartAngle;
	}

	public double getRadarEndAngle() {
		return radarEndAngle;
	}

	public void setRadarEndAngle(double radarEndAngle) {
		this.radarEndAngle = radarEndAngle;
	}

	public int getRadarInstallAngle() {
		return (int) radarInstallAngle;
	}

	public void setRadarInstallAngle(double radarInstallAngle) {
		this.radarInstallAngle = radarInstallAngle;
	}

	public double getCargoRadius() {
		return cargoRadius;
	}

	public void setCargoRadius(double cargoRadius) {
		this.cargoRadius = cargoRadius;
	}

	public double getHookHeight() {
		return hookHeight;
	}

	public void setHookHeight(double hookHeight) {
		this.hookHeight = hookHeight;
	}

	public double getGroundHeightThreshold() {
		return groundHeightThreshold;
	}

	public void setGroundHeightThreshold(double groundHeightThreshold) {
		this.groundHeightThreshold = groundHeightThreshold;
	}

	public double getLiftHorizontalThreshold() {
		return liftHorizontalThreshold;
	}

	public void setLiftHorizontalThreshold(double liftHorizontalThreshold) {
		this.liftHorizontalThreshold = liftHorizontalThreshold;
	}

	public double getLiftVerticalThreshold() {
		return liftVerticalThreshold;
	}

	public void setLiftVerticalThreshold(double liftVerticalThreshold) {
		this.liftVerticalThreshold = liftVerticalThreshold;
	}

	public double getRadarToLiftDistance() {
		return radarToLiftDistance;
	}

	public void setRadarToLiftDistance(double radarToLiftDistance) {
		this.radarToLiftDistance = radarToLiftDistance;
	}

	public int getPathCollectStatus() {
		return pathCollectStatus;
	}

	public void setPathCollectStatus(int pathCollectStatus) {
		this.pathCollectStatus = pathCollectStatus;
	}

	public int getPathCollectMode() {
		return pathCollectMode;
	}

	public void setPathCollectMode(int pathCollectMode) {
		this.pathCollectMode = pathCollectMode;
	}

	public List<HistoryPoint> getHistoryPointList() {
		return historyPointList;
	}

	public Boolean getCutoff() {
		return cutoff;
	}

	public void setCutoff(Boolean cutoff) {
		this.cutoff = cutoff;
	}

	public Boolean getLightOn() {
		return lightOn;
	}

	public void setLightOn(Boolean lightOn) {
		this.lightOn = lightOn;
	}

	public WeatherStationData getWeatherStationData() {
		return weatherStationData;
	}

	public void setWeatherStationData(WeatherStationData weatherStationData) {
		this.weatherStationData = weatherStationData;
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public GeoData getHookPos() {
		return hookPos;
	}

	public void setHookPos(GeoData hookPos) {
		this.hookPos = hookPos;
	}

	public double getHookGeoidalHeight() {
		return hookGeoidalHeight;
	}

	public void setHookGeoidalHeight(double hookGeoidalHeight) {
		this.hookGeoidalHeight = hookGeoidalHeight;
	}

	public double getUploadHookAltitude() {
		return uploadHookAltitude;
	}

	public void setUploadHookAltitude(double uploadHookAltitude) {
		this.uploadHookAltitude = uploadHookAltitude;
	}

	public SystemConfig getSystemConfig() {
		return systemConfig;
	}

	public void setSystemConfig(SystemConfig systemConfig) {
		this.systemConfig = systemConfig;
	}

	public boolean isLimitControlBy333() {
		return limitControlBy333;
	}

	public void setLimitControlBy333(boolean limitControlBy333) {
		this.limitControlBy333 = limitControlBy333;
	}

	public double getHelmetValidRange() {
		return helmetValidRange;
	}

	public void setHelmetValidRange(double helmetValidRange) {
		this.helmetValidRange = helmetValidRange;
	}

	public TowerConfig getTowerConfig() {
		return towerConfig;
	}

	public void setTowerConfig(TowerConfig towerConfig) {
		this.towerConfig = towerConfig;
	}

	public SemiautomaticTask getSemiautomaticTask() {
		return semiautomaticTask;
	}

	public void setSemiautomaticTask(SemiautomaticTask semiautomaticTask) {
		this.semiautomaticTask = semiautomaticTask;
	}

	public AbsoluteEncoderData getAbsoluteEncoderData() {
		return absoluteEncoderData;
	}

	public void setAbsoluteEncoderData(AbsoluteEncoderData absoluteEncoderData) {
		this.absoluteEncoderData = absoluteEncoderData;
	}
}

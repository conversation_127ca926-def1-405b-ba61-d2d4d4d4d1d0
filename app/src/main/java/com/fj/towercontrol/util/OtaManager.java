package com.fj.towercontrol.util;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.CloseUtils;
import com.blankj.utilcode.util.FileUtils;
import com.fj.fjprotocol.ProtocolConstants;
import com.fj.fjprotocol.ProtocolHelper;
import com.fj.fjprotocol.TransManager;
import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.Logger;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * OTA升级管理类
 *
 * <AUTHOR>
 */
public class OtaManager {
	private static final String TAG = "OtaManager";
	/**
	 * 升级类型-ecu
	 */
	private static final byte OTA_TYPE_ECU = 0x01;
	/**
	 * 分包大小256字节
	 */
	private static final int FILE_PART_SIZE = 256;
	/**
	 * MSG key - ecu回复OTA升级成功
	 */
	private static final int MSG_OTA_RESULT_SUCCESS = 1000;
	/**
	 * MSG key - ecu回复OTA升级失败
	 */
	private static final int MSG_OTA_RESULT_FAILED = 1001;
	/**
	 * MSG key - 请求文件信息超时
	 */
	private static final int MSG_REQUEST_FILE_INFO_TIMEOUT = 1002;
	/**
	 * MSG key - 请求分片超时
	 */
	private static final int MSG_REQUEST_SPLIT_FILE_TIMEOUT = 1003;
	/**
	 * MSG key - ecu上报OTA结果超时
	 */
	private static final int MSG_OTA_RESULT_TIMEOUT = 1004;
	private final List<byte[]> splitFileList = new ArrayList<>();
	private int totalFileSize = 0;
	private final Handler handler;
	private String filepath;
	private OtaCallback callback;

	/**
	 * 开始ecu ota升级
	 *
	 * @param filepath ecu升级文件路径
	 */
	public void startEcuUpgrade(String filepath, OtaCallback otaCallback) {
		this.filepath = filepath;
		this.callback = otaCallback;
		ThreadExecutor.getInstance().executor(() -> {
			if (callback != null) {
				callback.onStart();
			}
			SystemClock.sleep(1_000);
			TransManager.getInstance().sendOtaData(ProtocolHelper.packRequestOta(OTA_TYPE_ECU));
		});
	}

	/**
	 * 处理ECU回复的OTA消息
	 *
	 * @param data data
	 */
	public void handleEcuResponse(byte[] data) {
		if (data[0] == ProtocolConstants.OTA_START_UPGRADE) {
			//可以开始升级，等待ecu继续请求文件信息
			Logger.i(TAG, "receive ecu start upgrade");
			handler.sendEmptyMessageDelayed(MSG_REQUEST_FILE_INFO_TIMEOUT, 30_000);
		} else if (data[0] == ProtocolConstants.OTA_FILE_INFO) {
			Logger.i(TAG, "receive ecu request file info");
			handler.removeMessages(MSG_REQUEST_FILE_INFO_TIMEOUT);
			handler.removeMessages(MSG_REQUEST_SPLIT_FILE_TIMEOUT);
			//进行文件拆分
			splitOtaFile();
			//发送文件分包信息
			TransManager.getInstance().sendOtaData(ProtocolHelper.packOtaFileInfo(OTA_TYPE_ECU, totalFileSize, FILE_PART_SIZE, FileUtils.getFileMD5(filepath)));
		} else if (data[0] == ProtocolConstants.OTA_SEND_FILE) {
			handler.removeMessages(MSG_REQUEST_SPLIT_FILE_TIMEOUT);
			//发送拆分的文件
			int reqIndex = DataUtil.byte2int2(DataUtil.subBytes(data, 2, 2));
			Logger.i(TAG, "receive ecu request file slice: " + reqIndex);
			byte[] fileBytes = splitFileList.get(reqIndex);
			TransManager.getInstance().sendOtaData(ProtocolHelper.packSplitOtaFile(OTA_TYPE_ECU, reqIndex, fileBytes));
			if (reqIndex >= splitFileList.size() - 1) {
				//所有分片发送完毕，等待OTA结果回复
				handler.sendEmptyMessageDelayed(MSG_OTA_RESULT_TIMEOUT, 10_000);
			} else {
				//继续等待下一个分片请求
				handler.sendEmptyMessageDelayed(MSG_REQUEST_SPLIT_FILE_TIMEOUT, 5_000);
			}
		} else if (data[0] == ProtocolConstants.OTA_RESULT) {
			//收到OTA结果
			handler.removeMessages(MSG_REQUEST_FILE_INFO_TIMEOUT);
			handler.removeMessages(MSG_REQUEST_SPLIT_FILE_TIMEOUT);
			handler.removeMessages(MSG_OTA_RESULT_TIMEOUT);
			int otaRet = DataUtil.byte2int2(DataUtil.subBytes(data, 2, 2));
			Logger.i(TAG, "receive ecu ota result: " + otaRet);
			if (otaRet == 1) {
				//OTA升级成功
				handler.removeMessages(MSG_OTA_RESULT_SUCCESS);
				handler.sendEmptyMessage(MSG_OTA_RESULT_SUCCESS);
				if (callback != null) {
					callback.onComplete();
				}
			} else if (otaRet == 4) {
				//固件包交换完成，5s后没收到1也算成功
				handler.sendEmptyMessageDelayed(MSG_OTA_RESULT_SUCCESS, 5_000);
			} else {
				handler.sendEmptyMessage(MSG_OTA_RESULT_FAILED);
			}
		}
	}

	private void splitOtaFile() {
		splitFileList.clear();
		totalFileSize = 0;
		File file = new File(filepath);
		FileInputStream fis = null;
		try {
			fis = new FileInputStream(file);
			byte[] buffer = new byte[FILE_PART_SIZE];
			int bytesRead;
			while ((bytesRead = fis.read(buffer)) != -1) {
				totalFileSize += bytesRead;
				if (bytesRead == FILE_PART_SIZE) {
					splitFileList.add(buffer);
				} else {
					byte[] lastChunk = new byte[bytesRead];
					System.arraycopy(buffer, 0, lastChunk, 0, bytesRead);
					splitFileList.add(lastChunk);
				}
				buffer = new byte[FILE_PART_SIZE];
			}
			Logger.i(TAG, "splitOtaFile result: count -> " + splitFileList.size() + ", totalFileSize -> " + totalFileSize);
		} catch (IOException e) {
			Logger.e(TAG, "splitOtaFile exception: " + e.getMessage());
		} finally {
			CloseUtils.closeIOQuietly(fis);
		}
	}

	private OtaManager() {
		handler = new Handler(Looper.getMainLooper()) {
			@Override
			public void handleMessage(@NonNull Message msg) {
				super.handleMessage(msg);
				switch (msg.what) {
					case MSG_OTA_RESULT_SUCCESS:
						if (callback != null) {
							callback.onComplete();
						}
						break;
					case MSG_OTA_RESULT_FAILED:
						if (callback != null) {
							callback.onError("OTA回复升级结果异常");
						}
						break;
					case MSG_REQUEST_FILE_INFO_TIMEOUT:
						if (callback != null) {
							callback.onError("ecu请求文件信息超时");
						}
						break;
					case MSG_REQUEST_SPLIT_FILE_TIMEOUT:
						if (callback != null) {
							callback.onError("ecu请求文件分片超时");
						}
						break;
					case MSG_OTA_RESULT_TIMEOUT:
						if (callback != null) {
							callback.onError("ecu升级结果回复超时");
						}
						break;
				}
			}
		};
	}

	private static class SingletonHolder {
		private static final OtaManager INSTANCE = new OtaManager();
	}

	public static OtaManager getInstance() {
		return OtaManager.SingletonHolder.INSTANCE;
	}

	public interface OtaCallback {
		void onStart();

		void onComplete();

		void onError(String msg);
	}
}

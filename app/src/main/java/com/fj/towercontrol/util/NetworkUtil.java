package com.fj.towercontrol.util;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.telephony.TelephonyManager;

import com.fjdynamics.app.logger.Logger;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

public class NetworkUtil {
	public static final int NETWORK_NONE = 0; // 没有网络连接
	public static final int NETWORK_WIFI = 1; // wifi连接
	public static final int NETWORK_2G = 2; // 2G
	public static final int NETWORK_3G = 3; // 3G
	public static final int NETWORK_4G = 4; // 4G
	public static final int NETWORK_MOBILE = 5; // 手机流量

	/**
	 * 获取当前网络连接的类型
	 *
	 * @param context context
	 * @return int
	 */
	public static int getNetworkState(Context context) {
		ConnectivityManager connManager =
			(ConnectivityManager)
				context.getSystemService(Context.CONNECTIVITY_SERVICE); // 获取网络服务
		if (null == connManager) { // 为空则认为无网络
			return NETWORK_NONE;
		}
		// 获取网络类型，如果为空，返回无网络
		NetworkInfo activeNetInfo = connManager.getActiveNetworkInfo();
		if (activeNetInfo == null || !activeNetInfo.isAvailable()) {
			return NETWORK_NONE;
		}
		// 判断是否为WIFI
		NetworkInfo wifiInfo = connManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
		if (null != wifiInfo) {
			NetworkInfo.State state = wifiInfo.getState();
			if (null != state) {
				if (state == NetworkInfo.State.CONNECTED || state == NetworkInfo.State.CONNECTING) {
					return NETWORK_WIFI;
				}
			}
		}
		// 若不是WIFI，则去判断是2G、3G、4G网
		TelephonyManager telephonyManager =
			(TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
		int networkType = telephonyManager.getNetworkType();
		switch (networkType) {
                /*
                GPRS : 2G(2.5) General Packet Radia Service 114kbps
                EDGE : 2G(2.75G) Enhanced Data Rate for GSM Evolution 384kbps
                UMTS : 3G WCDMA 联通3G Universal Mobile Telecommunication System 完整的3G移动通信技术标准
                CDMA : 2G 电信 Code Division Multiple Access 码分多址
                EVDO_0 : 3G (EVDO 全程 CDMA2000 1xEV-DO) Evolution - Data Only (Data Optimized) 153.6kps - 2.4mbps 属于3G
                EVDO_A : 3G 1.8mbps - 3.1mbps 属于3G过渡，3.5G
                1xRTT : 2G CDMA2000 1xRTT (RTT - 无线电传输技术) 144kbps 2G的过渡,
                HSDPA : 3.5G 高速下行分组接入 3.5G WCDMA High Speed Downlink Packet Access 14.4mbps
                HSUPA : 3.5G High Speed Uplink Packet Access 高速上行链路分组接入 1.4 - 5.8 mbps
                HSPA : 3G (分HSDPA,HSUPA) High Speed Packet Access
                IDEN : 2G Integrated Dispatch Enhanced Networks 集成数字增强型网络 （属于2G，来自维基百科）
                EVDO_B : 3G EV-DO Rev.B 14.7Mbps 下行 3.5G
                LTE : 4G Long Term Evolution FDD-LTE 和 TDD-LTE , 3G过渡，升级版 LTE Advanced 才是4G
                EHRPD : 3G CDMA2000向LTE 4G的中间产物 Evolved High Rate Packet Data HRPD的升级
                HSPAP : 3G HSPAP 比 HSDPA 快些
                */
			// 2G网络
			case TelephonyManager.NETWORK_TYPE_GPRS:
			case TelephonyManager.NETWORK_TYPE_CDMA:
			case TelephonyManager.NETWORK_TYPE_EDGE:
			case TelephonyManager.NETWORK_TYPE_1xRTT:
			case TelephonyManager.NETWORK_TYPE_IDEN:
				return NETWORK_2G;
			// 3G网络
			case TelephonyManager.NETWORK_TYPE_EVDO_A:
			case TelephonyManager.NETWORK_TYPE_UMTS:
			case TelephonyManager.NETWORK_TYPE_EVDO_0:
			case TelephonyManager.NETWORK_TYPE_HSDPA:
			case TelephonyManager.NETWORK_TYPE_HSUPA:
			case TelephonyManager.NETWORK_TYPE_HSPA:
			case TelephonyManager.NETWORK_TYPE_EVDO_B:
			case TelephonyManager.NETWORK_TYPE_EHRPD:
			case TelephonyManager.NETWORK_TYPE_HSPAP:
				return NETWORK_3G;
			// 4G网络
			case TelephonyManager.NETWORK_TYPE_LTE:
				return NETWORK_4G;
			default:
				return NETWORK_MOBILE;
		}
	}

	public static String getNetWorkState(Context context) {
		String state = "";
		switch (NetworkUtil.getNetworkState(context)) {
			case NetworkUtil.NETWORK_WIFI:
				state = "WIFI";
				break;
			default:
				state = "";
				break;
			case NetworkUtil.NETWORK_2G:
				state = "2G";
				break;
			case NetworkUtil.NETWORK_3G:
				state = "3G";
				break;
			case NetworkUtil.NETWORK_4G:
				state = "4G";
				break;
		}
		return state;
	}

	/**
	 * 判断网络是否可用
	 */
	public static boolean isNetworkAvailable(Context context) {
		boolean isConnect = false;
		try {
			ConnectivityManager connectivityManager =
				(ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
			if (connectivityManager != null) {
				// 获取网络连接管理的对象
				NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
				if (networkInfo != null && networkInfo.isConnected()) {
					if (networkInfo.getState() == NetworkInfo.State.CONNECTED) {
						isConnect = true;
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return isConnect;
	}

	/**
	 * 获取当前的IP地址
	 *
	 * @param context
	 * @return
	 */
	public static String getCurrentIpAddress(Context context) {
		int state = getNetworkState(context);
		if (state == NETWORK_NONE) {
			return "";
		}
		if (state == NETWORK_WIFI) {
			return getLocalWifiIpAddress(context);
		} else {
			return getLocalMobileIpAddress();
		}
	}

	/**
	 * 将ip的整数形式转换成ip形式
	 *
	 * @param ipInt
	 * @return
	 */
	public static String int2ip(int ipInt) {

		StringBuilder sb = new StringBuilder();
		sb.append(ipInt & 0xFF).append(".");
		sb.append((ipInt >> 8) & 0xFF).append(".");
		sb.append((ipInt >> 16) & 0xFF).append(".");
		sb.append((ipInt >> 24) & 0xFF);
		return sb.toString();
	}

	/**
	 * 获取当前ip地址
	 *
	 * @param context
	 * @return
	 */
	public static String getLocalWifiIpAddress(Context context) {
		try {
			WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
			WifiInfo wifiInfo = wifiManager.getConnectionInfo();
			int i = wifiInfo.getIpAddress();
			return int2ip(i);
		} catch (Exception ex) {
			Logger.e(
				context.getClass().getSimpleName(),
				" 获取IP出错鸟!!!!请保证是WIFI,或者请重新打开网络!\n" + ex.getMessage());
		}
		return "";
	}

	// GPRS连接下的ip
	public static String getLocalMobileIpAddress() {
		try {
			for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces();
					 en.hasMoreElements(); ) {
				NetworkInterface intf = en.nextElement();
				for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses();
						 enumIpAddr.hasMoreElements(); ) {
					InetAddress inetAddress = enumIpAddr.nextElement();
					if (!inetAddress.isLoopbackAddress()) {
						return inetAddress.getHostAddress().toString();
					}
				}
			}
		} catch (SocketException ex) {
			Logger.e("WifiPreference IpAddress", ex.toString());
		}
		return null;
	}
}

package com.fj.towercontrol.util;

import static com.fj.fjprotocol.ProtocolConstants.DISCRETE_COMMAND;
import static com.fj.fjprotocol.ProtocolConstants.SEND_HISTORY_POINTS;

import android.os.Environment;

import androidx.annotation.WorkerThread;

import com.blankj.utilcode.util.CloseUtils;
import com.blankj.utilcode.util.FileUtils;
import com.fj.fjprotocol.TowerDataFormatter;
import com.fj.towercontrol.data.entity.HistoryPoint;
import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.Logger;
import com.opencsv.bean.CsvToBeanBuilder;
import com.opencsv.bean.StatefulBeanToCsv;
import com.opencsv.bean.StatefulBeanToCsvBuilder;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import com.opencsv.exceptions.CsvRequiredFieldEmptyException;

import org.apache.commons.collections4.CollectionUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.util.List;

/**
 * csv文件操作类
 *
 * <AUTHOR>
 */
public class CsvUtil {

	private static final String TAG = "CsvUtil";
	private static final String ROOT_DIR = Environment.getExternalStorageDirectory().getAbsolutePath() + "/tower/csv/";

	/**
	 * 轨迹文件是否存在
	 *
	 * @param taskId 任务id
	 * @return 是否存在
	 */
	public static boolean fileExist(String taskId) {
		File csvFile = new File(ROOT_DIR + taskId + ".csv");
		return csvFile.isFile();
	}

	/**
	 * 读取作业轨迹文件
	 *
	 * @param taskId 调运任务id
	 * @return 轨迹点集合
	 */
	@WorkerThread
	public static List<HistoryPoint> readCsvFile(String taskId) {
		File csvFile = new File(ROOT_DIR + taskId + ".csv");
		if (!csvFile.exists()) {
			return null;
		}
		try {
			return new CsvToBeanBuilder<HistoryPoint>(new FileReader(csvFile)).withType(HistoryPoint.class).build().parse();
		} catch (FileNotFoundException e) {
			Logger.e(TAG, "readCsvFile exception: " + e.getMessage());
		}
		return null;
	}

	/**
	 * 生成csv历史轨迹文件
	 *
	 * @param taskId uuid文件名
	 * @param data   历史轨迹集合
	 * @return 是否生成成功
	 */
	@WorkerThread
	public static boolean writeCsvFile(String taskId, List<HistoryPoint> data) {
		if (CollectionUtils.isEmpty(data)) {
			Logger.e(TAG, "writeCsvFile data is empty");
			return false;
		}
		File csvFile = new File(ROOT_DIR + taskId + ".csv");
		Writer writer = null;
		try {
			FileUtils.createFileByDeleteOldFile(csvFile);
			writer = new FileWriter(csvFile);
			StatefulBeanToCsv<HistoryPoint> beanToCsv = new StatefulBeanToCsvBuilder<HistoryPoint>(writer).build();
			beanToCsv.write(data);
			return true;
		} catch (IOException | CsvDataTypeMismatchException | CsvRequiredFieldEmptyException e) {
			Logger.e(TAG, "writeCsvFile exception: " + e.getMessage());
		} finally {
			CloseUtils.closeIOQuietly(writer);
		}
		return false;
	}

	/**
	 * 保存塔顶中心点校准轨迹
	 *
	 * @param data 轨迹列表
	 * @return 保存成功还是失败
	 */
	@WorkerThread
	public static boolean saveCalibrationPoints(List<HistoryPoint> data) {
		if (CollectionUtils.isEmpty(data)) {
			Logger.e(TAG, "writeCsvFile data is empty");
			return false;
		}
		File csvFile = new File(ROOT_DIR + "calibration/track.csv");
		Writer writer = null;
		try {
			FileUtils.createFileByDeleteOldFile(csvFile);
			writer = new FileWriter(csvFile);
			StatefulBeanToCsv<HistoryPoint> beanToCsv = new StatefulBeanToCsvBuilder<HistoryPoint>(writer).build();
			beanToCsv.write(data);
			return true;
		} catch (IOException | CsvDataTypeMismatchException | CsvRequiredFieldEmptyException e) {
			Logger.e(TAG, "writeCsvFile exception: " + e.getMessage());
		} finally {
			CloseUtils.closeIOQuietly(writer);
		}
		return false;
	}

	/**
	 * 读取塔顶中心点标定轨迹文件
	 *
	 * @return 轨迹点集合
	 */
	@WorkerThread
	public static List<HistoryPoint> readCalibrationFile() {
		File csvFile = new File(ROOT_DIR + "calibration/track.csv");
		if (!csvFile.exists()) {
			return null;
		}
		try {
			return new CsvToBeanBuilder<HistoryPoint>(new FileReader(csvFile)).withType(HistoryPoint.class).build().parse();
		} catch (FileNotFoundException e) {
			Logger.e(TAG, "readCsvFile exception: " + e.getMessage());
		}
		return null;
	}

	/**
	 * 封装历史轨迹点位协议包
	 *
	 * @param historyPointList 历史轨迹集合
	 * @param totalLength      所有历史轨迹总长度
	 * @param currentIndex     当前索引
	 * @return bytes
	 */
	public static byte[] convertHistoryPointListToBytes(List<HistoryPoint> historyPointList, int totalLength, int currentIndex) {
		if (historyPointList.isEmpty()) {
			return null;
		}
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SEND_HISTORY_POINTS};
		int latLen = 8;
		int lngLen = 8;
		int heightLen = 4;
		int hookGearLen = 1;
		int trolleyGearLen = 1;
		int spinGearLen = 1;
		byte[] cmdDataBytes = new byte[2 + 2 + 2 + (latLen + lngLen + heightLen + hookGearLen + trolleyGearLen + spinGearLen) * historyPointList.size()];

		int index = 0;
		// 点阵总长度
		byte[] totalLengthBytes = DataUtil.int2byte2(totalLength);
		System.arraycopy(totalLengthBytes, 0, cmdDataBytes, index, totalLengthBytes.length);
		index += 2;
		// 当前下发起始点位
		byte[] currentIndexBytes = DataUtil.int2byte2(currentIndex);
		System.arraycopy(currentIndexBytes, 0, cmdDataBytes, index, currentIndexBytes.length);
		index += 2;
		// 当前下发结束点位
		int endIndex = currentIndex + historyPointList.size() - 1;
		byte[] endIndexBytes = DataUtil.int2byte2(endIndex);
		System.arraycopy(endIndexBytes, 0, cmdDataBytes, index, endIndexBytes.length);
		index += 2;

		for (int i = 0; i < historyPointList.size(); i++) {
			HistoryPoint geoData = historyPointList.get(i);
			byte[] latBytes = DataUtil.longToByteArray((long) (geoData.getLat() * Math.pow(10, 10)));
			System.arraycopy(latBytes, 0, cmdDataBytes, index, latLen);
			index += latLen;
			byte[] lngBytes = DataUtil.longToByteArray((long) (geoData.getLng() * Math.pow(10, 10)));
			System.arraycopy(lngBytes, 0, cmdDataBytes, index, lngLen);
			index += lngLen;
			byte[] heightBytes = DataUtil.int2byte((int) (geoData.getAlt() * 10_000));
			System.arraycopy(heightBytes, 0, cmdDataBytes, index, heightLen);
			index += heightLen;
			byte[] hookGearBytes = new byte[]{(byte) ((int) geoData.getHookGear())};
			System.arraycopy(hookGearBytes, 0, cmdDataBytes, index, hookGearLen);
			index += hookGearLen;
			byte[] trolleyGearBytes = new byte[]{(byte) ((int) geoData.getTrolleyGear())};
			System.arraycopy(trolleyGearBytes, 0, cmdDataBytes, index, trolleyGearLen);
			index += trolleyGearLen;
			byte[] spinGearBytes = new byte[]{(byte) ((int) geoData.getSpinGear())};
			System.arraycopy(spinGearBytes, 0, cmdDataBytes, index, spinGearLen);
			index += spinGearLen;
		}

		byte[] reqDataDM = DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}
}

package com.fj.towercontrol.util;


/**
 * 四元数与欧拉角互转
 *
 * <AUTHOR>
 * @date 2023-09-22 09:58
 **/
public class EulerAngle2QuatUtil {

	/**
	 * 归一化
	 *
	 * @param x
	 * @param y
	 * @param z
	 * @param w
	 * @return
	 */
	public static Quaternion normalizeQuaternion(Quaternion q) {
		float w = q.w;
		float x = q.x;
		float y = q.y;
		float z = q.z;
		return normalizeQuaternion(w, x, y, z);
	}

	/**
	 * 归一化
	 *
	 * @param x
	 * @param y
	 * @param z
	 * @param w
	 * @return
	 */
	public static Quaternion normalizeQuaternion(float w, float x, float y, float z) {
		double lengthD = 1.0f / (w * w + x * x + y * y + z * z);
		w *= lengthD;
		x *= lengthD;
		y *= lengthD;
		z *= lengthD;
		return new Quaternion(w, x, y, z);
	}

	/**
	 * Slerp球面线性插值（Spherical Linear Interpolation）
	 *
	 * @param a 原始数据a
	 * @param b 原始数据b
	 * @param t 要插值的比例（中间插一个值1/2）
	 * @return
	 */
	public static Quaternion makeInterpolated(Quaternion a, Quaternion b, double t) {
		Quaternion out = new Quaternion();
		double cosHalfTheta = a.x * b.x + a.y * b.y + a.z * b.z + a.w * b.w;
		if (cosHalfTheta < 0.0F) {
			b = new Quaternion(b);
			cosHalfTheta = -cosHalfTheta;
			b.x = -b.x;
			b.y = -b.y;
			b.z = -b.z;
			b.w = -b.w;
		}

		double halfTheta = Math.acos(cosHalfTheta);
		double sinHalfTheta = Math.sqrt(1.0F - cosHalfTheta * cosHalfTheta);
		double ratioA;
		double ratioB;
		if (Math.abs(sinHalfTheta) > 0.001D) {
			double oneOverSinHalfTheta = 1.0F / sinHalfTheta;
			ratioA = Math.sin((1.0F - t) * halfTheta) * oneOverSinHalfTheta;
			ratioB = Math.sin(t * halfTheta) * oneOverSinHalfTheta;
		} else {
			ratioA = 1.0F - t;
			ratioB = t;
		}

		out.x = (float) (ratioA * a.x + ratioB * b.x);
		out.y = (float) (ratioA * a.y + ratioB * b.y);
		out.z = (float) (ratioA * a.z + ratioB * b.z);
		out.w = (float) (ratioA * a.w + ratioB * b.w);

		out = normalizeQuaternion(out.w, out.x, out.y, out.z);
		return out;
	}

	/**
	 * 欧拉角（弧度）转四元数
	 *
	 * @param pitch
	 * @param yaw
	 * @param roll
	 * @return
	 */
	public static Quaternion toQuaternion(double pitch, double yaw, double roll) {
		EulerAngles eu = new EulerAngles((float) Math.toRadians(pitch), (float) Math.toRadians(yaw), (float) Math.toRadians(roll)); // 角度转弧度
		return eu.toQuaternion();
	}

	/**
	 * 四元数转欧拉角（弧度）
	 *
	 * @param quaternion
	 * @return
	 */
	public static EulerAngles toEulerAngles(Quaternion quaternion) {
		return quaternion.toEulerAngles();
	}

	/**
	 * 姿态角——即欧拉角转四元数，对俩个四元数进行球面插值，四元数转回欧拉角并返回
	 *
	 * @param pitch    位置一俯仰角 -180~180
	 * @param yaw      位置一航向角 0~360
	 * @param roll     位置一翻滚角 -180~180
	 * @param pitch1   位置二俯仰角 -180~180
	 * @param yaw1     位置二俯仰角 0~360°
	 * @param roll1    位置二翻滚角 -180~180
	 * @param t        位置一时间
	 * @param t1       位置二时间
	 * @param t_insert 要计算姿态角的位置对应时间
	 * @return
	 */
	public static EulerAngles slerpInsert(float pitch, float yaw, float roll, float pitch1, float yaw1, float roll1, long t, long t1, long t_insert) {
		// 位置1 欧拉角转四元数
		// 位置2 欧拉角转四元数
		Quaternion p = toQuaternion(pitch, yaw, roll);
		Quaternion q = toQuaternion(pitch1, yaw1, roll1);

		// 计算插入的scale
		float scale = (float) ((t_insert - t) / ((t1 - t) * 1.0));

		// Slerp球面线性插值
		Quaternion r = makeInterpolated(q, p, scale);

		// 四元数转欧拉角
		return r.toEulerAngles();
	}

	private static void testSlerpInsert(float pitch, float yaw, float roll, float pitch1, float yaw1, float roll1, long t, long t1, long t_insert) {
//		log.info("==================testSlerpInsert start===============");
		EulerAngle2QuatUtil eq = new EulerAngle2QuatUtil();
		EulerAngles eulerAngles = eq.slerpInsert(pitch, yaw, roll, pitch1, yaw1, roll1, t, t1, t_insert);
		float roll2 = (float) Math.toDegrees(eulerAngles.roll); // 弧度转回角度
		float pitch2 = (float) Math.toDegrees(eulerAngles.pitch); // 弧度转回角度
		float heading2 = (float) (Math.toDegrees(eulerAngles.yaw) > 0 ? Math.toDegrees(eulerAngles.yaw) : Math.toDegrees(eulerAngles.yaw) + 360); // 弧度转回角度(航向角0~360°)

//		log.info("slerpInsert {} {} {}", Double.parseDouble(String.format("%.3f", roll2)), Double.parseDouble(String.format("%.3f", pitch2)), Double.parseDouble(String.format("%.3f", heading2)));
//		log.info("==================testSlerpInsert end=================");
	}

	private static Quaternion getQuaternion(float roll, float pitch, float yaw) {
		EulerAngle2QuatUtil eq = new EulerAngle2QuatUtil();
		EulerAngles eu = new EulerAngles((float) Math.toRadians(pitch), (float) Math.toRadians(yaw), (float) Math.toRadians(roll));
		Quaternion quaternion = eu.toQuaternion();
		EulerAngles eulerAngles = quaternion.toEulerAngles();
		float roll2 = (float) Math.toDegrees(eulerAngles.roll); // 弧度转回角度
		float pitch2 = (float) Math.toDegrees(eulerAngles.pitch); // 弧度转回角度
		float heading2 = (float) (Math.toDegrees(eulerAngles.yaw) > 0 ? Math.toDegrees(eulerAngles.yaw) : Math.toDegrees(eulerAngles.yaw) + 360); // 弧度转回角度(航向角0~360°)
//		log.info("toDegree: {} {} {}", Double.parseDouble(String.format("%.3f", roll2)), Double.parseDouble(String.format("%.3f", pitch2)), Double.parseDouble(String.format("%.3f", heading2)));
		return quaternion;
	}

	public static double wrapPi(double angle) {
		if (angle < -Math.PI) {
			angle = angle + 2 * Math.PI;
		} else if (angle > Math.PI) {
			angle = angle - 2 * Math.PI;
		}
		return angle;
	}

	/**
	 * @param pitch
	 * @param roll
	 * @param heading
	 * <AUTHOR>
	 */
	public static double[] calcVecCog(double pitch, double roll) {
		pitch = Math.toRadians(pitch);
		roll = Math.toRadians(roll);
		EulerAngles euler = new EulerAngles((float) pitch, (float) 0, (float) roll);
		// 欧拉转四元组
		Quaternion quaternion = euler.toQuaternion();
		// 归一化
		quaternion = normalizeQuaternion(quaternion);

		double craneTheRad = 2 * Math.acos(quaternion.w);
		double sinCraneThe = Math.sin(craneTheRad / 2);
		double[] vecU = {quaternion.x / sinCraneThe, quaternion.y / sinCraneThe, quaternion.z / sinCraneThe};
		double vecCogRad = Math.atan2(vecU[1], vecU[0]);
		vecCogRad = wrapPi(vecCogRad - Math.PI / 2);

		double[] results = new double[2];
		// 塔身倾角
		double craneThe = Math.toDegrees(craneTheRad);
		// 倾角朝向
		double vecCog = Math.toDegrees(vecCogRad);
		results[0] = craneThe;
		results[1] = vecCog;
		return results;
	}

//	public static void main(String[] args) {
//		String format = "俯仰角: %s, 滚转角: %s, 倾角朝向: %s";
//		System.out.printf((format) + "%n", 30, 20, calcVecCog(30, 20));
//		System.out.printf((format) + "%n", 45, 0, calcVecCog(45, 0));
//		System.out.printf((format) + "%n", 45, 45, calcVecCog(45, 45));
//		System.out.printf((format) + "%n", 0, 45, calcVecCog(0, 45));
//		System.out.printf((format) + "%n", -45, 45, calcVecCog(-45, 45));
//		System.out.printf((format) + "%n", -45, 0, calcVecCog(-45, 0));
//		System.out.printf((format) + "%n", -45, -45, calcVecCog(-45, -45));
//		System.out.printf((format) + "%n", 0, -45, calcVecCog(0, -45));
//		System.out.printf((format) + "%n", 45, -45, calcVecCog(45, -45));
//	}
}

package com.fj.towercontrol.util;

import com.blankj.utilcode.util.CollectionUtils;
import com.fj.towercontrol.data.entity.AlarmInfo;
import com.fj.towercontrol.data.entity.AlarmType;
import com.fj.towercontrol.data.net.dto.platform.AlarmConfig;
import com.fj.towercontrol.mqtt.entity.TowerData;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预警判断工具类
 *
 * <AUTHOR>
 */
public class AlarmJudgeUtil {

	private AlarmJudgeUtil() {
	}

	/**
	 * 评判是否有预警（目前只有距离预警由App生成，风速和重量预警由ecu生成）
	 *
	 * @param towerData 塔吊数据
	 * @return 预警列表
	 */
	public static List<AlarmInfo> judgeAlarmInfo(TowerData towerData) {
		List<AlarmConfig> alarmConfigList = FileStore.getAlarmConfigList();
		if (CollectionUtils.isEmpty(alarmConfigList)) {
			return Collections.emptyList();
		}
		Map<Integer, AlarmInfo> alarmMap = new HashMap<>();
		for (AlarmConfig config : alarmConfigList) {
			if (config.getAlarmLvl() == AlarmConfig.ALARM_LEVEL_SEVERE) {
				// 严重
				if (config.getParamType() == AlarmConfig.ALARM_TYPE_OBSTACLE_DISTANCE) {
					// 距离
					Double distance = towerData.getAlarmRadarDistance();
					if (distance != null && compare(config, distance)) {
						alarmMap.put(
							config.getParamType(),
							new AlarmInfo(
								config.getParamType(), config.getAlarmLvl(), distance));
					}
				}
				//                else if (config.getParamType() == 2) {
				//                    //重量
				//
				//                } else if (config.getParamType() == 3) {
				//                    //风速
				//                    double windSpeed = towerData.getWindSpeed();
				//                    if (compare(config, windSpeed)) {
				//                        alarmMap.put(config.getParamType(), new
				// AlarmInfo(config.getParamType(), config.getAlarmLvl(), windSpeed));
				//                    }
				//                }
			} else if (config.getAlarmLvl() == AlarmConfig.ALARM_LEVEL_NORMAL) {
				// 一般
				if (alarmMap.containsKey(config.getParamType())) {
					continue;
				}
				if (config.getParamType() == AlarmConfig.ALARM_TYPE_OBSTACLE_DISTANCE) {
					// 距离
					Double distance = towerData.getAlarmRadarDistance();
					if (distance != null && compare(config, distance)) {
						alarmMap.put(
							config.getParamType(),
							new AlarmInfo(
								config.getParamType(), config.getAlarmLvl(), distance));
					}
				}
				//                else if (config.getParamType() == 2) {
				//                    //重量
				//
				//                } else if (config.getParamType() == 3) {
				//                    //风速
				//                    double windSpeed = towerData.getWindSpeed();
				//                    if (compare(config, windSpeed)) {
				//                        alarmMap.put(config.getParamType(), new
				// AlarmInfo(config.getParamType(), config.getAlarmLvl(), windSpeed));
				//                    }
				//                }
			}
		}
		List<AlarmInfo> alarmInfoList = new ArrayList<>();
		for (Map.Entry<Integer, AlarmInfo> entry : alarmMap.entrySet()) {
			alarmInfoList.add(entry.getValue());
		}
		return alarmInfoList;
	}

	private static boolean compare(AlarmConfig config, double value) {
		int compareType = config.getComparatorType();
		double alarmValue = config.getAlarmValue();
		if (compareType == 1) {
			return value > alarmValue;
		} else if (compareType == 2) {
			return value == alarmValue;
		} else if (compareType == 3) {
			return value < alarmValue;
		} else if (compareType == 4) {
			return value >= alarmValue;
		} else if (compareType == 5) {
			return value <= alarmValue;
		}
		return false;
	}

	/**
	 * 判断当前是否需要汽车吊告警
	 *
	 * @return 告警的类型
	 */
	public static AlarmType judgeCarCraneCollision(double hookLat, double hookLng, double targetLat, double targetLng, double carCraneRadius, double warnDistance, double criticalDistance) {
		if (hookLat == 0 || hookLng == 0 || targetLat == 0 || targetLng == 0 || carCraneRadius == 0 || warnDistance == 0 || criticalDistance == 0) {
			return AlarmType.Normal;
		}
		double distance = Calculator.calculateDistance(hookLat, hookLng, targetLat, targetLng);
		if (distance <= carCraneRadius + criticalDistance) {
			return AlarmType.Critical;
		} else if (distance <= carCraneRadius + warnDistance) {
			return AlarmType.Warning;
		}
		return AlarmType.Normal;
	}
}

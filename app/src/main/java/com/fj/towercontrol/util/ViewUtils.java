package com.fj.towercontrol.util;

import android.annotation.SuppressLint;
import android.widget.PopupWindow;

import androidx.appcompat.widget.AppCompatSpinner;
import androidx.appcompat.widget.ListPopupWindow;

import java.lang.reflect.Field;

/**
 * View工具类
 *
 * <AUTHOR>
 */
public class ViewUtils {

	/**
	 * 设置Spinner弹出选项时隐藏导航栏
	 *
	 * @param spinner spinner
	 */
	@SuppressLint("DiscouragedPrivateApi")
	public static void avoidSpinnerDropdownFocus(AppCompatSpinner spinner) {
		try {
			Field listPopupField = AppCompatSpinner.class.getDeclaredField("mPopup");
			listPopupField.setAccessible(true);
			Object listPopup = listPopupField.get(spinner);
			if (listPopup instanceof ListPopupWindow) {
				Field popupField = ListPopupWindow.class.getDeclaredField("mPopup");
				popupField.setAccessible(true);
				Object popup = popupField.get(listPopup);
				if (popup instanceof PopupWindow) {
					((PopupWindow) popup).setFocusable(false);
				}
			}
		} catch (NoSuchFieldException | IllegalAccessException e) {
			e.printStackTrace();
		}
	}
}

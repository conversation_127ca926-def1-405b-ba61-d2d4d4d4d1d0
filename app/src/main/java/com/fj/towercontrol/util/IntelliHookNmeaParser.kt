package com.fj.towercontrol.util

import android.util.Log
import com.blankj.utilcode.util.ThreadUtils
import com.blankj.utilcode.util.ThreadUtils.SimpleTask
import net.sf.marineapi.nmea.event.AbstractSentenceListener
import net.sf.marineapi.nmea.io.SentenceReader
import net.sf.marineapi.nmea.sentence.GGASentence
import net.sf.marineapi.nmea.sentence.HDTSentence
import net.sf.marineapi.nmea.sentence.VTGSentence
import java.io.PipedInputStream
import java.io.PipedOutputStream

/**
 * 智能吊钩NMEA解析工具
 *
 * <AUTHOR>
 * @since 2024/4/22
 */
object IntelliHookNmeaParser {

	private const val TAG = "IntelliHookNmeaParser"
	private val inputStream = PipedInputStream()
	private val outputStream = PipedOutputStream(inputStream)
	private val sentenceReader: SentenceReader = SentenceReader(inputStream)

	init {
		sentenceReader.addSentenceListener(GGAListener())
//		sentenceReader.addSentenceListener(VTGListener())
//		sentenceReader.addSentenceListener(HDTListener())
		sentenceReader.start()
	}

	fun addNmeaData(nmeaData: ByteArray) {
		ThreadUtils.executeByIo(object : SimpleTask<Any>() {
			override fun doInBackground(): Any {
				outputStream.write(nmeaData)
				return Any()
			}

			override fun onSuccess(result: Any?) {
			}
		})
	}

	private class GGAListener : AbstractSentenceListener<GGASentence>() {
		override fun sentenceRead(sentence: GGASentence?) {
			if (sentence == null || !sentence.isValid) {
				return
			}
			Log.v(TAG, "GGA: $sentence")
			try {
				MemoryStore.getInstance().hookGeoidalHeight = sentence.geoidalHeight
			} catch (e: Exception) {
				//data not available
			}
		}
	}

	private class VTGListener : AbstractSentenceListener<VTGSentence>() {
		override fun sentenceRead(sentence: VTGSentence?) {
			if (sentence == null || !sentence.isValid) {
				return
			}
			Log.v(TAG, "VTG: $sentence")
		}
	}

	private class HDTListener : AbstractSentenceListener<HDTSentence>() {
		override fun sentenceRead(sentence: HDTSentence?) {
			if (sentence == null || !sentence.isValid) {
				return
			}
			Log.v(TAG, "HDT: $sentence")
		}
	}

}

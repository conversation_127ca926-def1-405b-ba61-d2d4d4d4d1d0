package com.fj.towercontrol.util;

import android.os.Environment;

import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ZipUtils;
import com.fj.towercontrol.data.net.callback.Callback;
import com.fj.towercontrol.repository.RepositoryContainer;
import com.fjdynamics.app.logger.Logger;
import com.fjdynamics.app.logger.LoggerManager;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 日志上传工具类
 *
 * <AUTHOR>
 */
public class LogUploader {

	private static final String TAG = "LogUploader";

	public static final int LOG_TYPE_APP = 1;
	public static final int LOG_TYPE_ECU = 2;
	public static final int LOG_TYPE_RTK = 3;

	/**
	 * 正在上传中，不可重复开始
	 */
	public static final int ERROR_UPLOAD_TASK_RUNNING = 1001;
	/**
	 * 日志文件夹为空
	 */
	public static final int ERROR_LOG_DIR_EMPTY = 1002;

	private final List<File> logList = new ArrayList<>();
	/**
	 * 当前上传的日志文件索引
	 */
	private final AtomicInteger index = new AtomicInteger(0);
	private int currLogType;
	private boolean uploading;
	private LogUploadCallback callback;

	private LogUploader() {
	}

	public static LogUploader getInstance() {
		return LogUploader.SingletonHolder.INSTANCE;
	}

	private static final class SingletonHolder {
		public static final LogUploader INSTANCE = new LogUploader();
	}

	/**
	 * 开始日志上传任务
	 *
	 * @param callback 结果回调
	 */
	public void start(int logType, LogUploadCallback callback) {
		this.callback = callback;
		if (uploading) {
			callback.onError(ERROR_UPLOAD_TASK_RUNNING, "正在进行上传任务，不可重复进行");
			return;
		}
		uploading = true;

		LoggerManager.flush();

		String sdcardPath = Environment.getExternalStorageDirectory().getAbsolutePath();
		File logDir = null;
		if (logType == LOG_TYPE_APP) {
			logDir = new File(sdcardPath + "/tower/log/app");
		} else if (logType == LOG_TYPE_ECU) {
			logDir = new File(sdcardPath + "/tower/log/ecu");
		} else if (logType == LOG_TYPE_RTK) {
			logDir = new File(sdcardPath + "/tower/log/rtk");
		}

		//根据最近修改时间对日志文件进行排序
		List<File> logFiles = FileUtils.listFilesInDir(logDir, Comparator.comparingLong(File::lastModified));
		if (CollectionUtils.isEmpty(logFiles)) {
			callback.onError(ERROR_LOG_DIR_EMPTY, "日志目录为空");
			uploading = false;
			return;
		}

		currLogType = logType;
		logList.clear();
		logList.addAll(logFiles);
		index.set(0);
		uploadNext();
	}

	/**
	 * 日志上传
	 */
	private void uploadNext() {
		if (index.get() >= logList.size()) {
			//日志全部上传完成
			callback.onFinish();
			uploading = false;
			return;
		}
		ThreadExecutor.getInstance().executor(() -> {
			File logFile = logList.get(index.getAndIncrement());
			if (!logFile.getName().endsWith(".xlog")
				|| FileUtils.getLength(logFile) == 0) {
				//不是xlog文件或者空文件
				FileUtils.delete(logFile);
				uploadNext();
				return;
			}
			//将xlog文件压缩成gz文件
			File zipFile;
			zipFile = new File(logFile.getParent(), logFile.getName().replace(".xlog", ".gz"));
			try {
				ZipUtils.zipFile(logFile, zipFile);
			} catch (IOException e) {
				Logger.e(TAG, "log file zip exception: " + e.getMessage());
			}
			if (FileUtils.getLength(zipFile) == 0) {
				//压缩失败
				FileUtils.delete(zipFile);
				uploadNext();
				return;
			}
			callback.onProgress("正在上传第" + index + "个日志文件，总计" + logList.size() + "个, 当前日志名称: " + logFile.getName());
			RepositoryContainer.getInstance()
				.getDeviceRepository()
				.uploadLog(currLogType, zipFile, new Callback<Object>() {
					@Override
					public void onSuccess(Object o) {
						Logger.d(TAG, "日志上传成功: " + zipFile.getName());
						FileUtils.delete(logFile);
						FileUtils.delete(zipFile);
						uploadNext();
					}

					@Override
					public void onError(String code, String msg) {
						Logger.e(TAG, "日志上传失败: " + msg);
						FileUtils.delete(zipFile);
						uploadNext();
					}
				});
		});
	}

	public interface LogUploadCallback {
		void onProgress(String msg);

		void onFinish();

		void onError(int code, String msg);
	}
}

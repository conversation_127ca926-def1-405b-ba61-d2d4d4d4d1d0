package com.fj.towercontrol.util;

import android.os.SystemClock;

import com.blankj.utilcode.util.CollectionUtils;
import com.fj.fjprotocol.TransManager;
import com.fj.towercontrol.data.entity.HistoryPoint;
import com.fjdynamics.app.logger.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 轨迹透传工具
 *
 * <AUTHOR>
 */
public class TaskHistorySender {

	private static final String TAG = "TaskHistorySender";
	private final List<HistoryPoint> historyPoints = new ArrayList<>();
	private final ExecutorService senderExecutor;
	private Callback callback;
	private boolean sending;
	private int startIndex;

	private int retryCount;
	private long lastHistorySentSuccessTimestamp;
	private CountDownLatch countDownLatch;

	private TaskHistorySender() {
		senderExecutor = Executors.newSingleThreadExecutor();
	}

	public static TaskHistorySender getInstance() {
		return TaskHistorySender.SingletonHolder.INSTANCE;
	}

	private static final class SingletonHolder {
		public static final TaskHistorySender INSTANCE = new TaskHistorySender();
	}

	public void start(List<HistoryPoint> historyPointList, Callback callback) {
		this.callback = callback;
		if (sending) {
			callback.onError(1001, "上一次轨迹发送还未结束");
			return;
		}
		sending = true;
		if (CollectionUtils.isEmpty(historyPointList)) {
			sending = false;
			callback.onError(1002, "未找到历史轨迹");
			return;
		}
		historyPoints.clear();
		historyPoints.addAll(historyPointList);
		//            // TODO: 2022/9/16 暂时关闭对轨迹的处理，需要有真实数据验证下效果
		//            // 对历史轨迹进行处理,过滤掉距离不足1米的点
		//            WorkingHistoryPoint prev = savedHistoryPoints.get(0);
		//            List<WorkingHistoryPoint> filteredPointList = new ArrayList<>();
		//            filteredPointList.add(prev);
		//            for (int i = 1; i < savedHistoryPoints.size(); i++) {
		//                WorkingHistoryPoint historyPoint = savedHistoryPoints.get(i);
		//                double distance = MathUtil.distance(prev.getLat(), prev.getLng(),
		// prev.getAlt(), historyPoint.getLat(), historyPoint.getLng(), historyPoint.getAlt());
		//                if (distance >= 1) {
		//                    filteredPointList.add(historyPoint);
		//                    prev = historyPoint;
		//                }
		//            }
		//            LogUtil.d(TAG, "原始轨迹点数: " + savedHistoryPoints.size() + ", 处理后的轨迹点数: " +
		// filteredPointList.size());
		//            savedHistoryPoints = filteredPointList;
		if (historyPoints.size() <= 1) {
			sending = false;
			callback.onError(1003, "历史轨迹为空");
			return;
		}

		startIndex = 0;
		send(startIndex);
	}

	private void send(int startIndex) {
		senderExecutor.execute(() -> {
			Logger.d(TAG, "发送历史轨迹: startIndex -> " + startIndex + ", totalSize -> " + historyPoints.size() + ", retryCount -> " + retryCount);
			int endIndex = Math.min((startIndex + 10), historyPoints.size());
			List<HistoryPoint> subList = historyPoints.subList(startIndex, endIndex);
			byte[] bytes = CsvUtil.convertHistoryPointListToBytes(subList, historyPoints.size(), startIndex);
			TransManager.getInstance().sendEcuDataWithUart1(bytes);
			countDownLatch = new CountDownLatch(1);
			try {
				boolean await = countDownLatch.await(1_000, TimeUnit.MILLISECONDS);
				if (!await) {
					if (retryCount < 3) {
						retryCount++;
						send(startIndex);
					} else {
						Logger.e(TAG, "轨迹发送失败");
						sending = false;
						callback.onError(1004, "ecu接收异常");
						retryCount = 0;
					}
				}
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		});
	}

	public void onEcuResponse(int result, int ecuLastReceivedIndex) {
		countDownLatch.countDown();
		retryCount = 0;
		if (result == 1) {
			// 接收成功，发送下一包
			startIndex = Math.min(ecuLastReceivedIndex + 10, historyPoints.size() - 1);
			send(startIndex);
		} else if (result == 2) {
			long current = SystemClock.elapsedRealtime();
			if (current - lastHistorySentSuccessTimestamp < 2_000) {
				// 不处理2s内的重复成功消息
				return;
			}
			lastHistorySentSuccessTimestamp = current;
			// 全部接收结束
			Logger.d(TAG, "轨迹发送成功");
			callback.onSuccess();
			sending = false;
			startIndex = 0;
		}
	}

	public interface Callback {
		void onSuccess();

		void onError(int code, String msg);
	}
}

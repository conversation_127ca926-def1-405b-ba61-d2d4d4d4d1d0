package com.fj.towercontrol.util;

/**
 * OTA升级相关工具类
 *
 * <AUTHOR>
 */
public class OtaUtils {

	private OtaUtils() {
	}

	/**
	 * 比较版本号判断是否需要升级
	 *
	 * @param currentVersion 当前版本号
	 * @param latestVersion  最新版本号
	 * @return true:需要升级 false:不需要升级
	 */
	public static boolean needUpgrade(String currentVersion, String latestVersion) {
		String[] parts1 = currentVersion.split("\\.");
		String[] parts2 = latestVersion.split("\\.");

		int length = Math.max(parts1.length, parts2.length);
		for (int i = 0; i < length; i++) {
			int num1 = (i < parts1.length) ? Integer.parseInt(parts1[i]) : 0;
			int num2 = (i < parts2.length) ? Integer.parseInt(parts2[i]) : 0;

			if (num1 < num2) {
				return true;
			} else if (num1 > num2) {
				return false;
			}
		}

		return false;
	}

}

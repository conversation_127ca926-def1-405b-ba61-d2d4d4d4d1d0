package com.fj.towercontrol.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Objects;

/**
 * 接口token生成工具
 *
 * <AUTHOR>
 */
public class TokenBuilder {
	private static final String SECRET = "www.fjdynamics.com";

	public TokenBuilder() {
	}

	public static String encrypt(String business) {
		return hash(SECRET + business).replaceAll("\\d", "");
	}

	public static String encrypt(String business, long timestamp) {
		return hash(SECRET + timestamp + business).replaceAll("\\d", "");
	}

	private static byte[] md5(String s) {
		try {
			MessageDigest algorithm = MessageDigest.getInstance("MD5");
			algorithm.reset();
			algorithm.update(s.getBytes(StandardCharsets.UTF_8));
			return algorithm.digest();
		} catch (Exception var3) {
			return null;
		}
	}

	private static String toHex(byte[] hash) {
		if (hash == null) {
			return null;
		} else {
			StringBuilder buf = new StringBuilder(hash.length * 2);
			for (byte b : hash) {
				if ((b & 255) < 16) {
					buf.append("0");
				}
				buf.append(Long.toString((long) (b & 255), 16));
			}
			return buf.toString();
		}
	}

	private static String hash(String s) {
		try {
			return new String(
				Objects.requireNonNull(toHex(md5(s))).getBytes(StandardCharsets.UTF_8),
				StandardCharsets.UTF_8);
		} catch (Exception var2) {
			return s;
		}
	}
}

@file:Suppress("MemberVisibilityCanBePrivate")

package com.fj.towercontrol.ui.base

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.view.KeyEvent
import android.view.WindowManager
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.fragment.app.DialogFragment

/**
 * 用于所有弹窗的 [DialogFragment] 基类
 *
 * <AUTHOR>
 * @since 2024/5/14
 */
abstract class BaseDialogFragment : DialogFragment(), IFragment {

	var onDismissListener: DialogInterface.OnDismissListener? = null
	var onCancelListener: DialogInterface.OnCancelListener? = null

	override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
		val dialog = super.onCreateDialog(savedInstanceState).apply {
			setOnKeyListener { _, keyCode, keyEvent ->
				if (keyCode == KeyEvent.KEYCODE_BACK && keyEvent.action == KeyEvent.ACTION_UP) {
					<EMAIL>()
				} else {
					false
				}
			}
		}

		//弹窗时隐藏导航栏
		dialog.window?.setFlags(
			WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
			WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
		)
		dialog.show()
		dialog.window?.apply {
			val windowInsetsController = WindowCompat.getInsetsController(this, decorView)
			windowInsetsController.systemBarsBehavior =
				WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
			windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())
		}
		dialog.window?.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)

		return dialog
	}

	override fun onResume() {
		super.onResume()
		dialog?.run {
			setCanceledOnTouchOutside(isCancelable)
			setCancelable(isCancelable)
		}
	}

	override fun onDismiss(dialog: DialogInterface) {
		onDismissListener?.onDismiss(dialog)
		super.onDismiss(dialog)
	}

	override fun onCancel(dialog: DialogInterface) {
		onCancelListener?.onCancel(dialog)
		super.onCancel(dialog)
	}

	override fun finish() {
		activity?.finish()
	}
}

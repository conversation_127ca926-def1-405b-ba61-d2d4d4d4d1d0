package com.fj.towercontrol.ui.base

import android.os.Bundle
import androidx.activity.addCallback
import androidx.fragment.app.Fragment

/**
 * Fragment基类
 *
 * <AUTHOR>
 * @since 2024/5/14
 */
abstract class BaseFragment : Fragment(), IFragment {

	override fun onCreate(savedInstanceState: Bundle?) {
		super.onCreate(savedInstanceState)
		activity?.onBackPressedDispatcher?.addCallback(this) {
			isEnabled = onBackPressed()
			@Suppress("DEPRECATION")
			if (!isEnabled) activity?.onBackPressed()
			isEnabled = true
		}
	}

	override fun finish() {
		activity?.finish()
	}
}

sealed interface IFragment {

	fun onBackPressed(): Boolean = false

	fun finish()
}

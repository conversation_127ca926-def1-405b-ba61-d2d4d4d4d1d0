package com.fj.towercontrol.ui.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding

/**
 * 使用 [ViewBinding] 的 Fragment 基类
 *
 * <AUTHOR>
 * @since 2024/5/14
 */
abstract class BaseBindingFragment<VB : ViewBinding> :
	BaseFragment(),
	IBindingFragment<VB> {
	private var _binding: VB? = null

	/**
	 * You can't call [binding] after [onDestroyView]
	 */
	override val binding: VB get() = checkNotNull(_binding) { "Binding has been destroyed" }

	override fun onCreateView(
		inflater: LayoutInflater,
		container: ViewGroup?,
		savedInstanceState: Bundle?,
	): View {
		_binding = inflateBinding(layoutInflater)
		initOnce()
		return binding.root
	}

	override fun onDestroyView() {
		_binding = null
		super.onDestroyView()
	}
}

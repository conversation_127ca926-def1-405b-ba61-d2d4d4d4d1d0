package com.fj.towercontrol.ui.base

import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

/**
 * Activity基类
 *
 * <AUTHOR>
 * @since 2024/5/15
 */
abstract class BaseActivity : AppCompatActivity() {

	override fun onCreate(savedInstanceState: Bundle?) {
		super.onCreate(savedInstanceState)
		//首次安装立即打开，后续切后台没有杀死 app 的情况下会触发反复进入 launcherActivity 的 bug
		if ((intent.flags and Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT) != 0) {
			finish()
			return
		}
		//设置横屏
		@SuppressLint("SourceLockedOrientationActivity")
		requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
		enableEdgeToEdge()
		//隐藏状态栏和导航栏
		WindowCompat.getInsetsController(window, window.decorView).apply {
			systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
			hide(WindowInsetsCompat.Type.systemBars())
		}
	}

	override fun onWindowFocusChanged(hasFocus: Boolean) {
		super.onWindowFocusChanged(hasFocus)
		if (hasFocus) {
			//防止弹窗消失后导航栏会再次出现
			WindowCompat.getInsetsController(window, window.decorView)
				.hide(WindowInsetsCompat.Type.systemBars())
		}
	}
}

package com.fj.towercontrol.ui.base

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding

/**
 * 使用 [ViewBinding] 的 Dialog 基类
 *
 * <AUTHOR>
 * @since 2024/5/14
 */
abstract class BaseBindingDialogFragment<VB : ViewBinding> :
	BaseDialogFragment(),
	IBindingFragment<VB> {

	override lateinit var binding: VB

	override fun onCreate(savedInstanceState: Bundle?) {
		super.onCreate(savedInstanceState)
		binding = inflateBinding(layoutInflater)
		initOnce()
	}

	override fun onCreateView(
		inflater: LayoutInflater,
		container: ViewGroup?,
		savedInstanceState: Bundle?,
	): View {
		dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
		return binding.root
	}

}

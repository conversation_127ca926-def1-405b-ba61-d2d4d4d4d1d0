package com.fj.towercontrol.ui.base

import android.view.ViewGroup
import androidx.viewbinding.ViewBinding

/**
 * 全屏显示的弹窗基类
 *
 * <AUTHOR>
 * @since 2024/5/14
 */
abstract class FullScreenDialogFragment<VB : ViewBinding> :
	BaseBindingDialogFragment<VB>() {

	override fun onResume() {
		super.onResume()
		dialog?.window?.run {
			attributes = attributes?.apply {
				width = ViewGroup.LayoutParams.MATCH_PARENT
				height = ViewGroup.LayoutParams.MATCH_PARENT
			}
		}
	}

	override fun isCancelable(): Boolean = false

	override fun onBackPressed(): Boolean = true
}

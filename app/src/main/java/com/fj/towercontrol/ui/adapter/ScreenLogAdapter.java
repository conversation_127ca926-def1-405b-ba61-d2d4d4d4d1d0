package com.fj.towercontrol.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.fj.towercontrol.R;
import com.fj.towercontrol.data.entity.ScreenLog;

import java.util.ArrayList;
import java.util.List;

public class ScreenLogAdapter extends RecyclerView.Adapter<ScreenLogAdapter.ViewHolder> {

	private final List<ScreenLog> logList = new ArrayList<>();

	@NonNull
	@Override
	public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
		return new ViewHolder(
			LayoutInflater.from(parent.getContext())
				.inflate(R.layout.item_screen_log, parent, false));
	}

	@Override
	public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
		ScreenLog screenLog = logList.get(position);
		holder.tvLog.setText(String.format("%s:  %s", screenLog.getLogTime(), screenLog.getMsg()));
	}

	@Override
	public int getItemCount() {
		return logList.size();
	}

	public void setData(List<ScreenLog> linkedList) {
		logList.clear();
		logList.addAll(linkedList);
		notifyDataSetChanged();
	}

	static class ViewHolder extends RecyclerView.ViewHolder {

		private final TextView tvLog;

		public ViewHolder(@NonNull View itemView) {
			super(itemView);
			tvLog = itemView.findViewById(R.id.tv_log);
		}
	}
}

package com.fj.towercontrol.log;

import com.fjdynamics.tractorprotocol.common.manager.ProtocolDataManager;
import com.fjdynamics.tractorprotocol.common.transfer.NewRtkReceiveDataCallBack;
import com.fjdynamics.tractorprotocol.rtk.manager.RtkManager;
import com.fjdynamics.tractorprotocol.rtk.protocol.JiZhanRTCMProtocol;
import com.fjdynamics.tractorprotocol.rtk.protocol.ObserveDataProtocol;
import com.fjdynamics.tractorprotocol.rtk.protocol.RtkDataProtocol;

import java.io.File;
import java.util.ArrayList;

/**
 * Author:yong.hu Date 2022/7/1 15:16 Desc 观测量数据管理类型
 */
public class BoardCardLogManager implements NewRtkReceiveDataCallBack {
	static volatile BoardCardLogManager defaultInstance;
	// 缓存数据
	private final ArrayList<byte[]> rtkByteList = new ArrayList<>();
	private int rtkReceiveCount = 0;

	public static BoardCardLogManager getInstance() {
		if (defaultInstance == null) {
			synchronized (BoardCardLogManager.class) {
				if (defaultInstance == null) {
					defaultInstance = new BoardCardLogManager();
				}
			}
		}
		return defaultInstance;
	}

	// 初始化数据回调
	public void initReceiveDataCallBack() {
		RtkManager.getInstance().setReceiveDataCallBack(this);
		// 带105，日志保证在sdcard/FJ/log/board2,与rtcm流文件同时间保存
		ProtocolDataManager.getInstance()
			.registerProtocolDataCallback(
				ObserveDataProtocol.class,
				ggaDataProtocol -> {
					if (!RtkManager.getInstance().isNewRtk()) {
						// 保存gga日志到本地
						byte[] ggaBytes = ggaDataProtocol.getObserveBytes();
						ObserveDataLogUtil.getInstance()
							.inputGgaLogToFile(
								ggaBytes); // 保存原始二进制数据，二进制转string保存乱码数据可能会转换出错导致无法解析
					}
				});
		ProtocolDataManager.getInstance()
			.registerProtocolDataCallback(
				JiZhanRTCMProtocol.class,
				jiZhanRTCMProtocol -> {
					// 保存rtcm日志到本地文件
					byte[] data = jiZhanRTCMProtocol.getGgaBytes();
					ObserveDataLogUtil.getInstance().inputRtcmLogToFile(data);
				});

		ProtocolDataManager.getInstance()
			.registerProtocolDataCallback(
				RtkDataProtocol.class,
				rtkDataProtocol -> {
					// 收集rtk日志
					byte[] bytes = rtkDataProtocol.getData();
					RtkLogUtil.inputLogToFile(new String(bytes));
				});
	}

	@Override
	public void onReceiveDataBack(byte[] bytes) {
		saveData(bytes);
	}

	/**
	 * 保存数据
	 *
	 * @param bytes
	 */
	private void saveData(byte[] bytes) {
		try {
			rtkByteList.add(bytes);
			rtkReceiveCount++;
			if (rtkReceiveCount > 9) {
				LogWriter.writeBoardLogToFile(rtkByteList);
				rtkByteList.clear();
				rtkReceiveCount = 0;
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 老的 由shell保存的
	 */
	private void checkAndDeleteOldFile() {
		File file = new File(LogWriter.LOG_PATH_FJ);
		if (!file.exists() || !file.isDirectory()) {
			return;
		}
		File[] files = file.listFiles();
		for (File file1 : files) {
			String name = file1.getName();
			if (name.contains("high_fre_data")
				&& (name.endsWith(".LogUtil") || name.endsWith(".zip"))) {
				file1.delete();
			}
		}
	}
}

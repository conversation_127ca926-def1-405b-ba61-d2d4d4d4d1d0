package com.fj.towercontrol.log;

import android.os.Environment;

import com.fjdynamics.app.logger.Logger;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Locale;

/**
 * Rtk日志写文件工具类
 *
 * <AUTHOR>
 * @date 2022.10.21
 */
public class RtkLogUtil {

	public static final String TAG = "RtkLogUtil";

	private static final SimpleDateFormat logTimeDateFormat =
		new SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault());

	public static final String LOG_DIR_PARENT =
		Environment.getExternalStorageDirectory().getAbsolutePath() + "/tower/";

	/**
	 * Rtk日志文件夹名称
	 */
	public static final String LOG_DIR_NAME = "RTK";
	/**
	 * log文件后缀名
	 */
	private static final String LOG_FILE_SUFFIX = ".log";

	private static String curRtkFilePath;

	private static LogFileWriter logFileWriter;

	/**
	 * 默认每个文件大小最大15M
	 */
	private static long DEF_RTCM_EACH_FILE_MAX_KB = 15 * 1024 * 1024L;

	public static void init() {
		getLogFileWriter().deleteRedundantFile();
	}

	/**
	 * 返回一个新的日志文件路径
	 */
	public static String newLogFilePath() {
		return LOG_DIR_PARENT
			+ LOG_DIR_NAME
			+ "/"
			+ logTimeDateFormat.format(System.currentTimeMillis())
			+ LOG_FILE_SUFFIX;
	}

	private static LogFileWriter getLogFileWriter() {
		if (logFileWriter == null) {
			logFileWriter =
				new LogFileWriter(LOG_DIR_PARENT + LOG_DIR_NAME, null, LOG_FILE_SUFFIX, 5);
		}
		return logFileWriter;
	}

	/**
	 * 日志输出到文件
	 */
	public static void inputLogToFile(String log) {
		if (curRtkFilePath == null) {
			curRtkFilePath = newLogFilePath();
		}
		File rtkFile = new File(curRtkFilePath);
		if (rtkFile.exists()) {
			if (rtkFile.length() > DEF_RTCM_EACH_FILE_MAX_KB) {
				curRtkFilePath = newLogFilePath();
				Logger.d(TAG, "new curRtkFilePath = " + curRtkFilePath);
			}
		}
		String curTime = logTimeDateFormat.format(System.currentTimeMillis());
		getLogFileWriter().inputLogToFile(curRtkFilePath, curTime + ":" + log);
	}
}

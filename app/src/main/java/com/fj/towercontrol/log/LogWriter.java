package com.fj.towercontrol.log;

import android.os.Environment;
import android.text.TextUtils;

import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.Logger;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * @Author: Sven
 * @date:2022/4/20 16:28
 * @description:
 */
public class LogWriter {

	public static final String TAG = "LogWriter";
	public static final String LOG_PATH_FJ = Environment.getExternalStorageDirectory() + "/FJ/";
	public static final String FREQUENCY_LOG_PATH =
		Environment.getExternalStorageDirectory() + "/FJ/High_Freq/";
	public static final String LOG_PATH =
		Environment.getExternalStorageDirectory() + "/FJ/High_Freq/";
	public static final String BOARD_LOG_PATH =
		Environment.getExternalStorageDirectory() + "/tower/log/board/";
	public static final String LOG_NAME_PREFIX = "high_fre_data_";
	public static String BOARD_LOG_NAME_PREFIX = "board_log_observe_";
	// 板卡压缩文件名前缀
	public static String BOARD_LOG_FILE_PREFIX = "board_log_";
	public static SimpleDateFormat sdfLog =
		new SimpleDateFormat("yyyyMMddHHmmssSSS", Locale.getDefault());
	public static final String LOG_NAME_SUFFIX = ".log";
	private static final int MAX_LOG_FILE_SIZE = 10 * 1024 * 1024;
	private static final int MAX_LOG_FILE_CNT = 10;
	private static boolean isHighLogThreadStart = false;
	private static boolean highLogThreadState = true;
	private static boolean isBoardThreadStart = false;
	private static boolean boardThreadState = true;
	private static final LinkedBlockingQueue<ArrayList<byte[]>> highEcuQueue =
		new LinkedBlockingQueue<>();
	// 板卡日志
	private static final LinkedBlockingQueue<ArrayList<byte[]>> um482Queue =
		new LinkedBlockingQueue<>();

	static class WriteHighLogThread extends Thread {
		String currentFileTime = "";

		@Override
		public void run() {
			super.run();
			while (highLogThreadState) {
				try {
					ArrayList<byte[]> byteList = highEcuQueue.take();
					currentFileTime =
						writeBytesToFile(
							true, byteList, LOG_PATH, LOG_NAME_PREFIX, currentFileTime);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * 多个一组读写，避免频繁IO
	 *
	 * @param byteList 组包后的ecu的二进制数据
	 */
	public static void writeLogToFile(ArrayList<byte[]> byteList) {
		try {
			highEcuQueue.put(new ArrayList<>(byteList));
			if (!isHighLogThreadStart) {
				isHighLogThreadStart = true;
				new WriteHighLogThread().start();
			}
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 保存板卡日志到文件中
	 *
	 * @param byteList
	 */
	public static void writeBoardLogToFile(ArrayList<byte[]> byteList) {
		try {
			um482Queue.put(new ArrayList<>(byteList));
			if (!isBoardThreadStart) {
				isBoardThreadStart = true;
				new WriteBoardLogThread().start();
			}
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 只针对新链路的盒子
	 */
	static class WriteBoardLogThread extends Thread {
		String currentFileTime = "";

		@Override
		public void run() {
			super.run();
			while (boardThreadState) {
				try {
					ArrayList<byte[]> byteList = um482Queue.take();
					currentFileTime =
						writeBytesToFile(
							false,
							byteList,
							BOARD_LOG_PATH,
							BOARD_LOG_NAME_PREFIX,
							currentFileTime);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		}
	}

	private static String writeBytesToFile(
		boolean needPack,
		ArrayList<byte[]> byteList,
		String logPath,
		String logPrefix,
		String currentFileTime) {
		try {
			if (byteList == null || byteList.isEmpty()) {
				return currentFileTime;
			}
			File logFile;
			if (TextUtils.isEmpty(currentFileTime)) {
				currentFileTime = getFormatDateTime(sdfLog);
			}
			String logName = logPrefix + currentFileTime + LOG_NAME_SUFFIX;
			logFile = new File(logPath + logName);
			if (!logFile.exists()) {
				logFile = mkFile(logPath, logName);
			}
			if (logFile == null) {
				return currentFileTime;
			}
			FileOutputStream fileOutputStream = new FileOutputStream(logFile, true);
			for (int i = 0; i < byteList.size(); i++) {
				byte[] bytes = byteList.get(i);
				if (needPack) {
					fileOutputStream.write(packageHighFreqBytes(bytes));
				} else {
					fileOutputStream.write(bytes);
				}
			}
			fileOutputStream.flush();
			fileOutputStream.close();

			if (logFile.length() > MAX_LOG_FILE_SIZE) {
				currentFileTime = "";
			}
			clearOldLog(logPath);

		} catch (IOException e) {
			e.printStackTrace();
		}
		return currentFileTime;
	}

	public static String getFormatDateTime(SimpleDateFormat sdf) {
		return sdf.format(new Date());
	}

	/**
	 * 文件名为时间，个数校验
	 */
	public static void clearOldLog(String path) {
		int maxFileCount = MAX_LOG_FILE_CNT;
		if (path.startsWith(BOARD_LOG_PATH)) {
			maxFileCount = 3;
		}
		File file = new File(path);
		if (!file.isDirectory() || !file.exists()) {
			return;
		}
		File[] AllFiles = file.listFiles(); // 列出目录下的所有文件
		if (AllFiles == null || AllFiles.length <= maxFileCount) {
			return;
		}
		ArrayList<String> mFilesList = new ArrayList<>(); // 存放Log 下的所有文件
		for (int i = 0; i < AllFiles.length; i++) {
			File mFile = AllFiles[i]; // 得到文件
			String name = mFile.getName(); // 得到文件的名字
			if (name.length() < 1) {
				return;
			}
			if (name.endsWith(".txt") || name.endsWith(".gz") || name.endsWith(".log")) { // 筛选出log
				mFilesList.add(name); // 把文件名添加到链表里
			}
		}
		Collections.sort(mFilesList); // 将文件按自然排序升序排列
		// 判断日志文件如果大于10，就要处理
		for (int i = 0; i < mFilesList.size() - maxFileCount + 1; i++) {
			String name = mFilesList.get(i); // 得到链表最早的文件名
			File mFile = new File(file, name); // 得到最早的文件
			Logger.d(TAG, "will delect file : " + mFile.getName());
			mFile.delete(); // 删除
		}
	}

	/**
	 * 创建文件
	 *
	 * @param filePath
	 * @param fileName
	 * @return
	 */
	public static File mkFile(String filePath, String fileName) {
		File file = null;
		try {
			File fileDir = new File(filePath); // 如果没有filePath文件夹则新建该文件夹
			if (!fileDir.exists()) {
				fileDir.mkdirs();
			}
			file = new File(filePath, fileName);
			file.createNewFile();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return file;
	}

	/**
	 * 对原始ECU数据进行组包用于保存高频日志
	 *
	 * @param bytes
	 * @return
	 */
	private static byte[] packageHighFreqBytes(byte[] bytes) {
		if (bytes.length < 25) {
			return new byte[]{};
		}
		byte[] header = new byte[]{(byte) 0xeb, (byte) 0x90};
		byte[] timestamp = DataUtil.longToByteArray(System.currentTimeMillis());
		byte[] data = DataUtil.subBytes(bytes, 22, bytes.length - 25);
		byte[] dataLen = DataUtil.int2byte2(data.length);
		byte[] tail = new byte[]{0x0a};
		return DataUtil.byteMerger(
			header,
			DataUtil.byteMerger(
				timestamp, DataUtil.byteMerger(dataLen, DataUtil.byteMerger(data, tail))));
	}

	/**
	 * 修改观测量类型时，日志文件名称同步更改
	 *
	 * @param type
	 */
	public static void modifyBoardCardFileName(int type) {
		switch (type) {
			// 观测量日志
			case 0:
				BOARD_LOG_NAME_PREFIX = "board_log_observe_";
				break;
			// 卫星信号
			case 1:
				BOARD_LOG_NAME_PREFIX = "board_log_satellite_";
				break;
			// 改正数
			case 2:
				BOARD_LOG_NAME_PREFIX = "board_log_corrected_number_";
				break;
		}
	}
}

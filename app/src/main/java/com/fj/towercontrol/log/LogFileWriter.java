package com.fj.towercontrol.log;

import android.text.TextUtils;
import android.util.Log;

import com.fj.towercontrol.util.FileUtil;
import com.fjdynamics.app.logger.Logger;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 日志写入文件
 *
 * <AUTHOR>
 * @date 2022/4/28
 */
public class LogFileWriter {

	private static final String TAG = "LogFileWriter";

	/**
	 * 用于写日志的Executor
	 */
	private ExecutorService logWriteExecutor = Executors.newSingleThreadExecutor();
	/**
	 * log保存的文件夹
	 */
	private final String logDir;
	/**
	 * log文件标识
	 */
	private final String logIdentification;
	/**
	 * log文件后缀名
	 */
	private final String logSuffix;
	/**
	 * log文件最大数量
	 */
	private final int logFileMaxCount;

	private File logTempFile;

	public LogFileWriter(
		String logDir, String logIdentification, String logSuffix, int logFileMaxCount) {
		this.logDir = logDir;
		this.logIdentification = logIdentification;
		this.logSuffix = logSuffix;
		this.logFileMaxCount = logFileMaxCount;
	}

	/**
	 * 日志输出到文件
	 */
	public void inputLogToFile(String filePath, String log) {
		if (TextUtils.isEmpty(filePath)) {
			return;
		}
		logWriteExecutor.submit(
			() -> {
				logTempFile = new File(filePath);
				// 如果文件不存在，需要创建新的log文件
				if (!logTempFile.exists()) {
					// 删除超过上限的log文件
					deleteRedundantFile();
				}
				if (!FileUtil.createOrExistsFile(logTempFile)) {
					return;
				}
				writeStringToFile(filePath, log + "\n");
			});
	}

	/**
	 * 日志输出到文件
	 */
	public void inputLogToFileNoWrap(String filePath, String log) {
		if (TextUtils.isEmpty(filePath)) {
			return;
		}
		logWriteExecutor.submit(
			() -> {
				logTempFile = new File(filePath);
				// 如果文件不存在，需要创建新的log文件
				if (!logTempFile.exists()) {
					// 删除超过上限的log文件
					deleteRedundantFile();
				}
				if (!FileUtil.createOrExistsFile(logTempFile)) {
					return;
				}
				writeStringToFile(filePath, log);
			});
	}

	/**
	 * 日志输出到文件
	 */
	public void inputLogToFile(String filePath, byte[] log) {
		if (TextUtils.isEmpty(filePath)) {
			return;
		}
		logWriteExecutor.submit(
			() -> {
				logTempFile = new File(filePath);
				// 如果文件不存在，需要创建新的log文件
				if (!logTempFile.exists()) {
					// 删除超过上限的log文件
					deleteRedundantFile();
				}
				if (!FileUtil.createOrExistsFile(logTempFile)) {
					return;
				}
				writeStringToFile(filePath, log);
			});
	}

	/**
	 * 将string写入文件
	 *
	 * @param filePath 文件路径
	 * @param content  写入内容
	 */
	private void writeStringToFile(String filePath, String content) {
		BufferedWriter bw = null;
		try {
			bw = new BufferedWriter(new FileWriter(filePath, true));
			bw.write(content);
		} catch (IOException e) {
			Logger.e(TAG, Log.getStackTraceString(e));
		} finally {
			try {
				if (bw != null) {
					bw.close();
				}
			} catch (IOException e) {
				Logger.e(TAG, Log.getStackTraceString(e));
			}
		}
	}

	/**
	 * 将bytes写入文件
	 *
	 * @param filePath 文件路径
	 * @param content  写入内容
	 */
	public void writeStringToFile(String filePath, byte[] content) {
		try {
			// 根据绝对路径初始化文件
			File localFile = new File(filePath);
			OutputStream os = new FileOutputStream(localFile, true);
			os.write(content);
			os.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 删除多余的日志文件 超过最大文件数量限制时删除之前的文件
	 */
	public void deleteRedundantFile() {
		File logFileParentDir = new File(logDir);
		if (!logFileParentDir.exists()) {
			return;
		}
		File[] childFiles = logFileParentDir.listFiles();
		if (childFiles == null || childFiles.length <= logFileMaxCount) {
			return;
		}
		boolean needCheckLogIdentification = !TextUtils.isEmpty(logIdentification);
		List<File> childFileList = new ArrayList<>();
		for (File file : childFiles) {
			if (needCheckLogIdentification && !file.getName().contains(logIdentification)) {
				continue;
			}
			if (!file.getName().endsWith(logSuffix)) {
				continue;
			}
			childFileList.add(file);
		}
		if (childFileList.size() <= logFileMaxCount) {
			return;
		}
		Collections.sort(
			childFileList,
			(o1, o2) -> {
				return Long.compare(o1.lastModified(), o2.lastModified());
			});
		List<File> waitDeleteFileList =
			childFileList.subList(0, childFileList.size() - logFileMaxCount);
		for (File file : waitDeleteFileList) {
			FileUtil.deleteFile(file);
		}
	}
}

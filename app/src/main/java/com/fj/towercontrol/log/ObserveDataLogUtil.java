package com.fj.towercontrol.log;

import android.os.Environment;

import com.fj.towercontrol.util.FileUtil;
import com.fjdynamics.app.logger.Logger;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

/**
 * Uturn日志写文件工具类
 *
 * <AUTHOR>
 * @date 2022.01.12
 */
public class ObserveDataLogUtil {
	public static final String TAG = "ObserveDataLogUtil";

	private static final SimpleDateFormat logTimeDateFormat =
		new SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault());

	/**
	 * 记亩日志文件夹名称
	 */
	public static String LOG_GGA_PREFIX = "gga_observe_";

	public static String LOG_RTCM_PREFIX = "rtcm_observe_";

	public static final String LOG_DIR_PARENT =
		Environment.getExternalStorageDirectory().getAbsolutePath() + "/tower/log/";

	public static final String LOG_DIR_NAME = "board2";

	/**
	 * log文件后缀名
	 */
	private static final String LOG_FILE_SUFFIX = ".log";

	/**
	 * 默认每个文件大小最大15M
	 */
	private static long DEF_GGA_EACH_FILE_MAX_KB = 15 * 1024 * 1024L;

	/**
	 * 保存最多10个日志文件
	 */
	private static int MAX_GGA_FILE_COUNT = 10;

	private static LogFileWriter logFileWriter;
	private static LogFileWriter rtcmlogFileWriter;
	private static String curTimetamp;
	private static String curRtcmFilePath;
	private static String curGgaFilePath;

	public static Object key = new Object();

	public static ObserveDataLogUtil observeDataLogUtil;

	private ObserveDataLogUtil() {
	}

	public static ObserveDataLogUtil getInstance() {
		if (observeDataLogUtil == null) {
			synchronized (key) {
				if (observeDataLogUtil == null) {
					observeDataLogUtil = new ObserveDataLogUtil();
				}
			}
		}
		return observeDataLogUtil;
	}

	public void init() {
		getGgaLogFileWriter().deleteRedundantFile();
		getRtcmLogFileWriter().deleteRedundantFile();
	}

	private LogFileWriter getGgaLogFileWriter() {
		if (logFileWriter == null) {
			logFileWriter =
				new LogFileWriter(
					LOG_DIR_PARENT + LOG_DIR_NAME,
					null,
					LOG_FILE_SUFFIX,
					MAX_GGA_FILE_COUNT);
		}
		return logFileWriter;
	}

	private LogFileWriter getRtcmLogFileWriter() {
		if (rtcmlogFileWriter == null) {
			rtcmlogFileWriter =
				new LogFileWriter(
					LOG_DIR_PARENT + LOG_DIR_NAME,
					null,
					LOG_FILE_SUFFIX,
					MAX_GGA_FILE_COUNT);
		}
		return rtcmlogFileWriter;
	}

	/**
	 * 日志输出到文件
	 */
	private void createLogName() {
		if (curTimetamp == null) {
			curTimetamp = logTimeDateFormat.format(System.currentTimeMillis());
			curGgaFilePath =
				LOG_DIR_PARENT
					+ LOG_DIR_NAME
					+ "/"
					+ LOG_GGA_PREFIX
					+ curTimetamp
					+ LOG_FILE_SUFFIX;
			curRtcmFilePath =
				LOG_DIR_PARENT
					+ LOG_DIR_NAME
					+ "/"
					+ LOG_RTCM_PREFIX
					+ curTimetamp
					+ LOG_FILE_SUFFIX;
		}
	}

	public void modifyLogFilePrefix(int type) {
		switch (type) {
			// 观测量日志
			case 0:
				LOG_GGA_PREFIX = "gga_observe_";
				LOG_RTCM_PREFIX = "rtcm_observe_";
				break;
			// 卫星信号
			case 1:
				LOG_GGA_PREFIX = "gga_satellite_";
				LOG_RTCM_PREFIX = "rtcm_satellite_";
				break;
			// 改正数
			case 2:
				LOG_GGA_PREFIX = "gga_corrected_number_";
				LOG_RTCM_PREFIX = "rtcm_corrected_number_";
				break;
		}
		curTimetamp = null;
		createLogName();
	}

	/**
	 * 日志输出到文件
	 */
	public void inputGgaLogToFile(String content) {
		//        if (!ObserveDataControlManager.curCollectControlStatus) {
		//            return;
		//        }
		createLogName();
		File ggaFile = new File(curGgaFilePath);
		if (ggaFile.exists()) {
			if (ggaFile.length() > DEF_GGA_EACH_FILE_MAX_KB) {
				curTimetamp = null;
				createLogName();
				Logger.d(TAG, "new curGgaFilePath = " + curGgaFilePath);
			}
		}
		getGgaLogFileWriter().inputLogToFileNoWrap(curGgaFilePath, content);
	}

	public void inputGgaLogToFile(byte[] content) {
		//        if (!ObserveDataControlManager.curCollectControlStatus) {
		//            return;
		//        }
		createLogName();
		File ggaFile = new File(curGgaFilePath);
		if (ggaFile.exists()) {
			if (ggaFile.length() > DEF_GGA_EACH_FILE_MAX_KB) {
				curTimetamp = null;
				createLogName();
				Logger.d(TAG, "new curGgaFilePath = " + curGgaFilePath);
			}
		}
		getGgaLogFileWriter().inputLogToFile(curGgaFilePath, content);
	}

	/**
	 * 日志输出到文件
	 */
	public void inputRtcmLogToFile(byte[] log) {
		//        if (!ObserveDataControlManager.curCollectControlStatus) {
		//            return;
		//        }
		createLogName();
		File rtcmFile = new File(curRtcmFilePath);
		if (rtcmFile.exists()) {
			if (rtcmFile.length() > DEF_GGA_EACH_FILE_MAX_KB) {
				curTimetamp = null;
				createLogName();
				Logger.d(TAG, "new curRtcmFilePath = " + curRtcmFilePath);
			}
		}
		getRtcmLogFileWriter().inputLogToFile(curRtcmFilePath, log);
	}

	public List<File> getUploadFileList() {
		List<File> childFileList = new ArrayList<>();
		File logFileParentDir = new File(LOG_DIR_PARENT + LOG_DIR_NAME);
		if (!logFileParentDir.exists()) {
			return childFileList;
		}
		File[] childFiles = logFileParentDir.listFiles();
		if (childFiles == null || childFiles.length <= 0) {
			return childFileList;
		}
		for (File file : childFiles) {
			childFileList.add(file);
		}
		Collections.sort(
			childFileList,
			(o1, o2) -> {
				return Long.compare(o1.lastModified(), o2.lastModified());
			});
		if (childFileList.size() < MAX_GGA_FILE_COUNT) {
			return childFileList;
		}
		int needDeleteCount = childFileList.size() - MAX_GGA_FILE_COUNT;
		for (int i = 0; i < needDeleteCount; i++) {
			childFileList.remove(i);
		}
		Logger.d(TAG, "childFileList :" + childFileList.size());
		return childFileList;
	}

	public void deleteFiles(List<File> files) {
		for (File file : files) {
			FileUtil.deleteFile(file);
		}
	}
}

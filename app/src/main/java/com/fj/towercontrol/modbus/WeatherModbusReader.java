package com.fj.towercontrol.modbus;

import android.util.Log;

import com.fj.fjprotocol.ProtocolHelper;
import com.fj.fjprotocol.TransManager;
import com.fj.towercontrol.data.entity.WeatherStationData;
import com.fj.towercontrol.util.CustomModbusReq;
import com.fj.towercontrol.util.MemoryStore;
import com.fjdynamics.app.logger.Logger;
import com.zgkxzx.modbus4And.requset.ModbusParam;
import com.zgkxzx.modbus4And.requset.OnRequestBack;

import java.util.Arrays;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 气象站数据读取类
 *
 * <AUTHOR>
 */
public class WeatherModbusReader {
	private static final String TAG = "WeatherModbusReader";
	// TODO: 2024/1/31 西沙使用的是模块默认的0x01，CIC使用的是0x88
	private static final int SLAVE_ID = 0x01;
	private static final int START_ADDRESS = 0x00;
	private static final int READ_COUNT = 43;
	private static final int REQUEST_INTERVAL = 2_000;
	private final CustomModbusReq modbusReq;
	private ScheduledExecutorService scheduler;
	private boolean connected;

	public void start() {
		if (scheduler == null || scheduler.isShutdown()) {
			scheduler = Executors.newScheduledThreadPool(1);
		}
		scheduler.scheduleWithFixedDelay(() -> {
			if (connected) {
				readHoldingRegisters();
			} else {
				initModbus();
			}
		}, 2_000L, REQUEST_INTERVAL, TimeUnit.MILLISECONDS);
	}

	public void stop() {
		scheduler.shutdownNow();
		modbusReq.destroy();
		connected = false;
	}

	private void initModbus() {
		modbusReq.destroy();
		ModbusParam modbusParam = new ModbusParam()
			.setHost(MemoryStore.getInstance().getSystemConfig().getWeatherStationUrl().getHost())
			.setPort(51001)
			.setEncapsulated(true)
			.setKeepAlive(true)
			.setTimeout(1_500)
			.setRetries(0);
		modbusReq.setParam(modbusParam)
			.init(new OnRequestBack<String>() {
				@Override
				public void onSuccess(String s) {
					Log.d(TAG, "initModbus onSuccess: " + s);
					connected = true;
				}

				@Override
				public void onFailed(String msg) {
					Logger.e(TAG, "initModbus onFailed: " + msg);
					connected = false;
					MemoryStore.getInstance().setWeatherStationData(null);
					TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.updateWeatherData(0, 0, 0, 0, 0));
				}
			});
	}

	private void readHoldingRegisters() {
		modbusReq.readHoldingRegisters(new OnRequestBack<short[]>() {
			@Override
			public void onSuccess(short[] data) {
				Log.d(TAG, "readHoldingRegisters onSuccess: " + Arrays.toString(data));
				if (data == null || data.length < READ_COUNT) {
					return;
				}
				double temperature = data[0] * 0.1;
				double humidity = data[7] * 0.1;
				int windDirection = data[21];
				double windSpeed = data[22] * 0.1;
				double windSpeedAverage = data[23] * 0.1;
				WeatherStationData weatherStationData = new WeatherStationData(temperature, humidity, windDirection, windSpeed, windSpeedAverage);
//				Logger.d(TAG, "weather data -> " + weatherStationData);
				MemoryStore.getInstance().setWeatherStationData(weatherStationData);
				TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.updateWeatherData(temperature, humidity, windDirection, windSpeed, windSpeedAverage));
			}

			@Override
			public void onFailed(String msg) {
				Logger.e(TAG, "readHoldingRegisters onFailed: " + msg);
				MemoryStore.getInstance().setWeatherStationData(null);
				TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.updateWeatherData(0, 0, 0, 0, 0));
			}
		}, SLAVE_ID, START_ADDRESS, READ_COUNT);
	}

	private WeatherModbusReader() {
		modbusReq = new CustomModbusReq();
	}

	private static class SingletonHolder {
		private static final WeatherModbusReader INSTANCE = new WeatherModbusReader();
	}

	public static WeatherModbusReader getInstance() {
		return WeatherModbusReader.SingletonHolder.INSTANCE;
	}
}

package com.fj.towercontrol.modbus;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.fj.towercontrol.data.entity.BatteryInfo;
import com.fj.towercontrol.data.entity.CargoWeightInfo;
import com.fj.towercontrol.data.entity.TowerConfig;
import com.fj.towercontrol.event.MessageEvent;
import com.fj.towercontrol.util.CustomModbusReq;
import com.fj.towercontrol.util.MemoryStore;
import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.Logger;
import com.zgkxzx.modbus4And.requset.ModbusParam;
import com.zgkxzx.modbus4And.requset.OnRequestBack;

import org.greenrobot.eventbus.EventBus;

import java.util.Arrays;

/**
 * 吊钩电池、称重读取类
 *
 * <AUTHOR>
 */
public class HookModbusMaster {
	private static final String TAG = "HookModbusMaster";
	/**
	 * 电池设备地址
	 */
	private static final int SLAVE_ADDRESS_BATTERY = 0xd2;
	/**
	 * 电池寄存器起始地址
	 */
	private static final int START_ADDRESS_BATTERY = 0x28;
	/**
	 * 电池读取的寄存器数量
	 */
	private static final int REGISTER_COUNT_BATTERY = 8;
	/**
	 * 称重设备地址
	 */
	private static final int SLAVE_ADDRESS_WEIGHT = 0x02;
	/**
	 * 称重寄存器起始地址
	 */
	private static final int START_ADDRESS_WEIGHT = 0x00;
	/**
	 * 称重读取的寄存器数量
	 */
	private static final int REGISTER_COUNT_WEIGHT = 7;
	/**
	 * 电池每次读取间隔 in millis
	 */
	private static final long BATTERY_READ_INTERVAL = 10_000L;
	/**
	 * 称重每次读取间隔 in millis
	 */
	private static final long WEIGHT_READ_INTERVAL = 500L;
	/**
	 * 连接延迟时间
	 */
	private static final long INITIAL_CONNECT_DELAY = 2_000L;
	/**
	 * msg-重连
	 */
	private static final int MSG_RECONNECT = 10001;
	/**
	 * msg-进行下一次读取电池数据
	 */
	private static final int MSG_READ_NEXT_BATTERY = 10002;
	/**
	 * msg-进行下一次读取称重数据
	 */
	private static final int MSG_READ_NEXT_WEIGHT = 10003;
	private final CustomModbusReq modbusReq = new CustomModbusReq();
	private final Handler handler;
	private BatteryInfo cacacheBatteryInfo;
	private int batteryErrorCount = 0;

	private HookModbusMaster() {
		handler = new Handler(Looper.getMainLooper()) {
			@Override
			public void handleMessage(@NonNull Message msg) {
				super.handleMessage(msg);
				if (msg.what == MSG_RECONNECT) {
					init();
				} else if (msg.what == MSG_READ_NEXT_BATTERY) {
					readBatteryData();
				} else if (msg.what == MSG_READ_NEXT_WEIGHT) {
					readHookWeighingData();
				}
			}
		};

	}

	private static class SingletonHolder {
		private static final HookModbusMaster INSTANCE = new HookModbusMaster();
	}

	public static HookModbusMaster getInstance() {
		return SingletonHolder.INSTANCE;
	}

	/**
	 * 开始查询
	 */
	public void start() {
		handler.postDelayed(this::init, INITIAL_CONNECT_DELAY);
	}

	/**
	 * 停止查询
	 */
	public void stop() {
		modbusReq.destroy();
		handler.removeCallbacksAndMessages(null);
	}

	private void init() {
		stop();
		ModbusParam modbusParam = new ModbusParam()
			.setHost(MemoryStore.getInstance().getSystemConfig().getHookUrl().getHost())
			.setPort(51001)
			.setEncapsulated(!MemoryStore.getInstance().getSystemConfig().getHookIsUsr())
			.setKeepAlive(true)
			.setTimeout(2000)
			.setRetries(0);
		modbusReq.setParam(modbusParam)
			.init(new OnRequestBack<>() {
				@Override
				public void onSuccess(String s) {
					Logger.d(TAG, "initModbus onSuccess: " + s);
					readBatteryData();
					readHookWeighingData();
				}

				@Override
				public void onFailed(String msg) {
					Logger.e(TAG, "initModbus onFailed: " + msg);
					EventBus.getDefault().post(new MessageEvent(MessageEvent.BATTERY_INFO_UPDATED, null));
					EventBus.getDefault().post(new MessageEvent(MessageEvent.CODE_CARGO_WEIGHT_EVENT, null));
					handler.sendEmptyMessageDelayed(MSG_RECONNECT, INITIAL_CONNECT_DELAY);
				}
			});
	}

	private void readBatteryData() {
		Log.d(TAG, "readBatteryData: start");
		modbusReq.readHoldingRegisters(new OnRequestBack<>() {
			@Override
			public void onSuccess(short[] data) {
				// [125, 30000, 998, 4171, 4170, 62, 62, 0]
				Logger.d(TAG, "readBatteryData onSuccess: " + Arrays.toString(data));
				if (data == null || data.length < REGISTER_COUNT_BATTERY) {
					return;
				}
				double percent = data[2] * 1.0 / 10; // 0.001,800/1000=80%
				int status = data[7]; // 0静止，1充电，2放电
				BatteryInfo batteryInfo = new BatteryInfo(percent, status);
				cacacheBatteryInfo = batteryInfo;
				EventBus.getDefault().post(new MessageEvent(MessageEvent.BATTERY_INFO_UPDATED, batteryInfo));
				handler.sendEmptyMessageDelayed(MSG_READ_NEXT_BATTERY, BATTERY_READ_INTERVAL);
			}

			@Override
			public void onFailed(String msg) {
				Logger.e(TAG, "readBatteryData onFailed: " + msg);
				batteryErrorCount++;
				if (batteryErrorCount < 3) {
					EventBus.getDefault().post(new MessageEvent(MessageEvent.BATTERY_INFO_UPDATED, cacacheBatteryInfo));
				} else {
					EventBus.getDefault().post(new MessageEvent(MessageEvent.BATTERY_INFO_UPDATED, null));
				}
				handler.sendEmptyMessageDelayed(MSG_READ_NEXT_BATTERY, 2_000);
			}
		}, SLAVE_ADDRESS_BATTERY, START_ADDRESS_BATTERY, REGISTER_COUNT_BATTERY);
	}

	private void readHookWeighingData() {
		Log.d(TAG, "readHookWeighingData: start");
		modbusReq.readInputRegisters(new OnRequestBack<>() {
			@Override
			public void onSuccess(byte[] data) {
				if (data.length < 12) {
					return;
				}
				int grossWeight = DataUtil.convertFourSignInt(DataUtil.subBytes(data, 0, 4)) * 10; // 毛重
				int tareWeight = DataUtil.convertFourSignInt(DataUtil.subBytes(data, 4, 4)) * 10; // 皮重
				int netWeight = DataUtil.convertFourSignInt(DataUtil.subBytes(data, 8, 4)) * 10; // 净重
				Logger.d(TAG, "readHookWeighingData onSuccess: " + DataUtil.byte2hex(data) + ", 毛重:" + grossWeight + ", 皮重: " + tareWeight + ", 净重: " + netWeight);
				//使用平台配置的智能吊钩皮重进行去皮
				TowerConfig towerConfig = MemoryStore.getInstance().getTowerConfig();
				double hookPeeledWeight = 0;
				if (towerConfig != null
					&& towerConfig.getCalibration() != null) {
					hookPeeledWeight = towerConfig.getCalibration().getHookPeeledWeight();
				}
				netWeight = Math.max(grossWeight - (int) hookPeeledWeight, 0);
				EventBus.getDefault().post(new MessageEvent(MessageEvent.CODE_CARGO_WEIGHT_EVENT, new CargoWeightInfo(grossWeight, tareWeight, netWeight)));
				handler.sendEmptyMessageDelayed(MSG_READ_NEXT_WEIGHT, WEIGHT_READ_INTERVAL);
			}

			@Override
			public void onFailed(String msg) {
				Logger.e(TAG, "readHookWeighingData onFailed " + msg);
				EventBus.getDefault().post(new MessageEvent(MessageEvent.CODE_CARGO_WEIGHT_EVENT, null));
				handler.sendEmptyMessageDelayed(MSG_READ_NEXT_WEIGHT, WEIGHT_READ_INTERVAL);
			}
		}, SLAVE_ADDRESS_WEIGHT, START_ADDRESS_WEIGHT, REGISTER_COUNT_WEIGHT);
	}

}

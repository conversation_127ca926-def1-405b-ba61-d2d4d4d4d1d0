package com.fj.towercontrol.modbus;

import android.annotation.SuppressLint;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.blankj.utilcode.util.ArrayUtils;
import com.fj.towercontrol.data.entity.VoiceAlarmType;
import com.fj.towercontrol.util.CustomModbusReq;
import com.fj.towercontrol.util.MemoryStore;
import com.zgkxzx.modbus4And.requset.ModbusParam;
import com.zgkxzx.modbus4And.requset.OnRequestBack;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 声音告警模块Modbus通信
 *
 * <AUTHOR>
 * @since 2024/8/19
 */
@SuppressLint("LogNotTimber")
public class VoiceModbusMaster {
	private static final String TAG = "VoiceModbusMaster";
	/**
	 * 连接延迟时间
	 */
	private static final long INITIAL_CONNECT_DELAY = 2_000L;
	/**
	 * 声音告警器设备地址
	 */
	private static final int SLAVE_ADDRESS_VOICE = 0x03;
	private final CustomModbusReq modbusReq = new CustomModbusReq();
	private final Handler handler = new Handler(Looper.getMainLooper());
	private final AtomicBoolean sending = new AtomicBoolean();

	private VoiceModbusMaster() {
	}

	private static class SingletonHolder {
		private static final VoiceModbusMaster INSTANCE = new VoiceModbusMaster();
	}

	public static VoiceModbusMaster getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public void start() {
		handler.postDelayed(this::init, INITIAL_CONNECT_DELAY);
	}

	public void stop() {
		modbusReq.destroy();
		handler.removeCallbacksAndMessages(null);
	}

	private void init() {
		stop();
		ModbusParam modbusParam = new ModbusParam()
			.setHost(MemoryStore.getInstance().getSystemConfig().getVoiceAlarmUrl().getHost())
			.setPort(51001)
			.setEncapsulated(!MemoryStore.getInstance().getSystemConfig().getVoiceIsUsr())
			.setKeepAlive(true)
			.setTimeout(800)
			.setRetries(0);
		modbusReq.setParam(modbusParam)
			.init(new OnRequestBack<String>() {
				@Override
				public void onSuccess(String s) {
					Log.d(TAG, "initModbus onSuccess: " + s);
//					stopVoice();
					configLoopMode();
				}

				@Override
				public void onFailed(String msg) {
					Log.e(TAG, "initModbus onFailed: " + msg);
					handler.postDelayed(() -> init(), INITIAL_CONNECT_DELAY);
				}
			});
	}

	/**
	 * 设置为单曲循环模式
	 */
	private void configLoopMode() {
		modbusReq.writeRegister(new OnRequestBack<String>() {
			@Override
			public void onSuccess(String s) {
				Log.d(TAG, "configLoopMode onSuccess: " + s);
			}

			@Override
			public void onFailed(String s) {
				Log.e(TAG, "configLoopMode onFailed: " + s);
			}
		}, SLAVE_ADDRESS_VOICE, 0x33, 0x04);
	}

//	/**
//	 * 停止播放
//	 */
//	public void stopVoice() {
//		if (!sending.compareAndSet(false, true)) {
//			Log.w(TAG, "stopVoice: last cmd not finished");
//			return;
//		}
//		Log.d(TAG, "stopVoice: start");
//		//停止播放前必须先读取当前播放状态，不然可能会出现未播放时发送停止指令一直不回复的情况
//		readCurrentPlayStatus(new ReadPlayStatusCallback() {
//			@Override
//			public void onSuccess(boolean isPlaying) {
//				if (!isPlaying) {
//					Log.w(TAG, "stopVoice: currently not playing");
//					sending.set(false);
//					return;
//				}
//				stopRealPlay();
//			}
//
//			@Override
//			public void onFailed(String msg) {
//				sending.set(false);
//			}
//		});
//	}


	/**
	 * 播放指定类型告警声音
	 *
	 * @param alarmType {@link VoiceAlarmType}
	 */
	public void playVoice(VoiceAlarmType alarmType) {
		if (!sending.compareAndSet(false, true)) {
			Log.w(TAG, "playVoice: last cmd not finished");
			return;
		}
		Log.d(TAG, "playVoice: start -> " + alarmType.getFileIndex());
		//查询当前播放状态
		readCurrentPlayStatus(new ReadPlayStatusCallback() {
			@Override
			public void onSuccess(boolean isPlaying) {
				if (isPlaying) {
					Log.w(TAG, "playVoice: already playing");
					sending.set(false);
					return;
				}
				//这里必须延迟100ms再发送播放指令，不然会出现指令发送后执行过2~3s才回包的情况
				handler.postDelayed(() -> startRealPlay(alarmType), 100);
			}

			@Override
			public void onFailed(String msg) {
				sending.set(false);
			}
		});

	}

	private void startRealPlay(VoiceAlarmType alarmType) {
		modbusReq.writeRegister(new OnRequestBack<String>() {
			@Override
			public void onSuccess(String s) {
				Log.d(TAG, "startRealPlay onSuccess: " + s);
				sending.set(false);
			}

			@Override
			public void onFailed(String s) {
				Log.e(TAG, "startRealPlay onFailed: " + s);
				sending.set(false);
			}
		}, SLAVE_ADDRESS_VOICE, 0x41, alarmType.getFileIndex());
	}

//	private void stopRealPlay() {
//		Log.d(TAG, "stopRealPlay: start");
//		modbusReq.writeRegister(new OnRequestBack<String>() {
//			@Override
//			public void onSuccess(String s) {
//				Log.d(TAG, "stopRealPlay onSuccess: " + s);
//				sending.set(false);
//			}
//
//			@Override
//			public void onFailed(String s) {
//				Log.e(TAG, "stopRealPlay onFailed: " + s);
//				sending.set(false);
//			}
//		}, SLAVE_ADDRESS_VOICE, 0x0e, 0x01);
//	}

	public void volumeUp() {
		modbusReq.writeRegister(new OnRequestBack<String>() {
			@Override
			public void onSuccess(String s) {
				Log.d(TAG, "volumeUp onSuccess: " + s);
			}

			@Override
			public void onFailed(String s) {
				Log.e(TAG, "volumeUp onFailed: " + s);
			}
		}, SLAVE_ADDRESS_VOICE, 0x05, 0x01);
	}

	public void volumeDown() {
		modbusReq.writeRegister(new OnRequestBack<String>() {
			@Override
			public void onSuccess(String s) {
				Log.d(TAG, "volumeDown onSuccess: " + s);
			}

			@Override
			public void onFailed(String s) {
				Log.e(TAG, "volumeDown onFailed: " + s);
			}
		}, SLAVE_ADDRESS_VOICE, 0x06, 0x01);
	}

	private void readCurrentPlayStatus(ReadPlayStatusCallback callback) {
		modbusReq.readHoldingRegisters(new OnRequestBack<short[]>() {
			@Override
			public void onSuccess(short[] shorts) {
				int length = ArrayUtils.getLength(shorts);
				if (length != 1) {
					Log.e(TAG, "readCurrentPlayStatus: data length mismatch -> " + length);
					return;
				}
				Log.d(TAG, "readCurrentPlayStatus: onSuccess -> " + shorts[0]);
				if (shorts[0] == 1) {
					if (callback != null) {
						callback.onSuccess(true);
					}
				} else {
					if (callback != null) {
						callback.onSuccess(false);
					}
				}
			}

			@Override
			public void onFailed(String s) {
				Log.e(TAG, "readCurrentPlayStatus: onFailed -> " + s);
				if (callback != null) {
					callback.onFailed(s);
				}
			}
		}, SLAVE_ADDRESS_VOICE, 0x10, 0x01);
	}

	public interface ReadPlayStatusCallback {
		void onSuccess(boolean isPlaying);

		void onFailed(String msg);
	}
}

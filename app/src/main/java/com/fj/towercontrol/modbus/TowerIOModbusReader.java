package com.fj.towercontrol.modbus;

import android.util.Log;

import com.fj.fjprotocol.ProtocolHelper;
import com.fj.fjprotocol.TransManager;
import com.fj.towercontrol.util.CustomModbusReq;
import com.fj.towercontrol.util.MemoryStore;
import com.fjdynamics.app.logger.Logger;
import com.zgkxzx.modbus4And.requset.ModbusParam;
import com.zgkxzx.modbus4And.requset.OnRequestBack;

import java.util.Arrays;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 塔上IO信号读取类
 *
 * <AUTHOR>
 */
public class TowerIOModbusReader {
	private static final String TAG = "TowerIOModbusReader";
	private static final int SLAVE_ID = 0x11;
	private static final int START_ADDRESS = 0x20;
	private static final int READ_COUNT = 4;
	private static final int REQUEST_INTERVAL = 1_000;
	private final CustomModbusReq modbusReq;
	private ScheduledExecutorService scheduler;
	private boolean connected;

	public void start() {
		if (scheduler == null || scheduler.isShutdown()) {
			scheduler = Executors.newScheduledThreadPool(1);
		}
		scheduler.scheduleWithFixedDelay(() -> {
			if (connected) {
				readDiscreteInput();
			} else {
				initModbus();
			}
		}, 2_000L, REQUEST_INTERVAL, TimeUnit.MILLISECONDS);
	}

	public void stop() {
		scheduler.shutdownNow();
		modbusReq.destroy();
		connected = false;
	}

	private void initModbus() {
		modbusReq.destroy();
		ModbusParam modbusParam = new ModbusParam()
			.setHost(MemoryStore.getInstance().getSystemConfig().getKeyIoUrl().getHost())
			.setPort(51001)
			.setEncapsulated(true)
			.setKeepAlive(true)
			.setTimeout(800)
			.setRetries(0);
		modbusReq.setParam(modbusParam)
			.init(new OnRequestBack<String>() {
				@Override
				public void onSuccess(String s) {
					Log.d(TAG, "initModbus onSuccess: " + s);
					connected = true;
				}

				@Override
				public void onFailed(String msg) {
					Logger.e(TAG, "initModbus onFailed: " + msg);
					connected = false;
				}
			});
	}

	private void readDiscreteInput() {
		modbusReq.readDiscreteInput(new OnRequestBack<boolean[]>() {
			@Override
			public void onSuccess(boolean[] booleans) {
				Log.d(TAG, "readDiscreteInput onSuccess: " + Arrays.toString(booleans));
				boolean cutoff = booleans[0];//钥匙开关，为1切断塔下控制
				boolean lightOn = booleans[1];//风标指示灯，为1灯亮，为0灯灭
				boolean changed = false;
				if (MemoryStore.getInstance().getCutoff() == null || MemoryStore.getInstance().getCutoff() != cutoff) {
					MemoryStore.getInstance().setCutoff(cutoff);
					changed = true;
				}
				if (MemoryStore.getInstance().getLightOn() == null || MemoryStore.getInstance().getLightOn() != lightOn) {
					MemoryStore.getInstance().setLightOn(lightOn);
					changed = true;
				}
				if (changed) {
					Logger.i(TAG, "io status update: cutoff -> " + cutoff + ", light -> " + lightOn);
					TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.updateCutoffAndLightSignal(MemoryStore.getInstance().getCutoff(), MemoryStore.getInstance().getLightOn()));
				}
			}

			@Override
			public void onFailed(String s) {
				Logger.e(TAG, "readDiscreteInput onFailed: %s");
			}
		}, SLAVE_ID, START_ADDRESS, READ_COUNT);
	}

	/**
	 * 设置开启IO转换模块 条件控制-正向跟随 功能
	 */
	public void setupIOSyncFunction() {
		modbusReq.writeRegisters(new OnRequestBack<String>() {
			@Override
			public void onSuccess(String s) {
				Logger.d(TAG, "setupIOSyncFunction onSuccess");
			}

			@Override
			public void onFailed(String s) {
				Logger.e(TAG, "setupIOSyncFunction onFailed: " + s);
			}
		}, SLAVE_ID, 0x0200, new short[]{0x2000, 0x0101, 0x0000, 0x0000});
	}

	private TowerIOModbusReader() {
		modbusReq = new CustomModbusReq();
	}

	private static class SingletonHolder {
		private static final TowerIOModbusReader INSTANCE = new TowerIOModbusReader();
	}

	public static TowerIOModbusReader getInstance() {
		return TowerIOModbusReader.SingletonHolder.INSTANCE;
	}
}

package com.fj.towercontrol.modbus;

import android.util.Log;

import com.fj.towercontrol.data.entity.TowerCraneData;
import com.fj.towercontrol.event.MessageEvent;
import com.fj.towercontrol.util.CustomModbusReq;
import com.fj.towercontrol.util.MemoryStore;
import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.Logger;
import com.zgkxzx.modbus4And.requset.ModbusParam;
import com.zgkxzx.modbus4And.requset.OnRequestBack;

import org.greenrobot.eventbus.EventBus;

import java.util.Arrays;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 永茂动臂塔机数据读取类
 *
 * <AUTHOR>
 */
public class TowerModbusReader {
	private static final String TAG = "TowerModbusReader";
	private static final int SLAVE_ID = 0x01;
	private static final int START_ADDRESS = DataUtil.byte2int2(new byte[]{0x02, (byte) 0xBE});
	private static final int READ_COUNT = 3;
	private static final int REQUEST_INTERVAL = 1_000;
	private final CustomModbusReq modbusReq;
	private ScheduledExecutorService scheduler;
	private boolean connected;

	public void start() {
		if (scheduler == null || scheduler.isShutdown()) {
			scheduler = Executors.newScheduledThreadPool(1);
		}
		scheduler.scheduleWithFixedDelay(() -> {
			if (connected) {
				readHoldingRegisters();
			} else {
				initModbus();
			}
		}, 2_000L, REQUEST_INTERVAL, TimeUnit.MILLISECONDS);
	}

	public void stop() {
		scheduler.shutdownNow();
		modbusReq.destroy();
		connected = false;
	}

	private void initModbus() {
		modbusReq.destroy();
		ModbusParam modbusParam = new ModbusParam()
			.setHost(MemoryStore.getInstance().getSystemConfig().getYongMaoCraneUrl().getHost())
			.setPort(51001)
			.setEncapsulated(true)
			.setKeepAlive(true)
			.setTimeout(800)
			.setRetries(0);
		modbusReq.setParam(modbusParam)
			.init(new OnRequestBack<String>() {
				@Override
				public void onSuccess(String s) {
					Log.d(TAG, "initModbus onSuccess: " + s);
					connected = true;
				}

				@Override
				public void onFailed(String msg) {
					Logger.e(TAG, "initModbus onFailed: " + msg);
					connected = false;
					EventBus.getDefault().post(new MessageEvent(MessageEvent.CODE_CRANE_DATA_UPDATE, null));
				}
			});
	}

	private void readHoldingRegisters() {
		modbusReq.readHoldingRegisters(new OnRequestBack<short[]>() {
			@Override
			public void onSuccess(short[] data) {
				// [1200,234,250]
				Log.d(TAG, "readHoldingRegisters onSuccess: " + Arrays.toString(data));
				if (data == null || data.length < READ_COUNT) {
					return;
				}
				int weight = data[0] * 10;//12000kg
				double height = data[1] * 1.0 / 10;//23.4m
				double radius = data[2] * 1.0 / 10;//25.0m
				EventBus.getDefault().post(new MessageEvent(MessageEvent.CODE_CRANE_DATA_UPDATE, new TowerCraneData(weight, height, radius)));
			}

			@Override
			public void onFailed(String msg) {
				Logger.e(TAG, "readHoldingRegisters onFailed: " + msg);
				EventBus.getDefault().post(new MessageEvent(MessageEvent.CODE_CRANE_DATA_UPDATE, null));
			}
		}, SLAVE_ID, START_ADDRESS, READ_COUNT);
	}

	private TowerModbusReader() {
		modbusReq = new CustomModbusReq();
	}

	private static class SingletonHolder {
		private static final TowerModbusReader INSTANCE = new TowerModbusReader();
	}

	public static TowerModbusReader getInstance() {
		return TowerModbusReader.SingletonHolder.INSTANCE;
	}
}

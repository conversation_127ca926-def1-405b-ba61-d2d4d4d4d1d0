package com.fj.towercontrol.modbus;

/**
 * 声光告警装置IO信号读取类
 *
 * <AUTHOR>
 */
public class AlarmIOModbusReader {
//	private static final String TAG = "AlarmIOModbusReader";
//	/**
//	 * 尝试重连消息
//	 */
//	private static final int MSG_RETRY_CONNECT = 10001;
//	/**
//	 * 重置声光状态消息
//	 */
//	private static final int MSG_RESET_STATUS = 10002;
//	private static final int SLAVE_ID = 0x11;
//	private static final int START_ADDRESS = 0x00;
//	private final Handler handler;
//	private final CustomModbusReq modbusReq;
//
//	public void start() {
//		handler.postDelayed(this::initModbus, 2_000);
//	}
//
//	public void stop() {
//		modbusReq.destroy();
//		handler.removeMessages(MSG_RETRY_CONNECT);
//		handler.removeMessages(MSG_RESET_STATUS);
//	}
//
//	private void initModbus() {
//		modbusReq.destroy();
//		ModbusParam modbusParam = new ModbusParam()
//			.setHost(MemoryStore.getInstance().getSystemConfig().getAlarmIoUrl().getHost())
//			.setPort(51001)
//			.setEncapsulated(true)
//			.setKeepAlive(true)
//			.setTimeout(800)
//			.setRetries(0);
//		modbusReq.setParam(modbusParam)
//			.init(new OnRequestBack<String>() {
//				@Override
//				public void onSuccess(String s) {
//					Log.d(TAG, "initModbus onSuccess: " + s);
//					handler.removeMessages(MSG_RETRY_CONNECT);
//				}
//
//				@Override
//				public void onFailed(String msg) {
//					Logger.e(TAG, "initModbus onFailed: " + msg);
//					handler.sendEmptyMessageDelayed(MSG_RETRY_CONNECT, 2_000);
//				}
//			});
//	}
//
//	public void controlIO(boolean light, boolean sound) {
//		modbusReq.writeCoils(new OnRequestBack<String>() {
//			@Override
//			public void onSuccess(String s) {
//				Log.d(TAG, "writeCoils onSuccess: " + s);
//				handler.removeMessages(MSG_RESET_STATUS);
//				if (light || sound) {
//					handler.sendEmptyMessageDelayed(MSG_RESET_STATUS, 5_000);
//				}
//			}
//
//			@Override
//			public void onFailed(String s) {
//				Log.e(TAG, "writeCoils onFailed: " + s);
//			}
//		}, SLAVE_ID, START_ADDRESS, new boolean[]{light, sound});
//	}
//
//	private AlarmIOModbusReader() {
//		modbusReq = new CustomModbusReq();
//		handler = new Handler(Looper.getMainLooper()) {
//			@Override
//			public void handleMessage(@NonNull Message msg) {
//				super.handleMessage(msg);
//				if (msg.what == MSG_RETRY_CONNECT) {
//					initModbus();
//				} else if (msg.what == MSG_RESET_STATUS) {
//					controlIO(false, false);
//				}
//			}
//		};
//	}
//
//	private static class SingletonHolder {
//		private static final AlarmIOModbusReader INSTANCE = new AlarmIOModbusReader();
//	}
//
//	public static AlarmIOModbusReader getInstance() {
//		return AlarmIOModbusReader.SingletonHolder.INSTANCE;
//	}
}

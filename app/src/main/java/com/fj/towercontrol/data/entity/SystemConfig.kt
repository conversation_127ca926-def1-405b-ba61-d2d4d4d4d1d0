package com.fj.towercontrol.data.entity

import com.fj.towercontrol.consts.AppConstants
import com.fj.towercontrol.consts.CraneType
import java.net.URI

/**
 * 系统配置
 *
 * <AUTHOR>
 * @since 2024/5/14
 */
data class SystemConfig(
	/**
	 * 塔吊类型 [CraneType]
	 */
	val craneType: Int = CraneType.YONGMAO.ordinal,
	/**
	 * 大屏ws服务地址
	 */
	val largeScreenUrl: URI = AppConstants.DEFAULT_LARGE_SCREEN_URL,
	/**
	 * 智能吊钩称重和电池 485模块转以太网地址
	 */
	val hookUrl: URI = AppConstants.DEFAULT_HOOK_URL,
	/**
	 * 吊钩称重和电池485模块是否是有人模块（有人模块不支持rtu）
	 */
	val hookIsUsr: Boolean = false,
	/**
	 * CIC永茂塔机地址
	 */
	val yongMaoCraneUrl: URI = AppConstants.DEFAULT_YONGMAO_CRANE_URL,
	/**
	 * 塔上钥匙开关IO模块地址
	 */
	val keyIoUrl: URI = AppConstants.DEFAULT_TOWER_TOP_IO_URL,
	/**
	 * 吊钩灯光告警485模块地址
	 */
	val alarmIoUrl: URI = AppConstants.DEFAULT_ALARM_IO_URL,
	/**
	 * 吊钩声音告警485模块地址
	 */
	val voiceAlarmUrl: URI = AppConstants.DEFAULT_VOICE_ALARM_URL,
	/**
	 * 吊钩声音告警485模块是否是有人模块（有人模块不支持rtu）
	 */
	val voiceIsUsr: Boolean = false,
	/**
	 * 气象站地址
	 */
	val weatherStationUrl: URI = AppConstants.DEFAULT_WEATHER_STATION_URL,
	/**
	 * 舵机地址
	 */
	val motorUrl: URI = AppConstants.DEFAULT_MOTOR_URL,
	/**
	 * 舵机俯仰调节限制角度[0-20]
	 */
	val motorLimitAngle: Int = 20,
	/**
	 * 汽车吊告警IO模块地址
	 */
	val carCraneUrl: URI = AppConstants.DEFAULT_CAR_CRANE_URL,
	/**
	 * 汽车吊最大工作半径
	 */
	val carCraneWorkRadius: Double = 47.0,
	/**
	 * 汽车吊防碰撞橙色预警距离
	 */
	val carCraneWarnDistance: Double = 10.0,
	/**
	 * 汽车吊防碰撞红色预警距离
	 */
	val carCraneCriticalDistance: Double = 5.0,
	/**
	 * 汽车吊位置
	 */
	val carCranePosition: Position? = Position(22.425033465, 114.268247972),
	/**
	 * 永茂485转以太网模块地址
	 */
	val yongMao485Url: URI = AppConstants.DEFAULT_YONGMAO_485_URL,
)

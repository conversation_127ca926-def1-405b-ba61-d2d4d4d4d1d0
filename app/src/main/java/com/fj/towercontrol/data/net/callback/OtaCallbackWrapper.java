package com.fj.towercontrol.data.net.callback;

import androidx.annotation.NonNull;

import com.fj.towercontrol.data.net.dto.ota.OtaResponse;

import retrofit2.Call;
import retrofit2.Response;

/**
 * OTA平台回调封装
 *
 * <AUTHOR>
 */
public class OtaCallbackWrapper<T> implements retrofit2.Callback<OtaResponse<T>> {

	private final Callback<T> callback;

	public OtaCallbackWrapper(Callback<T> callback) {
		this.callback = callback;
	}

	@Override
	public void onResponse(@NonNull Call<OtaResponse<T>> call, @NonNull Response<OtaResponse<T>> response) {
		if (callback == null) {
			return;
		}
		if (!response.isSuccessful()) {
			callback.onError("", "网络请求失败");
			return;
		}
		OtaResponse<T> apiResp = response.body();
		if (apiResp == null) {
			callback.onError("", "数据解析异常");
			return;
		}
		if (apiResp.getCode() == 0) {
			callback.onSuccess(apiResp.getData());
		} else {
			callback.onError(String.valueOf(apiResp.getCode()), apiResp.getCodeDesc());
		}
	}

	@Override
	public void onFailure(@NonNull Call<OtaResponse<T>> call, @NonNull Throwable t) {
		if (callback == null) {
			return;
		}
		callback.onError("", "网络异常");
	}
}

package com.fj.towercontrol.data.entity;

import com.opencsv.bean.CsvBindByName;

/**
 * 轨迹点
 *
 * <AUTHOR>
 */
public class HistoryPoint {

	/**
	 * 记录时间戳
	 */
	@CsvBindByName(required = true)
	private long timestamp;
	/**
	 * 纬度
	 */
	@CsvBindByName(required = true)
	private double lat;
	/**
	 * 经度
	 */
	@CsvBindByName(required = true)
	private double lng;
	/**
	 * 高程
	 */
	@CsvBindByName(required = true)
	private double alt;
	/**
	 * 吊钩档位
	 */
	@CsvBindByName
	private int hookGear;
	/**
	 * 小车档位
	 */
	@CsvBindByName
	private int trolleyGear;
	/**
	 * 旋转档位
	 */
	@CsvBindByName
	private int spinGear;

	public HistoryPoint() {
	}

	public HistoryPoint(long timestamp, double lat, double lng, double alt) {
		this.timestamp = timestamp;
		this.lat = lat;
		this.lng = lng;
		this.alt = alt;
	}

	public HistoryPoint(long timestamp, double lat, double lng, double alt, int hookGear, int trolleyGear, int spinGear) {
		this.timestamp = timestamp;
		this.lat = lat;
		this.lng = lng;
		this.alt = alt;
		this.hookGear = hookGear;
		this.trolleyGear = trolleyGear;
		this.spinGear = spinGear;
	}

	public double getLng() {
		return lng;
	}

	public void setLng(double lng) {
		this.lng = lng;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public double getAlt() {
		return alt;
	}

	public void setAlt(double alt) {
		this.alt = alt;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public int getHookGear() {
		return hookGear;
	}

	public void setHookGear(int hookGear) {
		this.hookGear = hookGear;
	}

	public int getTrolleyGear() {
		return trolleyGear;
	}

	public void setTrolleyGear(int trolleyGear) {
		this.trolleyGear = trolleyGear;
	}

	public int getSpinGear() {
		return spinGear;
	}

	public void setSpinGear(int spinGear) {
		this.spinGear = spinGear;
	}
}

package com.fj.towercontrol.data.net.callback;

import androidx.annotation.NonNull;

import com.fj.towercontrol.data.net.dto.platform.ApiResp;

import retrofit2.Call;
import retrofit2.Response;

/**
 * 通用回调处理
 *
 * <AUTHOR>
 */
public class CommonCallbackWrapper<T> implements retrofit2.Callback<ApiResp<T>> {

	private final Callback<T> callback;

	public CommonCallbackWrapper(Callback<T> callback) {
		this.callback = callback;
	}

	@Override
	public void onResponse(@NonNull Call<ApiResp<T>> call, @NonNull Response<ApiResp<T>> response) {
		if (callback == null) {
			return;
		}
		if (!response.isSuccessful()) {
			callback.onError("", "网络请求失败");
			return;
		}
		ApiResp<T> apiResp = response.body();
		if (apiResp == null) {
			callback.onError("", "数据解析异常");
			return;
		}
		if (apiResp.isSuccess()) {
			callback.onSuccess(apiResp.getData());
		} else {
			callback.onError(String.valueOf(apiResp.getCode()), apiResp.getMsg());
		}
	}

	@Override
	public void onFailure(@NonNull Call<ApiResp<T>> call, @NonNull Throwable t) {
		if (callback == null) {
			return;
		}
		callback.onError("", "网络异常");
	}
}

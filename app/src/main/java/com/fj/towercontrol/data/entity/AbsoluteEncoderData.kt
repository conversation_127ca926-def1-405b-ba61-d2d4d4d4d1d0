package com.fj.towercontrol.data.entity

import com.fjd.app.common.util.DataUtil
import com.fjdynamics.app.logger.Logger

/**
 * 绝对值编码器数据
 *
 * <AUTHOR>
 * @since 2025/5/12
 */
data class AbsoluteEncoderData(
	/**
	 * 吊钩编码器值
	 */
	var hookValue: Long = 0,
	/**
	 * 大臂编码器值
	 */
	var jibValue: Long = 0,
) {
	companion object {
		fun parse(bytes: ByteArray): AbsoluteEncoderData {
			val data = AbsoluteEncoderData()
			try {
				data.hookValue = DataUtil.unsigned4BytesToInt(DataUtil.subBytes(bytes, 0, 4), 0)
				data.jibValue = DataUtil.unsigned4BytesToInt(DataUtil.subBytes(bytes, 4, 4), 0)
			} catch (e: Exception) {
				Logger.e("AbsoluteEncoderData", "parse exception: " + e.message)
			}
			return data
		}

	}

}

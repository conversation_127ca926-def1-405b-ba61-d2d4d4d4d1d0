package com.fj.towercontrol.data.entity

import com.fjd.app.common.util.DataUtil
import com.fjdynamics.app.logger.Logger

/**
 * 永茂STT153平头塔吊数据
 *
 * <AUTHOR>
 * @since 2024/11/25
 */
data class YongMaoData(
	/**
	 * 回转角度
	 */
	var rotateAngle: Double = 0.0,
	/**
	 * 小车幅度
	 */
	var trolleyAmplitude: Double = 0.0,
	/**
	 * 动臂俯仰角
	 */
	var boomPitchAngle: Double = 0.0,
	/**
	 * 吊钩高度
	 */
	var hookHeight: Double = 0.0,
	/**
	 * 吊钩起重量
	 */
	var hookWeight: Double = 0.0,
	/**
	 * 起重力矩百分比(xx%)
	 */
	var liftingMomentPercent: Double = 0.0,
	/**
	 * 风速(m/s)
	 */
	var windSpeed: Double = 0.0,
	/**
	 * 大臂长度(m)
	 */
	var boomLength: Double = 0.0,
	/**
	 * 转角传感器值
	 */
	var angleSensor: Double = 0.0,
	/**
	 * 高度传感器值
	 */
	var heightSensor: Double = 0.0,
	/**
	 * 幅度传感器值
	 */
	var amplitudeSensor: Double = 0.0,
	/**
	 * 重量传感器值
	 */
	var weightSensor: Double = 0.0,
	/**
	 * 转角标定系数
	 */
	var angleCalibrationFactor: Double = 0.0,
	/**
	 * 高度标定系数
	 */
	var heightCalibrationFactor: Double = 0.0,
	/**
	 * 幅度标定系数
	 */
	var amplitudeCalibrationFactor: Double = 0.0,
	/**
	 * 重量标定时第一段系数
	 */
	var weightCalibrationFactor1: Double = 0.0,
	/**
	 * 重量标定时第二段系数
	 */
	var weightCalibrationFactor2: Double = 0.0,
	/**
	 * 重量标定时第三段系数
	 */
	var weightCalibrationFactor3: Double = 0.0,
	/**
	 * 吊钩倍率
	 */
	var hookMultiple: Double = 0.0,
	/**
	 * 状态位A
	 */
	var statusA: Int = 0,
	/**
	 * 座舱起升挡位
	 */
	var cabLiftGear: Int = 0,
	/**
	 * 座舱变幅挡位
	 */
	var cabAmplitudeGear: Int = 0,
	/**
	 * 座舱回转挡位
	 */
	var cabRotateGear: Int = 0,
	/**
	 * ECU输出起升挡位
	 */
	var ecuLiftGear: Int = 0,
	/**
	 * ECU输出变幅挡位
	 */
	var ecuAmplitudeGear: Int = 0,
	/**
	 * ECU输出回转挡位
	 */
	var ecuRotateGear: Int = 0,
	/**
	 * 塔机起升挡位
	 */
	var craneLiftGear: Int = 0,
	/**
	 * 塔机变幅挡位
	 */
	var craneAmplitudeGear: Int = 0,
	/**
	 * 塔机回转挡位
	 */
	var craneRotateGear: Int = 0,
) {
	companion object {
		private const val TAG = "YongMaoData"

		fun parse(bytes: ByteArray): YongMaoData {
			val data = YongMaoData()
			try {
				var idx = 0
				var len = 4
				data.rotateAngle = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.trolleyAmplitude = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.boomPitchAngle = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.hookHeight = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.hookWeight = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.liftingMomentPercent = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.windSpeed = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.boomLength = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.angleSensor = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.heightSensor = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.amplitudeSensor = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.weightSensor = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.angleCalibrationFactor = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.heightCalibrationFactor = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.amplitudeCalibrationFactor = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.weightCalibrationFactor1 = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.weightCalibrationFactor2 = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				data.weightCalibrationFactor3 = DataUtil.convertFourSignInt(
					DataUtil.subBytes(
						bytes,
						idx,
						len
					)
				) / 100.0.also { idx += len }
				len = 2
				data.hookMultiple =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)) / 100.0.also { idx += len }
				data.statusA = DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)).also { idx += len }
				data.cabLiftGear = bytes[idx++].toInt()
				data.cabAmplitudeGear = bytes[idx++].toInt()
				data.cabRotateGear = bytes[idx++].toInt()
				data.ecuLiftGear = bytes[idx++].toInt()
				data.ecuAmplitudeGear = bytes[idx++].toInt()
				data.ecuRotateGear = bytes[idx++].toInt()
				data.craneLiftGear = bytes[idx++].toInt()
				data.craneAmplitudeGear = bytes[idx++].toInt()
				data.craneRotateGear = bytes[idx].toInt()
			} catch (e: Exception) {
				Logger.e(TAG, "parse exception: " + e.message)
			}

			return data
		}
	}
}

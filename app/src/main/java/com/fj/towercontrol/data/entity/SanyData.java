package com.fj.towercontrol.data.entity;

import com.fj.towercontrol.util.EcuDataParser;
import com.fjd.app.common.util.DataUtil;
import com.tencent.mars.xlog.Log;

import java.util.List;

public class SanyData {
	private static final String TAG = "SanyData";
	private int birthYear;
	private int type;
	private int nominalTorque;
	private int serialNum;
	private int sysWorkTime;
	private int powerWorkTime;
	private int rotateMechType;
	private int luffMechType;
	private int riseMechType;
	private int armLength;
	private int workMode;
	private int riseGear;
	private int rotateGear;
	private int luffGear;
	private double windSpeed;
	private int multiplier;
	private double amplitude;
	private double height;
	private double angle;
	private double ratedLoad;
	private double realLoad;
	private double tqPercent;
	private List<Integer> upset;
	private List<Integer> leftset;
	private List<Integer> windWarning;
	private int riseInverterErrorCode;
	private int rotateInverterErrorCode;
	private int luffInverterErrorCode;
	private List<Integer> powerOn;
	private List<Integer> tq80Percent;
	private List<Integer> buzzer;
	/**
	 * 塔机远控状态 - 0远控，1近控
	 */
	private int towerRemoteStatus;
	/**
	 * 远程急停状态
	 */
	private int remoteStopStatus;
	/**
	 * 塔机急停状态
	 */
	private int towerStopStatus;
	/**
	 * 塔机心跳计数
	 */
	private int towerHeartbeatCount;
	/**
	 * ECU变幅挡位指令
	 */
	private int ecuVariationGear;
	/**
	 * ECU回转挡位指令
	 */
	private int ecuSpinGear;
	/**
	 * ECU起升挡位指令
	 */
	private int ecuLiftGear;
	/**
	 * ECU故障复位指令
	 */
	private int ecuRecoveryCommand;
	/**
	 * ECU回转抱闸指令
	 */
	private int ecuSpinCommand;
	/**
	 * ECU动力电源指令
	 */
	private int ecuPowerCommand;
	/**
	 * ECU急停指令
	 */
	private int ecuStopCommand;
	/**
	 * ECU请求登录
	 */
	private int ecuRequestLogin;
	/**
	 * ECU变幅挡位指令
	 */
	private int ecuOutputVariationGear;
	/**
	 * ECU回转挡位指令
	 */
	private int ecuOutputSpinGear;
	/**
	 * ECU起升挡位指令
	 */
	private int ecuOutputLiftGear;
	/**
	 * ECU变幅挡位指令
	 */
	private int ecuInputVariationGear;
	/**
	 * ECU回转挡位指令
	 */
	private int ecuInputSpinGear;
	/**
	 * ECU起升挡位指令
	 */
	private int ecuInputLiftGear;

	public SanyData(byte[] data) {
		try {
			int idx = 0;
			int len = 1;
			birthYear = data[idx] & 0xff;
			idx += len;
			type = data[idx] & 0xff;
			idx += len;
			len = 2;
			nominalTorque = DataUtil.byte2ToInt(DataUtil.subBytes(data, idx, len), 0);
			idx += len;
			serialNum = DataUtil.byte2ToInt(DataUtil.subBytes(data, idx, len), 0);
			idx += len;
			len = 4;
			sysWorkTime = DataUtil.byte4ToInt(DataUtil.subBytes(data, idx, len), 0);
			idx += len;
			powerWorkTime = DataUtil.byte4ToInt(DataUtil.subBytes(data, idx, len), 0);
			idx += len;
			len = 1;
			rotateMechType = data[idx] & 0xff;
			idx += len;
			luffMechType = data[idx] & 0xff;
			idx += len;
			riseMechType = data[idx] & 0xff;
			idx += len;
			armLength = data[idx] & 0xff;
			idx += len;
			workMode = data[idx] & 0xff;
			idx += len;
			riseGear = data[idx];
			idx += len;
			rotateGear = data[idx];
			idx += len;
			luffGear = data[idx];
			idx += len;
			len = 2;
			windSpeed = DataUtil.byte2int2(DataUtil.subBytes(data, idx, len)) * 1.0 / 10;
			idx += len;
			len = 1;
			multiplier = data[idx] & 0xff;
			idx += len;
			len = 2;
			amplitude = DataUtil.byte2int2(DataUtil.subBytes(data, idx, len)) * 1.0 / 10;
			idx += len;
			height = DataUtil.byte2int2(DataUtil.subBytes(data, idx, len)) * 1.0 / 10;
			idx += len;
			angle = (DataUtil.byte2int2(DataUtil.subBytes(data, idx, len)) + 15000) * 1.0 / 10;
			idx += len;
			ratedLoad = DataUtil.byte2int2(DataUtil.subBytes(data, idx, len)) * 1.0 / 100;
			idx += len;
			realLoad = DataUtil.byte2int2(DataUtil.subBytes(data, idx, len)) * 1.0 / 100;
			idx += len;
			tqPercent = DataUtil.byte2int2(DataUtil.subBytes(data, idx, len)) * 1.0 / 10;
			idx += len;
			len = 1;
			upset = EcuDataParser.parseIntegerBits(data[idx] & 0xff);
			idx += len;
			leftset = EcuDataParser.parseIntegerBits(data[idx] & 0xff);
			idx += len;
			windWarning = EcuDataParser.parseIntegerBits(data[idx] & 0xff);
			idx += len;
			riseInverterErrorCode = data[idx] & 0xff;
			idx += len;
			rotateInverterErrorCode = data[idx] & 0xff;
			idx += len;
			luffInverterErrorCode = data[idx] & 0xff;
			idx += len;
			powerOn = EcuDataParser.parseIntegerBits(data[idx] & 0xff);
			idx += len;
			tq80Percent = EcuDataParser.parseIntegerBits(data[idx] & 0xff);
			idx += len;
			buzzer = EcuDataParser.parseIntegerBits(data[idx] & 0xff);
			idx += len;
			towerRemoteStatus = data[idx] & 0xff;
			idx += len;
			remoteStopStatus = data[idx] & 0xff;
			idx += len;
			towerStopStatus = data[idx] & 0xff;
			idx += len;
			towerHeartbeatCount = data[idx] & 0xff;
			idx += len;
			ecuVariationGear = data[idx];
			idx += len;
			ecuSpinGear = data[idx];
			idx += len;
			ecuLiftGear = data[idx];
			idx += len;
			ecuRecoveryCommand = data[idx] & 0xff;
			idx += len;
			ecuSpinCommand = data[idx] & 0xff;
			idx += len;
			ecuPowerCommand = data[idx] & 0xff;
			idx += len;
			ecuStopCommand = data[idx] & 0xff;
			idx += len;
			ecuRequestLogin = data[idx++] & 0xff;
			ecuOutputVariationGear = data[idx++];
			ecuOutputSpinGear = data[idx++];
			ecuOutputLiftGear = data[idx++];
			ecuInputVariationGear = data[idx++];
			ecuInputSpinGear = data[idx++];
			ecuInputLiftGear = data[idx++];
		} catch (Exception e) {
			Log.e(TAG, "parse error: " + e.getMessage());
		}
	}

	public int getBirthYear() {
		return birthYear;
	}

	public int getType() {
		return type;
	}

	public int getNominalTorque() {
		return nominalTorque;
	}

	public int getSerialNum() {
		return serialNum;
	}

	public int getSysWorkTime() {
		return sysWorkTime;
	}

	public int getPowerWorkTime() {
		return powerWorkTime;
	}

	public int getRotateMechType() {
		return rotateMechType;
	}

	public int getLuffMechType() {
		return luffMechType;
	}

	public int getRiseMechType() {
		return riseMechType;
	}

	public int getArmLength() {
		return armLength;
	}

	public int getWorkMode() {
		return workMode;
	}

	public int getRiseGear() {
		return riseGear;
	}

	public int getRotateGear() {
		return rotateGear;
	}

	public int getLuffGear() {
		return luffGear;
	}

	public double getWindSpeed() {
		return windSpeed;
	}

	public int getMultiplier() {
		return multiplier;
	}

	public double getAmplitude() {
		return amplitude;
	}

	public double getHeight() {
		return height;
	}

	public double getAngle() {
		return angle;
	}

	public double getRatedLoad() {
		return ratedLoad;
	}

	public double getRealLoad() {
		return realLoad;
	}

	public double getTqPercent() {
		return tqPercent;
	}

	public List<Integer> getUpset() {
		return upset;
	}

	public List<Integer> getLeftset() {
		return leftset;
	}

	public List<Integer> getWindWarning() {
		return windWarning;
	}

	public int getRiseInverterErrorCode() {
		return riseInverterErrorCode;
	}

	public int getRotateInverterErrorCode() {
		return rotateInverterErrorCode;
	}

	public int getLuffInverterErrorCode() {
		return luffInverterErrorCode;
	}

	public List<Integer> getPowerOn() {
		return powerOn;
	}

	public List<Integer> getTq80Percent() {
		return tq80Percent;
	}

	public List<Integer> getBuzzer() {
		return buzzer;
	}

	public int getTowerRemoteStatus() {
		return towerRemoteStatus;
	}

	public int getRemoteStopStatus() {
		return remoteStopStatus;
	}

	public int getTowerStopStatus() {
		return towerStopStatus;
	}

	public int getTowerHeartbeatCount() {
		return towerHeartbeatCount;
	}

	public int getEcuVariationGear() {
		return ecuVariationGear;
	}

	public int getEcuSpinGear() {
		return ecuSpinGear;
	}

	public int getEcuLiftGear() {
		return ecuLiftGear;
	}

	public int getEcuRecoveryCommand() {
		return ecuRecoveryCommand;
	}

	public int getEcuSpinCommand() {
		return ecuSpinCommand;
	}

	public int getEcuPowerCommand() {
		return ecuPowerCommand;
	}

	public int getEcuStopCommand() {
		return ecuStopCommand;
	}

	public int getEcuRequestLogin() {
		return ecuRequestLogin;
	}

	public int getEcuOutputVariationGear() {
		return ecuOutputVariationGear;
	}

	public int getEcuOutputSpinGear() {
		return ecuOutputSpinGear;
	}

	public int getEcuOutputLiftGear() {
		return ecuOutputLiftGear;
	}

	public int getEcuInputVariationGear() {
		return ecuInputVariationGear;
	}

	public int getEcuInputSpinGear() {
		return ecuInputSpinGear;
	}

	public int getEcuInputLiftGear() {
		return ecuInputLiftGear;
	}
}

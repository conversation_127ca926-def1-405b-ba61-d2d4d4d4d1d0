package com.fj.towercontrol.data.entity;

import com.fj.towercontrol.data.net.dto.platform.Calibration;
import com.fj.towercontrol.mqtt.entity.LoadingConfig;

import java.util.List;

/**
 * 平台塔吊配置
 *
 * <AUTHOR>
 */
public class TowerConfig {

	/**
	 * 基本属性配置
	 */
	private TowerBaseConfig towerBaseConfig;
	/**
	 * 限重表
	 */
	private List<LoadingConfig> loadCapacityChart;
	/**
	 * 校准相关配置
	 */
	private Calibration calibration;

	public TowerBaseConfig getTowerBaseConfig() {
		return towerBaseConfig;
	}

	public void setTowerBaseConfig(TowerBaseConfig towerBaseConfig) {
		this.towerBaseConfig = towerBaseConfig;
	}

	public List<LoadingConfig> getLoadCapacityChart() {
		return loadCapacityChart;
	}

	public void setLoadCapacityChart(List<LoadingConfig> loadCapacityChart) {
		this.loadCapacityChart = loadCapacityChart;
	}

	public Calibration getCalibration() {
		return calibration;
	}

	public void setCalibration(Calibration calibration) {
		this.calibration = calibration;
	}
}

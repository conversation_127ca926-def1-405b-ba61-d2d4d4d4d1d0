package com.fj.towercontrol.data.entity

/**
 * 声音告警类型
 *
 * <AUTHOR>
 * @since 2024/12/2
 */
enum class VoiceAlarmType(
	/**
	 * 不同告警类型对应的声音文件索引，从1开始
	 */
	val fileIndex: Int
) {
	/**
	 * 吊物吊运告警
	 */
	LIFTING_ALARM(1),

	/**
	 * 空钩告警
	 */
	HANGING_ALARM(2);

	companion object {
		/**
		 * 将后台发的声音告警类型转换为本地定义的声音告警类型
		 *
		 * @param type 0:吊运中告警 1:空钩告警
		 * @return VoiceAlarmType
		 */
		@JvmStatic
		fun fromVoiceType(type: Int): VoiceAlarmType {
			if (type == 1) {
				return HANGING_ALARM
			}
			return LIFTING_ALARM
		}
	}
}

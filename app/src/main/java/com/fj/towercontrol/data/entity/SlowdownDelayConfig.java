package com.fj.towercontrol.data.entity;

import androidx.annotation.NonNull;

/**
 * 缓停配置
 *
 * <AUTHOR>
 */
public class SlowdownDelayConfig {
	/**
	 * 回转
	 */
	private int spin1;
	private int spin2;
	/**
	 * 变幅
	 */
	private int variation1;
	private int variation2;
	private int variation3;
	private int variation4;
	/**
	 * 吊钩
	 */
	private int hook1;
	private int hook2;
	private int hook3;
	private int hook4;

	public SlowdownDelayConfig(int spin1, int spin2, int variation1, int variation2, int variation3, int variation4, int hook1, int hook2, int hook3, int hook4) {
		this.spin1 = spin1;
		this.spin2 = spin2;
		this.variation1 = variation1;
		this.variation2 = variation2;
		this.variation3 = variation3;
		this.variation4 = variation4;
		this.hook1 = hook1;
		this.hook2 = hook2;
		this.hook3 = hook3;
		this.hook4 = hook4;
	}

	public int getSpin1() {
		return spin1;
	}

	public void setSpin1(int spin1) {
		this.spin1 = spin1;
	}

	public int getSpin2() {
		return spin2;
	}

	public void setSpin2(int spin2) {
		this.spin2 = spin2;
	}

	public int getVariation1() {
		return variation1;
	}

	public void setVariation1(int variation1) {
		this.variation1 = variation1;
	}

	public int getVariation2() {
		return variation2;
	}

	public void setVariation2(int variation2) {
		this.variation2 = variation2;
	}

	public int getVariation3() {
		return variation3;
	}

	public void setVariation3(int variation3) {
		this.variation3 = variation3;
	}

	public int getVariation4() {
		return variation4;
	}

	public void setVariation4(int variation4) {
		this.variation4 = variation4;
	}

	public int getHook1() {
		return hook1;
	}

	public void setHook1(int hook1) {
		this.hook1 = hook1;
	}

	public int getHook2() {
		return hook2;
	}

	public void setHook2(int hook2) {
		this.hook2 = hook2;
	}

	public int getHook3() {
		return hook3;
	}

	public void setHook3(int hook3) {
		this.hook3 = hook3;
	}

	public int getHook4() {
		return hook4;
	}

	public void setHook4(int hook4) {
		this.hook4 = hook4;
	}

	@NonNull
	@Override
	public String toString() {
		return "SlowdownDelayConfig{" +
			"spin1=" + spin1 +
			", spin2=" + spin2 +
			", variation1=" + variation1 +
			", variation2=" + variation2 +
			", variation3=" + variation3 +
			", variation4=" + variation4 +
			", hook1=" + hook1 +
			", hook2=" + hook2 +
			", hook3=" + hook3 +
			", hook4=" + hook4 +
			'}';
	}
}

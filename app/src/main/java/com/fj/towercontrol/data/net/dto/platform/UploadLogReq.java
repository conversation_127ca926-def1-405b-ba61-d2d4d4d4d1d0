package com.fj.towercontrol.data.net.dto.platform;

import com.google.gson.annotations.SerializedName;

public class UploadLogReq {

	private Integer id;
	/**
	 * 文件名称
	 */
	@SerializedName("fileName")
	private String filename;
	/**
	 * 文件归属系统，0 - APP
	 */
	private Integer fileSystem;
	/**
	 * 文件归属设备,0 - 空中1904
	 */
	private Integer fileDevice;
	/**
	 * 上传类型，0 - 手动获取
	 */
	private Integer uploadType;
	/**
	 * 设备sn
	 */
	private String deviceSn;
	/**
	 * 上传用户
	 */
	private String uploadPerson;
	/**
	 * 归属项目，0 - 新鸿基
	 */
	private Integer belongProject;
	/**
	 * 版本
	 */
	private String version;
	/**
	 * 日志文件地址
	 */
	private String fileUrl;
	/**
	 * 创建时间
	 */
	private String createTime;
	/**
	 * 修改时间
	 */
	private String updateTime;
	/**
	 * 0 - 正常,1-删除
	 */
	private Integer isDeleted;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getFilename() {
		return filename;
	}

	public void setFilename(String filename) {
		this.filename = filename;
	}

	public Integer getFileSystem() {
		return fileSystem;
	}

	public void setFileSystem(Integer fileSystem) {
		this.fileSystem = fileSystem;
	}

	public Integer getFileDevice() {
		return fileDevice;
	}

	public void setFileDevice(Integer fileDevice) {
		this.fileDevice = fileDevice;
	}

	public Integer getUploadType() {
		return uploadType;
	}

	public void setUploadType(Integer uploadType) {
		this.uploadType = uploadType;
	}

	public String getDeviceSn() {
		return deviceSn;
	}

	public void setDeviceSn(String deviceSn) {
		this.deviceSn = deviceSn;
	}

	public String getUploadPerson() {
		return uploadPerson;
	}

	public void setUploadPerson(String uploadPerson) {
		this.uploadPerson = uploadPerson;
	}

	public Integer getBelongProject() {
		return belongProject;
	}

	public void setBelongProject(Integer belongProject) {
		this.belongProject = belongProject;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getFileUrl() {
		return fileUrl;
	}

	public void setFileUrl(String fileUrl) {
		this.fileUrl = fileUrl;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}
}

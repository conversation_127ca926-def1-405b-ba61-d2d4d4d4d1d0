package com.fj.towercontrol.data.net.dto.iot;

import java.util.List;

/**
 * iot平台设备自注册接口响应体
 *
 * <AUTHOR>
 */
public class RegisterDeviceResp {
	/**
	 * IOT平台内部设备ID
	 */
	private String id;
	/**
	 * IOT平台内部产品ID
	 */
	private String productId;
	/**
	 * 产品名称
	 */
	private String productName;
	/**
	 * 设备名称
	 */
	private String name;
	/**
	 * 设备sn号
	 */
	private String deviceSn;

	private Integer autoRegister;
	/**
	 * 离在线状态
	 */
	private StateDTO state;
	/**
	 * 设备启用状态
	 */
	private Integer enabledState;
	/**
	 * 设备入网状态
	 */
	private Integer internetAccess;

	private List<?> features;
	/**
	 * 设备数据创建时间
	 */
	private Long createTime;
	/**
	 * 删除标志
	 */
	private String delFlag;
	/**
	 * 设备类型
	 */
	private String deviceType;
	/**
	 * 产品Key
	 */
	private String productKey;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDeviceSn() {
		return deviceSn;
	}

	public void setDeviceSn(String deviceSn) {
		this.deviceSn = deviceSn;
	}

	public Integer getAutoRegister() {
		return autoRegister;
	}

	public void setAutoRegister(Integer autoRegister) {
		this.autoRegister = autoRegister;
	}

	public StateDTO getState() {
		return state;
	}

	public void setState(StateDTO state) {
		this.state = state;
	}

	public Integer getEnabledState() {
		return enabledState;
	}

	public void setEnabledState(Integer enabledState) {
		this.enabledState = enabledState;
	}

	public Integer getInternetAccess() {
		return internetAccess;
	}

	public void setInternetAccess(Integer internetAccess) {
		this.internetAccess = internetAccess;
	}

	public List<?> getFeatures() {
		return features;
	}

	public void setFeatures(List<?> features) {
		this.features = features;
	}

	public Long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public String getDelFlag() {
		return delFlag;
	}

	public void setDelFlag(String delFlag) {
		this.delFlag = delFlag;
	}

	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	public String getProductKey() {
		return productKey;
	}

	public void setProductKey(String productKey) {
		this.productKey = productKey;
	}

	public static class StateDTO {
		private String text;
		private String value;

		public String getText() {
			return text;
		}

		public void setText(String text) {
			this.text = text;
		}

		public String getValue() {
			return value;
		}

		public void setValue(String value) {
			this.value = value;
		}
	}
}

package com.fj.towercontrol.data.net.dto.platform;

import androidx.annotation.NonNull;

/**
 * 塔吊属性接口响应
 *
 * <AUTHOR>
 */
public class TowerConfigDto {
	private long id;
	/**
	 * 设备id
	 */
	private long deviceId;
	/**
	 * 属性名
	 */
	private String propertyName;
	/**
	 * 属性值
	 */
	private String propertyValue;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(long deviceId) {
		this.deviceId = deviceId;
	}

	public String getPropertyName() {
		return propertyName;
	}

	public void setPropertyName(String propertyName) {
		this.propertyName = propertyName;
	}

	public String getPropertyValue() {
		return propertyValue;
	}

	public void setPropertyValue(String propertyValue) {
		this.propertyValue = propertyValue;
	}

	@NonNull
	@Override
	public String toString() {
		return "TowerConfigDto{"
			+ "id="
			+ id
			+ ", deviceId="
			+ deviceId
			+ ", propertyName='"
			+ propertyName
			+ '\''
			+ ", propertyValue='"
			+ propertyValue
			+ '\''
			+ '}';
	}
}

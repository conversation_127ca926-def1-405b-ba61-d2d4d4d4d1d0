package com.fj.towercontrol.data.entity;

import android.os.SystemClock;

public class RadarTargetInfo {
	/**
	 * 传感器id
	 */
	private int radarId;
	/**
	 * 目标障碍物id
	 */
	private int targetId;
	/**
	 * 目标距离
	 */
	private double distance;
	/**
	 * 目标速度
	 */
	private double speed;
	/**
	 * 目标角度
	 */
	private int degree;
	/**
	 * 数据生成时间戳
	 */
	private long timestamp;

	private double disLat;
	private double disLng;
	private double svelLng;
	private double svelLat;

	public RadarTargetInfo(
		int radarId,
		int targetId,
		double distance,
		double speed,
		int degree,
		double disLng,
		double disLat,
		double svelLng,
		double svelLat) {
		this.radarId = radarId;
		this.targetId = targetId;
		this.distance = distance;
		this.speed = speed;
		this.degree = degree;
		this.disLng = disLng;
		this.disLat = disLat;
		this.svelLng = svelLng;
		this.svelLat = svelLat;
		this.timestamp = SystemClock.elapsedRealtime();
	}

	public int getRadarId() {
		return radarId;
	}

	public int getTargetId() {
		return targetId;
	}

	public double getDistance() {
		return distance;
	}

	public double getSpeed() {
		return speed;
	}

	public int getDegree() {
		return degree;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public double getDisLat() {
		return disLat;
	}

	public double getDisLng() {
		return disLng;
	}

	public double getSvelLng() {
		return svelLng;
	}

	public double getSvelLat() {
		return svelLat;
	}

	@Override
	public String toString() {
		return "RadarTargetInfo{"
			+ "radarId="
			+ radarId
			+ ", targetId="
			+ targetId
			+ ", distance="
			+ distance
			+ ", speed="
			+ speed
			+ ", degree="
			+ degree
			+ ", timestamp="
			+ timestamp
			+ ", disLat="
			+ disLat
			+ ", disLng="
			+ disLng
			+ ", svelLng="
			+ svelLng
			+ ", svelLat="
			+ svelLat
			+ '}';
	}
}

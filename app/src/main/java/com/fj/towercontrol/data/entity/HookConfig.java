package com.fj.towercontrol.data.entity;

/**
 * 吊钩相关配置
 *
 * <AUTHOR>
 */
public class HookConfig {
	private int type;
	/**
	 * 吊钩最大离地高度
	 */
	private double maxHeightAboveGround;

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public double getMaxHeightAboveGround() {
		return maxHeightAboveGround;
	}

	public void setMaxHeightAboveGround(double maxHeightAboveGround) {
		this.maxHeightAboveGround = maxHeightAboveGround;
	}
}

package com.fj.towercontrol.data.net.dto.platform

/**
 * 后台接口查询到的终端设备信息DTO
 *
 * <AUTHOR>
 * @since 2024/4/8
 */
data class TerminalDeviceDTO(
	val id: Long,
	val terminalNo: String,
	val terminalName: String,
	val projectId: Long,
	val towerId: Long,
)


/**
 * 终端设备查询请求DTO
 *
 * <AUTHOR>
 * @since 2024/4/8
 */
data class TerminalDeviceListReqDTO(
	val sn: String,
	/**
	 * 终端类型：1-ECU,2-工控机,3-中控平板,4-智慧安全帽
	 */
	val terminalType: String,
)

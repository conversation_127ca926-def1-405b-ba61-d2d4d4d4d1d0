package com.fj.towercontrol.data.net.dto.platform;

/**
 * 平台配置的校准值
 *
 * <AUTHOR>
 */
public class Calibration {
	/**
	 * 塔机称重去皮重量
	 */
	private double peeledWeight;
	/**
	 * 智能吊钩去皮重量
	 */
	private double hookPeeledWeight;
	/**
	 * 倾角校准值
	 */
	private double inclination;

	public double getPeeledWeight() {
		return peeledWeight;
	}

	public void setPeeledWeight(double peeledWeight) {
		this.peeledWeight = peeledWeight;
	}

	public double getInclination() {
		return inclination;
	}

	public void setInclination(double inclination) {
		this.inclination = inclination;
	}

	public double getHookPeeledWeight() {
		return hookPeeledWeight;
	}

	public void setHookPeeledWeight(double hookPeeledWeight) {
		this.hookPeeledWeight = hookPeeledWeight;
	}
}

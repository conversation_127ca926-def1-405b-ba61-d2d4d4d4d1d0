package com.fj.towercontrol.data.entity

import com.google.gson.annotations.SerializedName

/**
 * 位置信息，区别于 [com.fj.fjprotocol.data.GeoData], 不保存 east 和 north 数据
 *
 * <AUTHOR>
 * @since 2024/4/22
 */
data class Position(
	/**
	 * 纬度 in degrees
	 */
	@SerializedName("lat") val latitude: Double,
	/**
	 * 经度 in degrees
	 */
	@SerializedName("lng") val longitude: Double,
	/**
	 * 海拔高度 in meters
	 */
	@SerializedName("alt") val altitude: Double = 0.0
)

package com.fj.towercontrol.data.net.dto.ota;

import java.util.List;

/**
 * 查询OTA信息响应DTO
 *
 * <AUTHOR>
 */
public class OtaInfoRespDTO {
	/**
	 * 云端id
	 */
	private Long id;
	/**
	 * 是否静默下载标志（0：有感、1：静默）
	 */
	private String silentDownloadFlag;
	/**
	 * 是否静默升级标志（0：有感、1：静默）
	 */
	private String autoUpgradeFlag;
	/**
	 * 升级提示语列表
	 */
	private List<?> upgradePromptList;
	/**
	 * 版本号,例如0.0.0.1
	 */
	private String versionNo;
	/**
	 * 升级包类型（0：全量包、1：已使用）
	 */
	private Integer upgradePackageType;
	/**
	 * 是否捆绑升级标志（0：否、1：是）
	 */
	private String bindUpgradeFlag;
	/**
	 * 升级文件列表
	 */
	private List<UpgradeFileListDTO> upgradeFileList;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getSilentDownloadFlag() {
		return silentDownloadFlag;
	}

	public void setSilentDownloadFlag(String silentDownloadFlag) {
		this.silentDownloadFlag = silentDownloadFlag;
	}

	public String getAutoUpgradeFlag() {
		return autoUpgradeFlag;
	}

	public void setAutoUpgradeFlag(String autoUpgradeFlag) {
		this.autoUpgradeFlag = autoUpgradeFlag;
	}

	public List<?> getUpgradePromptList() {
		return upgradePromptList;
	}

	public void setUpgradePromptList(List<?> upgradePromptList) {
		this.upgradePromptList = upgradePromptList;
	}

	public String getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(String versionNo) {
		this.versionNo = versionNo;
	}

	public Integer getUpgradePackageType() {
		return upgradePackageType;
	}

	public void setUpgradePackageType(Integer upgradePackageType) {
		this.upgradePackageType = upgradePackageType;
	}

	public String getBindUpgradeFlag() {
		return bindUpgradeFlag;
	}

	public void setBindUpgradeFlag(String bindUpgradeFlag) {
		this.bindUpgradeFlag = bindUpgradeFlag;
	}

	public List<UpgradeFileListDTO> getUpgradeFileList() {
		return upgradeFileList;
	}

	public void setUpgradeFileList(List<UpgradeFileListDTO> upgradeFileList) {
		this.upgradeFileList = upgradeFileList;
	}

	public static class UpgradeFileListDTO {
		/**
		 * 文件标识，比如"app"
		 */
		private String fileTag;
		/**
		 * 文件版本号,比如"1.0.0.6"
		 */
		private String fileNo;
		/**
		 * 文件名称
		 */
		private String fileName;
		/**
		 * 文件下载地址
		 */
		private String filePath;
		/**
		 * 文件MD5
		 */
		private String fileMd5;
		/**
		 * 文件大小
		 */
		private Long fileSize;

		public String getFileTag() {
			return fileTag;
		}

		public void setFileTag(String fileTag) {
			this.fileTag = fileTag;
		}

		public String getFileNo() {
			return fileNo;
		}

		public void setFileNo(String fileNo) {
			this.fileNo = fileNo;
		}

		public String getFileName() {
			return fileName;
		}

		public void setFileName(String fileName) {
			this.fileName = fileName;
		}

		public String getFilePath() {
			return filePath;
		}

		public void setFilePath(String filePath) {
			this.filePath = filePath;
		}

		public String getFileMd5() {
			return fileMd5;
		}

		public void setFileMd5(String fileMd5) {
			this.fileMd5 = fileMd5;
		}

		public Long getFileSize() {
			return fileSize;
		}

		public void setFileSize(Long fileSize) {
			this.fileSize = fileSize;
		}
	}
}

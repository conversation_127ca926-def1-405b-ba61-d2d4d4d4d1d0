package com.fj.towercontrol.data.net.service;

import com.fj.towercontrol.data.net.ApiManager;
import com.fj.towercontrol.data.net.dto.platform.AlarmConfig;
import com.fj.towercontrol.data.net.dto.platform.ApiResp;
import com.fj.towercontrol.data.net.dto.platform.SystemConfigDTO;
import com.fj.towercontrol.data.net.dto.platform.SystemConfigReqDTO;
import com.fj.towercontrol.data.net.dto.platform.TerminalDeviceDTO;
import com.fj.towercontrol.data.net.dto.platform.TerminalDeviceListReqDTO;
import com.fj.towercontrol.data.net.dto.platform.TowerConfigDto;

import java.util.List;
import java.util.Map;

import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;
import retrofit2.http.PartMap;
import retrofit2.http.Query;

/**
 * 塔吊云平台接口定义
 *
 * <AUTHOR>
 */
public interface PlatformService {

	/**
	 * 上传App日志到云平台
	 *
	 * @param map  参数map
	 * @param file 文件
	 * @return call
	 */
	@Multipart
	@POST("open-api/terminal/log/upload")
	@Headers({"Domain-Name: " + ApiManager.DOMAIN_PLATFORM})
	Call<ApiResp<Object>> uploadLog(@PartMap Map<String, RequestBody> map, @Part MultipartBody.Part file);

	/**
	 * 查询告警规则
	 *
	 * @return call
	 */
	@GET("open-api/config/alarm/list")
	@Headers({"Domain-Name: " + ApiManager.DOMAIN_PLATFORM})
	Call<ApiResp<List<AlarmConfig>>> getAlarmConfigList(@Query("sn") String sn);

	/**
	 * 查询塔吊属性
	 *
	 * @return call
	 */
	@GET("open-api/config/device-property/list")
	@Headers({"Domain-Name: " + ApiManager.DOMAIN_PLATFORM})
	Call<ApiResp<List<TowerConfigDto>>> getTowerConfig(@Query("sn") String sn);

	/**
	 * 查询终端设备列表
	 *
	 * @param req req
	 * @return callback
	 */
	@POST("open-api/terminal/info/list")
	@Headers({"Domain-Name: " + ApiManager.DOMAIN_PLATFORM})
	Call<ApiResp<List<TerminalDeviceDTO>>> getTerminalDeviceList(@Body TerminalDeviceListReqDTO req);

	/**
	 * 查询平台系统配置
	 *
	 * @return call
	 */
	@Headers({"Domain-Name: " + ApiManager.DOMAIN_PLATFORM})
	@POST("open-api/sys/config/list")
	Call<ApiResp<List<SystemConfigDTO>>> getSystemConfigList(@Body SystemConfigReqDTO req);
}

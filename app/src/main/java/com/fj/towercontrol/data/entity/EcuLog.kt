package com.fj.towercontrol.data.entity

import android.util.Log
import com.fj.fjprotocol.data.GeoData
import okio.Buffer
import java.util.Locale

/**
 * Ecu日志上报
 *
 * <AUTHOR>
 */
class EcuLog(
	/**
	 * ECU系统时间戳
	 */
	var timestamp: Long = 0L,
	/**
	 * 吊钩gps状态 (1:单点, 4:固定)
	 */
	var hookGpsStatus: Int = 0,
	/**
	 * 吊钩卫星数量
	 */
	var hookSatelliteCount: Int = 0,
	/**
	 * 吊钩位置
	 */
	var hookLocation: GeoData = GeoData(0.0, 0.0, 0.0),
	/**
	 * 塔身gps状态 (1:单点, 4:固定)
	 */
	var towerGpsStatus: Int = 0,
	/**
	 * 塔身卫星数量
	 */
	var towerSatelliteCount: Int = 0,
	/**
	 * 塔身位置
	 */
	var towerLocation: GeoData = GeoData(0.0, 0.0, 0.0),
	/**
	 * 旋转中心位置
	 */
	var centerLocation: GeoData = GeoData(0.0, 0.0, 0.0)
) {
	companion object {
		private const val TAG = "EcuLog"
		private const val GEO_COORDINATE_DIVISOR = 10_000_000_000.0
		private const val GEO_ALTITUDE_DIVISOR = 10_000.0

		@JvmStatic
		fun fromBytes(data: ByteArray): EcuLog {
			return EcuLog().apply {
				try {
					val buffer = Buffer().write(data)
					timestamp = buffer.readInt().toLong() and 0xFFFFFFFFL
					hookGpsStatus = buffer.readByte().toInt() and 0xFF
					hookSatelliteCount = buffer.readByte().toInt() and 0xFF

					val hookLat = buffer.readLong() / GEO_COORDINATE_DIVISOR
					val hookLng = buffer.readLong() / GEO_COORDINATE_DIVISOR
					val hookAlt = buffer.readInt() / GEO_ALTITUDE_DIVISOR
					val hookNorth = buffer.readInt() / GEO_ALTITUDE_DIVISOR
					val hookEast = buffer.readInt() / GEO_ALTITUDE_DIVISOR
					hookLocation.apply {
						lat = hookLat
						lng = hookLng
						alt = hookAlt
						north = hookNorth
						east = hookEast
					}

					towerGpsStatus = buffer.readByte().toInt() and 0xFF
					towerSatelliteCount = buffer.readByte().toInt() and 0xFF

					val towerLat = buffer.readLong() / GEO_COORDINATE_DIVISOR
					val towerLng = buffer.readLong() / GEO_COORDINATE_DIVISOR
					val towerAlt = buffer.readInt() / GEO_ALTITUDE_DIVISOR
					towerLocation.apply {
						lat = towerLat
						lng = towerLng
						alt = towerAlt
					}

					val centerLat = buffer.readLong() / GEO_COORDINATE_DIVISOR
					val centerLng = buffer.readLong() / GEO_COORDINATE_DIVISOR
					val centerAlt = buffer.readInt() / GEO_ALTITUDE_DIVISOR
					centerLocation.apply {
						lat = centerLat
						lng = centerLng
						alt = centerAlt
					}
				} catch (e: Exception) {
					Log.e(TAG, "ecu log parse error: ${e.message}", e)
				}
			}
		}
	}

	override fun toString(): String {
		return """
            时间戳：$timestamp，
            吊钩gps状态：$hookGpsStatus，卫星数量：$hookSatelliteCount，
            吊钩纬度：${hookLocation.lat}，经度：${hookLocation.lng}，高度：${hookLocation.alt}，
            吊钩北向位置：${hookLocation.north}，东向位置：${hookLocation.east}，
            塔身gps状态：$towerGpsStatus，塔身卫星数量：$towerSatelliteCount，
            塔身纬度：${towerLocation.lat}，经度：${towerLocation.lng}，高度：${towerLocation.alt}，
            旋转中心纬度：${centerLocation.lat}，经度：${centerLocation.lng}，高度：${centerLocation.alt}
        """.trimIndent().format(Locale.getDefault())
	}
}

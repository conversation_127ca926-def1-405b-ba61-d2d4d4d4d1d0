package com.fj.towercontrol.data.net.service;

import com.fj.towercontrol.data.net.ApiManager;
import com.fj.towercontrol.data.net.dto.ota.OtaInfoReqDTO;
import com.fj.towercontrol.data.net.dto.ota.OtaInfoRespDTO;
import com.fj.towercontrol.data.net.dto.ota.OtaResponse;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Streaming;
import retrofit2.http.Url;

/**
 * OTA平台接口定义
 *
 * <AUTHOR>
 */
public interface OtaService {
	/**
	 * 获取平台配置的OTA信息
	 *
	 * @param req req
	 * @return Call
	 */
	@Headers({"Domain-Name: " + ApiManager.DOMAIN_OTA})
	@POST("fj-pandas-ota/ota/getUpgrade")
	Call<OtaResponse<OtaInfoRespDTO>> getOtaInfo(@Body OtaInfoReqDTO req);

	/**
	 * 下载文件<br>
	 * 注意: 下载文件时如果启用了{@link okhttp3.logging.HttpLoggingInterceptor}并且level超过了HEADERS,@Streaming注解将不会生效
	 *
	 * @param downloadUrl 下载地址url
	 * @return 文件流
	 */
	@Streaming
	@GET
	Call<ResponseBody> downloadFile(@Url String downloadUrl);
}

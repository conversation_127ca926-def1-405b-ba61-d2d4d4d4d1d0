package com.fj.towercontrol.data.entity;

import com.fj.fjprotocol.data.GeoData;

/**
 * 半自动任务
 *
 * <AUTHOR>
 */
public class SemiautomaticTask {
	/**
	 * 采点时吊钩的位置信息
	 */
	private GeoData hook;
	/**
	 * 采点时塔身的位置信息
	 */
	private GeoData tower;
	/**
	 * 采点时的塔顶双天线航向
	 */
	private double heading;
	/**
	 * 采点时塔臂的仰角
	 */
	private double jibAngle;

	public SemiautomaticTask(GeoData hook, GeoData tower, double heading, double jibAngle) {
		this.hook = hook;
		this.tower = tower;
		this.heading = heading;
		this.jibAngle = jibAngle;
	}

	public GeoData getHook() {
		return hook;
	}

	public void setHook(GeoData hook) {
		this.hook = hook;
	}

	public GeoData getTower() {
		return tower;
	}

	public void setTower(GeoData tower) {
		this.tower = tower;
	}

	public double getJibAngle() {
		return jibAngle;
	}

	public void setJibAngle(double jibAngle) {
		this.jibAngle = jibAngle;
	}

	public double getHeading() {
		return heading;
	}

	public void setHeading(double heading) {
		this.heading = heading;
	}

}

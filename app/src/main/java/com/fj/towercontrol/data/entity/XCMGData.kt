package com.fj.towercontrol.data.entity

import com.fjd.app.common.util.DataUtil
import com.fjdynamics.app.logger.Logger
import okhttp3.internal.and

/**
 * 徐工(Xuzhou Construction Machinery Group Co., Ltd.)塔吊数据
 *
 * <AUTHOR>
 * @since 2024/8/20
 */
data class XCMGData(
	/**
	 * 额定重量
	 */
	var ratedWeight: Double = 0.0,
	/**
	 * 实际重量
	 */
	var actualWeight: Double = 0.0,
	/**
	 * 力矩百分比
	 */
	var torquePercent: Double = 0.0,
	/**
	 * 变幅幅度
	 */
	var amplitude: Double = 0.0,
	/**
	 * 吊臂长度
	 */
	var jibLength: Double = 0.0,
	/**
	 * 吊臂角度
	 */
	var jibAngle: Double = 0.0,
	/**
	 * 吊钩高度
	 */
	var hookHeight: Double = 0.0,
	/**
	 * 风速
	 */
	var windSpeed: Double = 0.0,
	/**
	 * 回转角度
	 */
	var rotateAngle: Double = 0.0,
	/**
	 * 倍率
	 */
	var multiplicationFactor: Int = 0,
	/**
	 * E故障码
	 */
	var eFaultCode: Int = 0,
	/**
	 * EW故障码
	 */
	var ewFaultCode: Int = 0,
	/**
	 * EC故障码
	 */
	var ecFaultCode: Int = 0,
	/**
	 * ES故障码
	 */
	var esFaultCode: Int = 0,
	/**
	 * 起升变频器故障码
	 */
	var liftInverterFaultCode: Int = 0,
	/**
	 * 变幅变频器故障码
	 */
	var slewInverterFaultCode: Int = 0,
	/**
	 * 回转变频器故障码
	 */
	var rotateInverterFaultCode: Int = 0,
	/**
	 * 塔机控制状态(0：近控 1：远控)
	 */
	var controlState: Int = 0,
	/**
	 * 回转状态（0：制动 1：释放）
	 */
	var rotateState: Int = 0,
	/**
	 * 塔机动力电源状态(0：未上电 1：上电)
	 */
	var powerState: Int = 0,
	/**
	 * 塔机起升挡位
	 */
	var craneLiftGear: Int = 0,
	/**
	 * 塔机变幅挡位
	 */
	var cranSlewGear: Int = 0,
	/**
	 * 塔机回转挡位
	 */
	var craneRotateGear: Int = 0,
	/**
	 * 座舱起升挡位
	 */
	var cockpitLiftGear: Int = 0,
	/**
	 * 座舱变幅挡位
	 */
	var cockpitSlewGear: Int = 0,
	/**
	 * 座舱回转挡位
	 */
	var cockpitRotateGear: Int = 0,
	/**
	 * ECU输出起升挡位
	 */
	var ecuLiftGear: Int = 0,
	/**
	 * ECU输出变幅挡位
	 */
	var ecuSlewGear: Int = 0,
	/**
	 * ECU输出回转挡位
	 */
	var ecuRotateGear: Int = 0,
	/**
	 * 起升变幅器故障
	 */
	var liftAmplitudeError: Int = 0,
	/**
	 * 变幅变幅器故障
	 */
	var slewAmplitudeError: Int = 0,
	/**
	 * 回转变幅器故障
	 */
	var rotateAmplitudeError: Int = 0,
	/**
	 * 塔上急停状态
	 */
	var towerEmergencyStop: Int = 0,
	/**
	 * 座舱急停状态
	 */
	var cockpitEmergencyStop: Int = 0,
	/**
	 * 风标状态
	 */
	var weatherVine: Int = 0,
	/**
	 * 蜗速状态
	 */
	var wormSpeed: Int = 0,
) {
	companion object {
		private const val TAG = "XCMGData"

		fun parse(bytes: ByteArray): XCMGData {
			val data = XCMGData()
			try {
				var idx = 0
				var len = 2
				data.ratedWeight =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)) / 10.0.also { idx += len }
				data.actualWeight =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)) / 10.0.also { idx += len }
				data.torquePercent =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)) / 10.0.also { idx += len }
				data.amplitude =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)) / 10.0.also { idx += len }
				data.jibLength =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)) / 10.0.also { idx += len }
				data.jibAngle =
					DataUtil.convertTwoSignInt(DataUtil.subBytes(bytes, idx, len)) / 10.0.also { idx += len }
				data.hookHeight =
					DataUtil.convertTwoSignInt(DataUtil.subBytes(bytes, idx, len)) / 10.0.also { idx += len }
				data.windSpeed =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)) / 10.0.also { idx += len }
				data.rotateAngle =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)) / 10.0.also { idx += len }
				data.multiplicationFactor =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)).also { idx += len }
				data.eFaultCode = DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)).also { idx += len }
				data.ewFaultCode =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)).also { idx += len }
				data.ecFaultCode =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)).also { idx += len }
				data.esFaultCode =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)).also { idx += len }
				data.liftInverterFaultCode =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)).also { idx += len }
				data.slewInverterFaultCode =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)).also { idx += len }
				data.rotateInverterFaultCode =
					DataUtil.byte2int2(DataUtil.subBytes(bytes, idx, len)).also { idx += len }
				data.controlState = bytes[idx++].and(0xff)
				data.rotateState = bytes[idx++].and(0xff)
				data.powerState = bytes[idx++].and(0xff)
				data.cockpitLiftGear = bytes[idx++].toInt()
				data.cockpitSlewGear = bytes[idx++].toInt()
				data.cockpitRotateGear = bytes[idx++].toInt()
				data.ecuLiftGear = bytes[idx++].toInt()
				data.ecuSlewGear = bytes[idx++].toInt()
				data.ecuRotateGear = bytes[idx++].toInt()
				data.craneLiftGear = bytes[idx++].toInt()
				data.cranSlewGear = bytes[idx++].toInt()
				data.craneRotateGear = bytes[idx++].toInt()
				data.liftAmplitudeError = bytes[idx++].and(0xff)
				data.slewAmplitudeError = bytes[idx++].and(0xff)
				data.rotateAmplitudeError = bytes[idx++].and(0xff)
				data.towerEmergencyStop = bytes[idx++].and(0xff)
				data.cockpitEmergencyStop = bytes[idx++].and(0xff)
				data.weatherVine = bytes[idx++].and(0xff)
				data.wormSpeed = bytes[idx++].and(0xff)
			} catch (e: Exception) {
				Logger.e(TAG, "parse exception: " + e.message)
			}
			return data
		}
	}
}


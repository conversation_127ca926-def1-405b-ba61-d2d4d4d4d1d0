package com.fj.towercontrol.data.entity;

import com.fj.fjprotocol.data.GeoData;
import com.google.gson.annotations.SerializedName;

/**
 * 塔吊基础信息
 *
 * <AUTHOR>
 */
public class TowerBaseConfig {
	/**
	 * 塔臂长度
	 */
	private Double towerArmLength;
	/**
	 * 塔吊品牌
	 */
	private String towerBrand;
	/**
	 * 限重类型（塔机还是限重表）
	 */
	private int radiusWeightLimitType;
	/**
	 * 吊钩相关配置
	 */
	@SerializedName("hookHeight")
	private HookConfig hookConfig;
	/**
	 * 塔基位置信息
	 */
	private GeoData towerPos;
	/**
	 * 塔顶位置信息
	 */
	private GeoData towerTopPos;
	/**
	 * IMU校准参数配置
	 */
	@SerializedName("incCalibration")
	private ImuCalibration imuCalibration;

	public Double getTowerArmLength() {
		return towerArmLength;
	}

	public void setTowerArmLength(Double towerArmLength) {
		this.towerArmLength = towerArmLength;
	}

	public GeoData getTowerPos() {
		return towerPos;
	}

	public void setTowerPos(GeoData towerPos) {
		this.towerPos = towerPos;
	}

	public String getTowerBrand() {
		return towerBrand;
	}

	public void setTowerBrand(String towerBrand) {
		this.towerBrand = towerBrand;
	}

	public int getRadiusWeightLimitType() {
		return radiusWeightLimitType;
	}

	public void setRadiusWeightLimitType(int radiusWeightLimitType) {
		this.radiusWeightLimitType = radiusWeightLimitType;
	}

	public HookConfig getHookConfig() {
		return hookConfig;
	}

	public void setHookConfig(HookConfig hookConfig) {
		this.hookConfig = hookConfig;
	}

	public GeoData getTowerTopPos() {
		return towerTopPos;
	}

	public void setTowerTopPos(GeoData towerTopPos) {
		this.towerTopPos = towerTopPos;
	}

	public ImuCalibration getImuCalibration() {
		return imuCalibration;
	}

	public void setImuCalibration(ImuCalibration imuCalibration) {
		this.imuCalibration = imuCalibration;
	}
}

package com.fj.towercontrol.data.net.interceptor;

import androidx.annotation.NonNull;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 用于添加公共请求头的拦截器
 *
 * <AUTHOR>
 */
public class HeaderInterceptor implements Interceptor {

	@NonNull
	@Override
	public Response intercept(@NonNull Chain chain) throws IOException {
		Request request = chain.request()
			.newBuilder()
			.addHeader("x-platform-header", "1623253851130204162")
			.addHeader("x-organization-header", "1623255766052278273")
			.build();
		return chain.proceed(request);
	}
}

package com.fj.towercontrol.data.entity;

import androidx.annotation.NonNull;

/**
 * 倾斜度
 *
 * <AUTHOR>
 */
public class Vibration {

	private double ax;

	private double ay;

	private double az;

	public Vibration() {
	}

	public Vibration(double ax, double ay, double az) {
		this.ax = ax;
		this.ay = ay;
		this.az = az;
	}

	public double getAx() {
		return ax;
	}

	public void setAx(double ax) {
		this.ax = ax;
	}

	public double getAy() {
		return ay;
	}

	public void setAy(double ay) {
		this.ay = ay;
	}

	public double getAz() {
		return az;
	}

	public void setAz(double az) {
		this.az = az;
	}

	@NonNull
	@Override
	public String toString() {
		return "Vibration{" + "ax=" + ax + ", ay=" + ay + ", az=" + az + '}';
	}
}

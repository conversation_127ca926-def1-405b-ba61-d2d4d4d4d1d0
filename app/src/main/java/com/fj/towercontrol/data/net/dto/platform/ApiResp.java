package com.fj.towercontrol.data.net.dto.platform;

/**
 * 塔吊云平台通用response封装
 *
 * <AUTHOR>
 */
public class ApiResp<T> {

	/**
	 * 响应码
	 */
	private int code;
	/**
	 * 请求是否成功
	 */
	private boolean success;
	/**
	 * 响应消息
	 */
	private String msg;
	/**
	 * 返回数据
	 */
	private T data;

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}
}

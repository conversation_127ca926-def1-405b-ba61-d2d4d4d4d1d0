package com.fj.towercontrol.data.entity;

import androidx.annotation.NonNull;

/**
 * 塔机数据封装
 *
 * <AUTHOR>
 */
public class TowerCraneData {
	/**
	 * 当前重量（kg）
	 */
	private final int cargoWeight;
	/**
	 * 吊钩高度（m）
	 */
	private final double hookHeight;
	/**
	 * 工作半径（m)
	 */
	private final double workRadius;

	public TowerCraneData(int cargoWeight, double hookHeight, double workRadius) {
		this.cargoWeight = cargoWeight;
		this.hookHeight = hookHeight;
		this.workRadius = workRadius;
	}

	public int getCargoWeight() {
		return cargoWeight;
	}

	public double getHookHeight() {
		return hookHeight;
	}

	public double getWorkRadius() {
		return workRadius;
	}

	@NonNull
	@Override
	public String toString() {
		return "TowerCraneData{" +
			"cargoWeight=" + cargoWeight +
			", hookHeight=" + hookHeight +
			", workRadius=" + workRadius +
			'}';
	}
}

package com.fj.towercontrol.data.net.dto.iot;

/**
 * IOT平台设备自注册请求体
 *
 * <AUTHOR>
 */
public class RegisterDeviceReq {

	/**
	 * 必填，产品pdk
	 */
	private String productKey;
	/**
	 * 必填，产品Secret
	 */
	private String productSecret;
	/**
	 * 必填，设备Sn号
	 */
	private String deviceSn;
	/**
	 * 选填，设备名称
	 */
	private String name;

	public RegisterDeviceReq(String productKey, String productSecret, String deviceSn) {
		this(productKey, productSecret, deviceSn, null);
	}

	public RegisterDeviceReq(
		String productKey, String productSecret, String deviceSn, String name) {
		this.productKey = productKey;
		this.productSecret = productSecret;
		this.deviceSn = deviceSn;
		this.name = name;
	}

	public String getProductKey() {
		return productKey;
	}

	public void setProductKey(String productKey) {
		this.productKey = productKey;
	}

	public String getProductSecret() {
		return productSecret;
	}

	public void setProductSecret(String productSecret) {
		this.productSecret = productSecret;
	}

	public String getDeviceSn() {
		return deviceSn;
	}

	public void setDeviceSn(String deviceSn) {
		this.deviceSn = deviceSn;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}

package com.fj.towercontrol.data.net.dto.ota;

import java.util.List;

/**
 * 查询OTA信息请求DTO
 *
 * <AUTHOR>
 */
public class OtaInfoReqDTO {
	/**
	 * 设备sn
	 */
	private String sn;
	/**
	 * 产品pdk
	 */
	private String productKey;
	/**
	 * 大版本号
	 */
	private String versionNo;
	/**
	 * 模块列表
	 */
	private List<ModuleVersionNoListDTO> moduleVersionNoList;

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getProductKey() {
		return productKey;
	}

	public void setProductKey(String productKey) {
		this.productKey = productKey;
	}

	public String getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(String versionNo) {
		this.versionNo = versionNo;
	}

	public List<ModuleVersionNoListDTO> getModuleVersionNoList() {
		return moduleVersionNoList;
	}

	public void setModuleVersionNoList(List<ModuleVersionNoListDTO> moduleVersionNoList) {
		this.moduleVersionNoList = moduleVersionNoList;
	}

	public static class ModuleVersionNoListDTO {
		/**
		 * 模块标识，比如"app"
		 */
		private String moduleTag;
		/**
		 * 版本号
		 */
		private String versionNo;

		public String getModuleTag() {
			return moduleTag;
		}

		public void setModuleTag(String moduleTag) {
			this.moduleTag = moduleTag;
		}

		public String getVersionNo() {
			return versionNo;
		}

		public void setVersionNo(String versionNo) {
			this.versionNo = versionNo;
		}
	}
}

package com.fj.towercontrol.data.entity;

import android.os.Parcel;
import android.os.Parcelable;

import com.fj.fjprotocol.ntrip.bean.AddressInfo;
import com.fj.fjprotocol.ntrip.bean.UserInfo;

/**
 * Everyday is another day, keep going. Created by ramo.wu email: <EMAIL> date:
 * 2021/1/7 21:19 desc:
 *
 * <AUTHOR>
 */
public class NtripInfo implements Parcelable {
	private UserInfo userInfo;
	private AddressInfo addressInfo;
	private String sourcePoint;

	public UserInfo getUserInfo() {
		return userInfo;
	}

	public void setUserInfo(UserInfo userInfo) {
		this.userInfo = userInfo;
	}

	public AddressInfo getAddressInfo() {
		return addressInfo;
	}

	public void setAddressInfo(AddressInfo addressInfo) {
		this.addressInfo = addressInfo;
	}

	public String getSourcePoint() {
		return sourcePoint;
	}

	public void setSourcePoint(String sourcePoint) {
		this.sourcePoint = sourcePoint;
	}

	public NtripInfo() {
	}

	protected NtripInfo(Parcel in) {
		userInfo = in.readParcelable(UserInfo.class.getClassLoader());
		addressInfo = in.readParcelable(AddressInfo.class.getClassLoader());
		sourcePoint = in.readString();
	}

	@Override
	public void writeToParcel(Parcel dest, int flags) {
		dest.writeParcelable(userInfo, flags);
		dest.writeParcelable(addressInfo, flags);
		dest.writeString(sourcePoint);
	}

	@Override
	public int describeContents() {
		return 0;
	}

	public static final Creator<NtripInfo> CREATOR =
		new Creator<NtripInfo>() {
			@Override
			public NtripInfo createFromParcel(Parcel in) {
				return new NtripInfo(in);
			}

			@Override
			public NtripInfo[] newArray(int size) {
				return new NtripInfo[size];
			}
		};
}

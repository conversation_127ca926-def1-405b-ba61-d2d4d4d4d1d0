package com.fj.towercontrol.data.net.service;

import com.fj.towercontrol.data.net.ApiManager;
import com.fj.towercontrol.data.net.dto.iot.IotApiResp;
import com.fj.towercontrol.data.net.dto.iot.RegisterDeviceReq;
import com.fj.towercontrol.data.net.dto.iot.RegisterDeviceResp;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * IOT平台接口定义
 *
 * <AUTHOR>
 */
public interface IotService {

	/**
	 * 设备自注册
	 *
	 * @param req req
	 * @return call
	 */
	@POST("fj-pandas-iot/device/instance/register")
	@Headers({"Domain-Name: " + ApiManager.DOMAIN_IOT})
	Call<IotApiResp<RegisterDeviceResp>> registerDevice(@Body RegisterDeviceReq req);
}

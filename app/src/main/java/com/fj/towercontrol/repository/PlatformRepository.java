package com.fj.towercontrol.repository;

import com.fj.towercontrol.TowerApp;
import com.fj.towercontrol.data.net.ApiManager;
import com.fj.towercontrol.data.net.callback.Callback;
import com.fj.towercontrol.data.net.callback.CommonCallbackWrapper;
import com.fj.towercontrol.data.net.dto.platform.AlarmConfig;
import com.fj.towercontrol.data.net.dto.platform.SystemConfigDTO;
import com.fj.towercontrol.data.net.dto.platform.SystemConfigReqDTO;
import com.fj.towercontrol.data.net.dto.platform.TerminalDeviceDTO;
import com.fj.towercontrol.data.net.dto.platform.TerminalDeviceListReqDTO;
import com.fj.towercontrol.data.net.dto.platform.TowerConfigDto;
import com.fj.towercontrol.data.net.service.PlatformService;

import java.util.List;

/**
 * 云平台相关数据层
 *
 * <AUTHOR>
 */
public class PlatformRepository {

	private static final String TAG = "PlatformRepository";

	private final PlatformService platformService;

	public PlatformRepository() {
		platformService = ApiManager.getInstance().getPlatformService();
	}

	/**
	 * 查询平台告警规则配置
	 *
	 * @param callback callback
	 */
	public void queryPlatformAlarmConfig(Callback<List<AlarmConfig>> callback) {
		platformService
			.getAlarmConfigList(TowerApp.VEHICLE_SN)
			.enqueue(new CommonCallbackWrapper<>(callback));
	}

	/**
	 * 查询平台塔吊属性配置
	 *
	 * @param callback callback
	 */
	public void queryPlatformTowerConfig(Callback<List<TowerConfigDto>> callback) {
		platformService
			.getTowerConfig(TowerApp.VEHICLE_SN)
			.enqueue(new CommonCallbackWrapper<>(callback));
	}

	/**
	 * 查询项目下所有安全帽信息
	 *
	 * @param callback callback
	 */
	public void queryHelmetList(Callback<List<TerminalDeviceDTO>> callback) {
		TerminalDeviceListReqDTO req = new TerminalDeviceListReqDTO(TowerApp.VEHICLE_SN, "4");
		platformService.getTerminalDeviceList(req)
			.enqueue(new CommonCallbackWrapper<>(callback));
	}

	/**
	 * 查询平台系统配置
	 */
	public void querySystemConfigList(Callback<List<SystemConfigDTO>> callback) {
		platformService.getSystemConfigList(new SystemConfigReqDTO())
			.enqueue(new CommonCallbackWrapper<>(callback));
	}
}

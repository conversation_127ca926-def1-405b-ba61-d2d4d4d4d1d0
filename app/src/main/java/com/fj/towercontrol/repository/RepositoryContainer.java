package com.fj.towercontrol.repository;

/**
 * 数据层容器
 *
 * <AUTHOR>
 */
public class RepositoryContainer {

	private final DeviceRepository deviceRepository = new DeviceRepository();

	private final PlatformRepository platformRepository = new PlatformRepository();

	private RepositoryContainer() {
	}

	private static class SingletonHolder {
		private static final RepositoryContainer INSTANCE = new RepositoryContainer();
	}

	public static RepositoryContainer getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public DeviceRepository getDeviceRepository() {
		return deviceRepository;
	}

	public PlatformRepository getPlatformRepository() {
		return platformRepository;
	}
}

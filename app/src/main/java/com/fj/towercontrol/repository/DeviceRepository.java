package com.fj.towercontrol.repository;

import android.util.Log;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.CloseUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.fj.towercontrol.BuildConfig;
import com.fj.towercontrol.TowerApp;
import com.fj.towercontrol.consts.MqttConstants;
import com.fj.towercontrol.data.net.ApiManager;
import com.fj.towercontrol.data.net.callback.Callback;
import com.fj.towercontrol.data.net.callback.CommonCallbackWrapper;
import com.fj.towercontrol.data.net.callback.IotCallbackWrapper;
import com.fj.towercontrol.data.net.callback.OtaCallbackWrapper;
import com.fj.towercontrol.data.net.dto.iot.RegisterDeviceReq;
import com.fj.towercontrol.data.net.dto.iot.RegisterDeviceResp;
import com.fj.towercontrol.data.net.dto.ota.OtaInfoReqDTO;
import com.fj.towercontrol.data.net.dto.ota.OtaInfoRespDTO;
import com.fj.towercontrol.data.net.service.IotService;
import com.fj.towercontrol.data.net.service.OtaService;
import com.fj.towercontrol.data.net.service.PlatformService;
import com.fj.towercontrol.util.ThreadExecutor;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.FileNameMap;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Response;

/**
 * 设备管理Repo
 *
 * <AUTHOR>
 */
public class DeviceRepository {

	private static final String TAG = "DeviceRepository";

	private final PlatformService platformService;
	private final IotService iotService;
	private final OtaService otaService;

	public DeviceRepository() {
		platformService = ApiManager.getInstance().getPlatformService();
		iotService = ApiManager.getInstance().getIotService();
		otaService = ApiManager.getInstance().getOtaService();
	}

	/**
	 * 设备自注册
	 *
	 * @param callback callback
	 */
	public void registerDevice(Callback<RegisterDeviceResp> callback) {
		RegisterDeviceReq req =
			new RegisterDeviceReq(
				MqttConstants.PRODUCT_KEY,
				MqttConstants.PRODUCT_SECRET,
				TowerApp.VEHICLE_SN);
		iotService.registerDevice(req).enqueue(new IotCallbackWrapper<>(callback));
	}

	/**
	 * 上传日志文件
	 *
	 * @param logType  日志类型
	 * @param file     日志文件
	 * @param callback 回调
	 */
	public void uploadLog(int logType, File file, Callback<Object> callback) {
		Map<String, RequestBody> partMap = new HashMap<>();
		partMap.put("sn", createRequestBody(TowerApp.VEHICLE_SN));
		//日志类型，1--app日志、2--ecu日志、3--rtk日志、4--系统日志、5--VCU日志、6--板卡日志、7--ECU高频日志、8--GNSS日志
		partMap.put("logType", createRequestBody(String.valueOf(logType)));
		partMap.put("version", createRequestBody(BuildConfig.VERSION_NAME));
		//日志归属系统，1车机，2遥控器
		partMap.put("logRelSys", createRequestBody("1"));
		//上传类型，1手动上传，2一键获取
		partMap.put("uploadType", createRequestBody("2"));
		MultipartBody.Part filePart = MultipartBody.Part.createFormData("file", file.getName(), RequestBody.create(MediaType.parse(guessMimeType(file.getName())), file));
		platformService.uploadLog(partMap, filePart)
			.enqueue(new CommonCallbackWrapper<>(callback));
	}

	private RequestBody createRequestBody(String value) {
		return RequestBody.create(MediaType.parse("text/plain"), value);
	}

	private String guessMimeType(String filename) {
		FileNameMap fileNameMap = URLConnection.getFileNameMap();
		String contentTypeFor = null;
		try {
			contentTypeFor = fileNameMap.getContentTypeFor(URLEncoder.encode(filename, "UTF-8"));
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		if (contentTypeFor == null) {
			contentTypeFor = "application/octet-stream";
		}
		return contentTypeFor;
	}

	/**
	 * 查询OTA平台配置的升级信息
	 *
	 * @param callback callback
	 */
	public void getOtaInfo(Callback<OtaInfoRespDTO> callback) {
		OtaInfoReqDTO req = new OtaInfoReqDTO();
		req.setSn(TowerApp.VEHICLE_SN);
		req.setProductKey(MqttConstants.PRODUCT_KEY);
		req.setVersionNo("0.0.0.0");
		otaService.getOtaInfo(req)
			.enqueue(new OtaCallbackWrapper<>(callback));
	}

	/**
	 * 下载ota文件
	 *
	 * @param fileUrl  下载url
	 * @param filepath 文件保存路径(包含文件名)
	 * @param listener 下载进度回调
	 */
	public void downloadOtaFile(String fileUrl, String filepath, DownloadListener listener) {
		otaService.downloadFile(fileUrl)
			.enqueue(new retrofit2.Callback<ResponseBody>() {
				@Override
				public void onResponse(@NonNull Call<ResponseBody> call, @NonNull Response<ResponseBody> response) {
					// FIXME: 2024/5/11 现场出现下载文件等待很久的情况，并且过程中还可以再次点击更新，需要下载时显示个进度框
					ThreadExecutor.getInstance().executor(() -> {
						InputStream in = null;
						OutputStream out = null;
						try (ResponseBody body = response.body()) {
							if (body == null) {
								Log.e(TAG, "downloadFile onResponse: body is null");
								if (listener != null) {
									ThreadUtils.runOnUiThread(listener::onFailure);
								}
								return;
							}
							long totalBytes = body.contentLength();
							byte[] buffer = new byte[2048];
							long progressBytes = 0L;
							File targetFile = new File(filepath);
							FileUtils.deleteFilesInDir(targetFile.getParentFile());
							FileUtils.createOrExistsFile(targetFile);
							in = body.byteStream();
							out = new FileOutputStream(targetFile);
							int bytes = in.read(buffer);
							while (bytes >= 0) {
								out.write(buffer, 0, bytes);
								progressBytes += bytes;
								if (listener != null) {
									int progress = (int) ((progressBytes * 100) / totalBytes);
									ThreadUtils.runOnUiThread(() -> listener.onProgress(progress));
								}
								bytes = in.read(buffer);
							}
							Log.d(TAG, "downloadFile onResponse: download complete");
							if (listener != null) {
								ThreadUtils.runOnUiThread(() -> listener.onComplete(targetFile.getAbsolutePath()));
							}
						} catch (Exception e) {
							Log.e(TAG, "downloadFile onResponse: download apk exception: " + e.getMessage());
							if (listener != null) {
								ThreadUtils.runOnUiThread(listener::onFailure);
							}
							e.printStackTrace();
						} finally {
							CloseUtils.closeIOQuietly(in, out);
						}
					});
				}

				@Override
				public void onFailure(@NonNull Call<ResponseBody> call, @NonNull Throwable t) {
					Log.e(TAG, "downloadFile onFailure: %s" + t.getMessage());
					if (listener != null) {
						ThreadUtils.runOnUiThread(listener::onFailure);
					}
				}
			});
	}

	/**
	 * 下载回调
	 *
	 * <AUTHOR>
	 */
	public interface DownloadListener {
		void onProgress(int progress);

		void onComplete(String path);

		void onFailure();
	}
}

package com.fj.towercontrol.tcp;

import com.fj.fjprotocol.ProtocolHelper;
import com.fj.fjprotocol.TransManager;
import com.fj.towercontrol.util.IntelliHookNmeaParser;
import com.fjd.app.common.tcp.TcpServer;
import com.fjdynamics.app.logger.RtkLogger;

import java.nio.charset.StandardCharsets;

/**
 * 吊钩位置数据接收服务
 *
 * <AUTHOR>
 */
public class HookLocationReceiver {

	private static final String TAG = "HookLocationReceiver";
	private final TcpServer tcpServer;
	private long logWriteTimeStamp;

	private HookLocationReceiver() {
		tcpServer = new TcpServer(8887, true, data -> {
			TransManager.getInstance().sendEcuDataWithUart6(ProtocolHelper.sendHookGgaData(data));
			IntelliHookNmeaParser.INSTANCE.addNmeaData(data);
			if (System.currentTimeMillis() - logWriteTimeStamp >= 1_000) {
				//按1hz打印日志，防止惯导高频输出时日志打印过于占用资源
				RtkLogger.d(TAG, "hook received -> " + new String(data, StandardCharsets.UTF_8));
				logWriteTimeStamp = System.currentTimeMillis();
			}
		});
	}

	public void start() {
		tcpServer.start();
	}

	public void stop() {
		tcpServer.stop();
	}

	private static class SingletonHolder {
		private static final HookLocationReceiver INSTANCE = new HookLocationReceiver();
	}

	public static HookLocationReceiver getInstance() {
		return HookLocationReceiver.SingletonHolder.INSTANCE;
	}
}

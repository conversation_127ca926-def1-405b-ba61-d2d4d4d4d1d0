package com.fj.towercontrol.tcp

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.fj.fjprotocol.ProtocolHelper
import com.fj.fjprotocol.TransManager
import com.fj.towercontrol.util.FileStore
import com.fjd.app.common.tcp.client.TcpClient
import com.fjd.app.common.tcp.client.TcpClient.Callback
import com.fjd.app.common.util.DataUtil
import com.fjdynamics.app.logger.Logger

/**
 * 永茂STT153平头塔吊485数据读取
 *
 * <AUTHOR>
 * @since 2024/11/21
 */
object YongMaoDataRetriever {

	private const val TAG = "YongMaoDataRetriever"

//	private const val START_CHAR = '#'.code.toByte()
//	private const val REAL_TIME_DATA_CMD = 0x01.toByte()
//	private const val DATA_LENGTH = 82

	private var tcpClient: TcpClient? = null

	private var handler = Handler(Looper.getMainLooper())
//	private var parserExecutor = Executors.newSingleThreadExecutor()

//	// 添加缓冲区相关常量和变量
//	private const val BUFFER_MAX_SIZE = 1024 * 4 // 4KB缓冲区大小
//	private var buffer = ByteArray(0) // 数据缓冲区
//
//	private var logTimestamp = 0L

	fun connect() {
		if (tcpClient == null) {
			tcpClient =
				TcpClient(FileStore.getSystemConfig().yongMao485Url.host, 51001, 2_000, object : Callback {
					override fun onConnectStatusChanged(connected: Boolean) {
						Logger.i(TAG, "onConnectStatusChanged: $connected")
						if (connected) {
							handler.removeCallbacksAndMessages(null)
							sendQueryCmd()
						} else {
							handler.postDelayed({ connect() }, 2_000)
						}
					}

					override fun onDataReceived(data: ByteArray) {
						Log.d(TAG, "onDataReceived: ${DataUtil.byte2hex(data)}")
						TransManager.getInstance()
							.sendEcuDataWithUart1(ProtocolHelper.sendYongMaoData(data))
//						parseData(data)
					}

				})
		}
		tcpClient?.connect()
	}

	private fun sendQueryCmd() {
		//起始位
		var cmd = byteArrayOf('#'.code.toByte())
		//命令类型
		cmd += 0x02
		//参数地址
		cmd += byteArrayOf(0x00, 0x00)
		//参数数值
		cmd += byteArrayOf(0x00, 0x00, 0x00, 0x00)
		//crc校验
		cmd += DataUtil.yongMaoCrc16(cmd, cmd.size)
		Log.d(TAG, "sendQueryCmd: ${DataUtil.byte2hex(cmd)}")
		tcpClient?.send(cmd)

		handler.postDelayed({ sendQueryCmd() }, 200)
	}

	fun disconnect() {
		handler.removeCallbacksAndMessages(null)
		tcpClient?.disconnect()
		tcpClient = null
	}

//	private fun parseData(data: ByteArray) {
//		parserExecutor.execute {
//			try {
//				// 1. 将新数据添加到缓冲区
//				if (buffer.size > BUFFER_MAX_SIZE) {
//					// 如果缓冲区过大，清空防止内存泄漏
//					Logger.w(TAG, "Buffer overflow, clearing buffer")
//					buffer = ByteArray(0)
//				}
//				buffer = DataUtil.byteMerger(buffer, data)
//
//				// 2. 循环处理缓冲区中的所有完整数据包
//				while (true) {
//					// 查找包头
//					val startIndex = buffer.indexOf(START_CHAR)
//					if (startIndex < 0) {
//						// 没有找到包头，清空缓冲区
//						buffer = ByteArray(0)
//						break
//					}
//
//					// 确保缓冲区中至少包含命令类型字节
//					if (buffer.size < startIndex + 2) {
//						break
//					}
//
//					// 检查命令类型
//					if (buffer[startIndex + 1] != REAL_TIME_DATA_CMD) {
//						// 无效的命令类型，移除这个包头前的数据，继续查找下一个包头
//						buffer = DataUtil.subBytes(buffer, startIndex + 1, buffer.size - (startIndex + 1))
//						continue
//					}
//
//					// 确保缓冲区中至少包含长度字段
//					if (buffer.size < startIndex + 4) {
//						break
//					}
//
//					// 获取数据长度
//					val length = DataUtil.bytes2Int2LSB(DataUtil.subBytes(buffer, startIndex + 2, 2))
//					if (length != DATA_LENGTH) {
//						// 数据长度不正确，移除这个包头前的数据，继续查找下一个包头
//						buffer = DataUtil.subBytes(buffer, startIndex + 1, buffer.size - (startIndex + 1))
//						continue
//					}
//
//					// 检查缓冲区中是否包含完整的数据包
//					if (buffer.size < startIndex + DATA_LENGTH) {
//						// 数据包不完整，等待更多数据
//						break
//					}
//
//					// 提取完整的数据包
//					val validData = DataUtil.subBytes(buffer, startIndex, DATA_LENGTH)
//
//					// 移除已处理的数据
//					buffer = if (startIndex + DATA_LENGTH >= buffer.size) {
//						ByteArray(0)
//					} else {
//						DataUtil.subBytes(
//							buffer,
//							startIndex + DATA_LENGTH,
//							buffer.size - (startIndex + DATA_LENGTH)
//						)
//					}
//
//					// 校验CRC
//					val dataCrc = DataUtil.subBytes(validData, DATA_LENGTH - 2, 2)
//					val calculatedCrc =
//						DataUtil.crc16(DataUtil.subBytes(validData, 0, DATA_LENGTH - 2), DATA_LENGTH - 2)
//
//					if (!dataCrc.contentEquals(calculatedCrc)) {
//						Logger.w(TAG, "CRC check failed")
//						continue
//					}
//
//					// 解析数据包
//					val yongMaoData = YongMaoData(
//						rotateAngle = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 4, 4)
//						) / 100.0,
//						trolleyAmplitude = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 8, 4)
//						) / 100.0,
//						boomPitchAngle = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 12, 4)
//						) / 100.0,
//						hookHeight = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 16, 4)
//						) / 100.0,
//						hookWeight = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 20, 4)
//						) / 100.0,
//						liftingMomentPercent = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 24, 4)
//						) / 100.0,
//						windSpeed = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 28, 4)
//						) / 100.0,
//						boomLength = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 32, 4)
//						) / 100.0,
//						angleSensor = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 36, 4)
//						) / 100.0,
//						heightSensor = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 40, 4)
//						) / 100.0,
//						amplitudeSensor = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 44, 4)
//						) / 100.0,
//						weightSensor = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 48, 4)
//						) / 100.0,
//						angleCalibrationFactor = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 52, 4)
//						) / 100.0,
//						heightCalibrationFactor = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 56, 4)
//						) / 100.0,
//						amplitudeCalibrationFactor = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 60, 4)
//						) / 100.0,
//						weightCalibrationFactor1 = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 64, 4)
//						) / 100.0,
//						weightCalibrationFactor2 = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 68, 4)
//						) / 100.0,
//						weightCalibrationFactor3 = DataUtil.convertFourSignLSB(
//							DataUtil.subBytes(validData, 72, 4)
//						) / 100.0,
//						hookMultiple = DataUtil.bytes2Int2LSB(DataUtil.subBytes(validData, 76, 2)).toDouble(),
//						statusA = DataUtil.bytes2Int2LSB(DataUtil.subBytes(validData, 78, 2))
//					)
//					val currentTimeMillis = System.currentTimeMillis()
//					if (currentTimeMillis - logTimestamp >= 1000) {
//						logTimestamp = currentTimeMillis
//						Log.d(TAG, "Parsed YongMao data: $yongMaoData")
//					}
//				}
//			} catch (e: Exception) {
//				Logger.e(TAG, "Parse data error: ${e.message}")
//			}
//		}
//	}

}

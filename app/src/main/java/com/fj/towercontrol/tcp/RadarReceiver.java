package com.fj.towercontrol.tcp;

import android.os.SystemClock;
import android.util.Pair;

import com.blankj.utilcode.util.StringUtils;
import com.fj.towercontrol.data.entity.RadarTargetInfo;
import com.fj.towercontrol.mqtt.entity.TowerData;
import com.fj.towercontrol.util.MemoryStore;
import com.fjd.app.common.tcp.TcpServer;
import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.Logger;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 毫米波雷达接收服务
 *
 * <AUTHOR>
 */
public class RadarReceiver {

	private static final String TAG = "RadarReceiver";
	/**
	 * 是否打印雷达相关日志（高频，慎打）
	 */
	private static final boolean DEBUG = false;
	/**
	 * 每个雷达目标的超时时间
	 */
	private static final long TIMEOUT = 2_000;
	private static final int PACKET_SIZE = 13;
	private static final byte[] HEAD = new byte[]{(byte) 0x08, (byte) 0x00, (byte) 0x00};
	private final TcpServer tcpServer;
	private final ConcurrentHashMap<Pair<Integer, Integer>, RadarTargetInfo> dataMap = new ConcurrentHashMap<>();
	private byte[] buffer = new byte[0];
	/**
	 * 上一次水平档位方向
	 */
	private int lastHorizontalDirection;
	/**
	 * 上一次垂直方向档位方向
	 */
	private int lastVerticalDirection;
	/**
	 * 维持数据的次数
	 */
	private int horizontalKeepCount;
	private int verticalKeepCount;
	/**
	 * 每秒接收到的target数量统计
	 */
	private int targetSum;

	private RadarReceiver() {
		tcpServer = new TcpServer(8889, this::spiltData);
	}

	private void spiltData(byte[] data) {
		buffer = appendBuffer(data);
		while (buffer.length >= PACKET_SIZE) {
			int headIndex = findHead(buffer);
			if (headIndex < 0) {
				// 未找到帧头
				return;
			}
			int tailIndex = headIndex + PACKET_SIZE - 1;
			if (tailIndex > buffer.length - 1) {
				// 当前数据不完整，等待下一包
				return;
			}
			int clipStartIndex;
			// 找到帧尾，回调这一包数据
			byte[] frameData = DataUtil.subBytes(buffer, headIndex, PACKET_SIZE);
			parseData(frameData);
			// 校验通过裁剪帧
			clipStartIndex = tailIndex + 1;
			buffer = DataUtil.subBytes(buffer, clipStartIndex, buffer.length - clipStartIndex);
		}
	}

	private void parseData(byte[] frameData) {
//		Log.e(TAG, "parseData -> " + DataUtil.byte2hex(frameData));
		int[] data = new int[frameData.length];
		for (int i = 0; i < frameData.length; i++) {
			data[i] = frameData[i] & 0xff;
		}
		if (data[3] != 0x06) {
			return;
		}
		if ((data[4] & 0x0f) == 0x0b) {
			targetSum++;
			int radarId = data[4] >> 4;
			if (radarId < 1 || radarId > 6) {
				//只有1-6号雷达
				return;
			}
			int targetId = data[5] & 0xff;
			//纵向距离
			double disLng;
			//横向距离
			double disLat;
			if (radarId < 5) {
				//1-4号SR71
				disLng = (data[6] * 32 + (data[7] >> 3)) * 0.1 - 500;
				disLat = ((data[7] & 0x07) * 256 + data[8]) * 0.1 - 102.3;
			} else {
				//5-6号SR73F
				disLng = (data[6] * 32 + (data[7] >> 3)) * 0.2 - 500;
				disLat = ((data[7] & 0x07) * 256 + data[8]) * 0.2 - 204.6;
			}
			//纵向速度
			double svelLng = (data[9] * 4 + (data[10] >> 6)) * 0.25 - 128;
			//横向速度
			double svelLat = ((data[10] & 0x3F) * 8 + (data[11] >> 5)) * 0.25 - 64;
			//雷达检测目标横截面面接
			//            double rcs = (data[12] * 0.5) - 64;
			//目标角度
			double azimuth = Math.atan2(disLat, disLng); //弧度
			int degree = (int) (azimuth * 180 / Math.PI);
			double distance = Math.sqrt(disLng * disLng + disLat * disLat);
//			if (radarId < 5) {
//				//四周雷达
////				if (degree < -45 || degree > 45) {
////					//只检测90°内的目标
////					return;
////				}
//				//过滤雷达打地数据
//				int installAngle = MemoryStore.getInstance().getRadarInstallAngle();
//				double threshold = MemoryStore.getInstance().getGroundHeightThreshold();
//				if (threshold > 0 && installAngle < 0 && degree < Math.abs(installAngle)) {
//					double vDistance = Calculator.calculateTargetVerticalDistance(distance, degree, installAngle);
//					if (Math.abs(vDistance - MemoryStore.getInstance().getHookHeight()) < threshold / 100) {
//						return;
//					}
//				}
//
//				//过滤雷达打到吊物数据
//				int realAngle = degree + installAngle;
//				if (realAngle < 0) {
//					//目标与水平面夹角rad
//					double angleInRadians = Math.toRadians(Math.abs(realAngle));
//					//目标点水平方向到吊物中心点距离
//					double horizontalDistance = Math.cos(angleInRadians) * distance + 0.325;
//					//目标点竖直方向到雷达距离
//					double verticalDistance = Math.sin(angleInRadians) * distance;
//					//吊物半径
//					double liftRadius = MemoryStore.getInstance().getCargoRadius();
//					//雷达到吊物竖直方向距离
//					double radarVerticalDistance = MemoryStore.getInstance().getRadarToLiftDistance();
//					double horizontalThreshold = MemoryStore.getInstance().getLiftHorizontalThreshold() / 100;
//					double verticalThreshold = MemoryStore.getInstance().getLiftVerticalThreshold() / 100;
//					if (horizontalDistance - liftRadius <= horizontalThreshold
//						&& Math.abs(verticalDistance - radarVerticalDistance) <= verticalThreshold) {
//						return;
//					}
//				}
//			} else {
//				//底部雷达
//				double start = MemoryStore.getInstance().getRadarStartAngle();
//				double end = MemoryStore.getInstance().getRadarEndAngle();
//				//只检测在配置的检测区间内的target
//				if (start != 0
//					&& degree > start * -1
//					&& degree < start) {
//					return;
//				}
//				if (degree > end || degree < end * -1) {
//					return;
//				}
//			}
			double speed = svelLng * Math.cos(azimuth) + svelLat * Math.sin(azimuth);
			RadarTargetInfo targetInfo = new RadarTargetInfo(radarId, targetId, distance, speed, degree, disLng, disLat, svelLng, svelLat);
			putDataIntoMap(targetInfo);
		}
	}

	private void putDataIntoMap(RadarTargetInfo targetInfo) {
//		if (DEBUG) {
//			Log.e(TAG, "target: " + targetInfo);
//		}
		dataMap.put(new Pair<>(targetInfo.getRadarId(), targetInfo.getTargetId()), targetInfo);
	}

	private byte[] appendBuffer(byte[] data) {
		byte[] result = new byte[buffer.length + data.length];
		System.arraycopy(buffer, 0, result, 0, buffer.length);
		System.arraycopy(data, 0, result, buffer.length, data.length);
		return result;
	}

	private int findHead(byte[] data) {
		for (int i = 0; i < data.length - 2; ++i) {
			if (data[i] == HEAD[0] && data[i + 1] == HEAD[1] && data[i + 2] == HEAD[2]) {
				return i;
			}
		}
		return -1;
	}

	public ConcurrentHashMap<Pair<Integer, Integer>, RadarTargetInfo> getDataMap() {
		return dataMap;
	}

	public void start() {
		tcpServer.start();
	}

	public void stop() {
		tcpServer.stop();
	}

	/**
	 * 获取最近目标信息
	 *
	 * @param towerData 当前塔吊状态
	 * @return result[0]-四周最近目标信息, result[1]底部最近目标信息
	 */
	public RadarTargetInfo[] findNearestTarget(TowerData towerData) {
		long timestamp = SystemClock.elapsedRealtime();
		//吊钩档位,-4~0~4,负值下降
		int hookGear = towerData.getRemoteHookGear();
		//塔臂档位,-4~0~4,负值后退
		int armGear = towerData.getRemoteTrolleyGear();
		//回转档位,-2~0~2,负值向左
		int spinGear = towerData.getRemoteSpinGear();
		int radarSpinAngle = (int) towerData.getRadarShiftingAngle();
		int filteredRadarId = 0;
		//当前水平档位方向
		int horizontalDirection = 0;
		if (armGear != 0 || spinGear != 0) {
			if (Math.abs(armGear) >= Math.abs(spinGear)) {
				if (armGear >= 0) {
					horizontalDirection = 1;
				} else {
					horizontalDirection = 2;
				}
			} else {
				if (spinGear >= 0) {
					horizontalDirection = 3;
				} else {
					horizontalDirection = 4;
				}
			}
		}
		//当前垂直档位方向
		int verticalDirection = 0;
		if (hookGear != 0) {
			if (hookGear > 0) {
				verticalDirection = 1;
			} else {
				verticalDirection = -1;
			}
		}
		if (DEBUG) {
			Logger.d(
				TAG,
				StringUtils.format(
					"findNearestTarget: hookGear -> %s, armGear -> %s, spinGear -> %s, radarSpinAngle -> %s, horizontalDirection -> %s, lastHorizontalDirection -> %s, horizontalKeepCount -> %s, targetSum -> %s, filteredSum -> %s",
					hookGear, armGear, spinGear, radarSpinAngle, horizontalDirection, lastHorizontalDirection, horizontalKeepCount, targetSum, dataMap.size())
			);
		}
		targetSum = 0;

		//水平档位方向变化后维持5s原来档位方向的雷达
		if (lastHorizontalDirection != 0) {
			if (horizontalDirection != lastHorizontalDirection) {
				horizontalKeepCount++;
				if (horizontalKeepCount > 5) {
					horizontalKeepCount = 0;
				} else {
					horizontalDirection = lastHorizontalDirection;
				}
			}
		} else {
			horizontalKeepCount = 0;
		}
		lastHorizontalDirection = horizontalDirection;

		//垂直档位方向变化后维持5s原来档位方向的雷达
		if (lastVerticalDirection != 0) {
			if (verticalDirection != lastVerticalDirection) {
				verticalKeepCount++;
				if (verticalKeepCount > 5) {
					verticalKeepCount = 0;
				} else {
					verticalDirection = lastVerticalDirection;
				}
			}
		} else {
			verticalKeepCount = 0;
		}
		lastVerticalDirection = verticalDirection;

		if (horizontalDirection == 1) {
			filteredRadarId = (360 - radarSpinAngle + 45) % 360 / 90 + 1;
		} else if (horizontalDirection == 2) {
			filteredRadarId = (360 + 180 - radarSpinAngle + 45) % 360 / 90 + 1;
		} else if (horizontalDirection == 3) {
			filteredRadarId = (360 + 90 - radarSpinAngle + 45) % 360 / 90 + 1;
		} else if (horizontalDirection == 4) {
			filteredRadarId = (360 + 270 - radarSpinAngle + 45) % 360 / 90 + 1;
		}
		if (DEBUG) {
			Logger.d(TAG, StringUtils.format("findNearestTarget: filteredRadarId -> %s", filteredRadarId));
		}
		Set<Map.Entry<Pair<Integer, Integer>, RadarTargetInfo>> entries = dataMap.entrySet();
		RadarTargetInfo circleTarget = null;
		RadarTargetInfo bottomTarget = null;
		for (Map.Entry<Pair<Integer, Integer>, RadarTargetInfo> entry : entries) {
			RadarTargetInfo targetInfo = entry.getValue();
			if (timestamp - targetInfo.getTimestamp() > TIMEOUT) {
				// 忽略两秒未更新的数据
				dataMap.remove(entry.getKey());
				continue;
			}
			int radarId = targetInfo.getRadarId();
			if (radarId < 5) {
				//周边四个雷达
				if (filteredRadarId != 0
					&& radarId != filteredRadarId) {
					//忽略非过滤后的雷达的目标信息
					continue;
				}
				if (verticalDirection != 0) {
					//忽略非当前垂直运动方向上的目标数据
					int realAngle = targetInfo.getDegree() + MemoryStore.getInstance().getRadarInstallAngle();
					if (verticalDirection > 0 && realAngle < 0) {
						continue;
					} else if (verticalDirection < 0 && realAngle > 0) {
						continue;
					}
				}
				if (circleTarget == null
					|| circleTarget.getDistance() > targetInfo.getDistance()) {
					circleTarget = targetInfo;
				}
			} else {
				//底部雷达
				if (Math.abs(targetInfo.getDisLng()) < MemoryStore.getInstance().getRadarDistance()) {
					//忽略小于配置的雷达距吊钩距离的障碍物
					continue;
				}
				if (bottomTarget == null
					|| bottomTarget.getDistance() > targetInfo.getDistance()) {
					bottomTarget = targetInfo;
				}
			}
		}
		RadarTargetInfo[] result = new RadarTargetInfo[2];
		result[0] = circleTarget;
		result[1] = bottomTarget;
		return result;
	}

	private static class SingletonHolder {
		private static final RadarReceiver INSTANCE = new RadarReceiver();
	}

	public static RadarReceiver getInstance() {
		return RadarReceiver.SingletonHolder.INSTANCE;
	}
}

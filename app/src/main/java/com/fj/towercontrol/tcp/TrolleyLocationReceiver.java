package com.fj.towercontrol.tcp;

import com.fj.fjprotocol.ProtocolHelper;
import com.fj.fjprotocol.TransManager;
import com.fjd.app.common.tcp.TcpServer;
import com.fjdynamics.app.logger.Logger;

import java.nio.charset.StandardCharsets;

/**
 * 小车位置数据接收服务
 *
 * <AUTHOR>
 */
public class TrolleyLocationReceiver {

	private static final String TAG = "TrolleyLocationReceiver";

	private final TcpServer tcpServer;

	private TrolleyLocationReceiver() {
		tcpServer = new TcpServer(8888, data -> {
			String ggaStr = new String(data, StandardCharsets.UTF_8);
			Logger.d(TAG, "trolley received -> " + ggaStr);
			TransManager.getInstance()
				.sendEcuDataWithUart6(ProtocolHelper.sendGearGgaData(ggaStr));
		});
	}

	public void start() {
		tcpServer.start();
	}

	public void stop() {
		tcpServer.stop();
	}

	private static class SingletonHolder {
		private static final TrolleyLocationReceiver INSTANCE = new TrolleyLocationReceiver();
	}

	public static TrolleyLocationReceiver getInstance() {
		return TrolleyLocationReceiver.SingletonHolder.INSTANCE;
	}
}

package com.fj.towercontrol.tcp

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.fj.towercontrol.util.ImuDataParser
import com.fjd.app.common.tcp.client.TcpClient
import com.fjd.app.common.util.DataUtil

/**
 * 倾角仪CAN转以太网模块客户端
 *
 * <AUTHOR>
 * @since 2025/8/15
 */
object ImuTcpClient {
	private const val TAG = "ImuTcpClient"
	private var connected: Boolean = false
	private var handler = Handler(Looper.getMainLooper())
	private val dataParser = ImuDataParser {
		// TODO: handle data

	}

	private val tcpClient: TcpClient = TcpClient("10.64.176.234", 4001, object : TcpClient.Callback {
		override fun onConnectStatusChanged(connected: Boolean) {
			<EMAIL> = connected
			Log.i(TAG, "onConnectStatusChanged: $connected")
			if (!connected) {
				handler.postDelayed({ connect() }, 2_000)
			} else {
				handler.removeCallbacksAndMessages(null)
			}
		}

		override fun onDataReceived(data: ByteArray) {
			Log.d(TAG, "onDataReceived: ${DataUtil.byte2hex(data)}")
			dataParser.addData(data)
		}

	})

	fun connect() {
		if (connected) {
			return
		}
		tcpClient.connect()
	}

	fun sendData(data: ByteArray) {
		if (!connected) {
			return
		}
		Log.d(TAG, "sendData: ${DataUtil.byte2hex(data)}")
		tcpClient.send(data)
	}
}

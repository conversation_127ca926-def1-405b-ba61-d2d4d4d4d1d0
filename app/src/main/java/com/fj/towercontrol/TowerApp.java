package com.fj.towercontrol;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Context;
import android.os.Handler;
import android.os.SystemClock;
import android.text.TextUtils;

import com.blankj.utilcode.util.GsonUtils;
import com.fj.fjprotocol.ControlCenterManager;
import com.fj.fjprotocol.TransManager;
import com.fj.fjprotocol.util.AppCallBack;
import com.fj.fjprotocol.util.LogCallBack;
import com.fj.towercontrol.event.MessageEvent;
import com.fj.towercontrol.util.DeviceUtil;
import com.fj.towercontrol.util.GsonBitSetAdapter;
import com.fj.towercontrol.util.MemoryStore;
import com.fj.towercontrol.util.ThreadExecutor;
import com.fjd.app.common.motor.MotorTcpController;
import com.fjd.app.common.util.LogUtils;
import com.fjd.app.common.websocket.WebSocketListener;
import com.fjd.app.common.websocket.server.SimpleWebSocketServer;
import com.fjdynamics.app.logger.Logger;
import com.fjdynamics.tractorprotocol.rtk.manager.RtkManager;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.tencent.mmkv.MMKV;

import org.greenrobot.eventbus.EventBus;

import java.nio.charset.StandardCharsets;
import java.util.BitSet;

public class TowerApp extends Application {

	private static final String TAG = "TowerApp";

	/**
	 * 是否是生产环境
	 */
	public static final boolean IS_PROD = TextUtils.equals(BuildConfig.FLAVOR, "prod");

	public static String VEHICLE_SN = "";
//	public static String VEHICLE_SN = "SVEALQ15822A02235ZC";//香港cic场地设备sn
//	public static String VEHICLE_SN = "SVEALQ15822A02146ZC";//香港西沙场地设备sn
//	public static String VEHICLE_SN = "SVEALQ15822A02188ZC";//南京测试设备sn

	@SuppressLint("StaticFieldLeak")
	private static Context context;

	private static SimpleWebSocketServer webSocketServer;

	@Override
	public void onCreate() {
		super.onCreate();
//        GsonUtils.setGsonDelegate(new
//                GsonBuilder().disableHtmlEscaping().serializeNulls().setObjectToNumberStrategy(in -> {
//            double dbNum = in.nextDouble();
//            // TODO: 2023/5/10 gson在转换Object类型的数据时会自动将其中的数字类型全部转成double, 可以在这里手动判断对应的数字类型并返回
//            return null;
//        }).create());
//        GsonUtils.setGsonDelegate(new GsonBuilder().disableHtmlEscaping().create());
		//GsonUtils默认序列化null，iot平台接收null数据会有异常，这里设置下不序列化null
		Gson gson = new GsonBuilder()
			.disableHtmlEscaping()
			.registerTypeAdapter(BitSet.class, new GsonBitSetAdapter())
			.create();
		GsonUtils.setGsonDelegate(gson);

		context = getApplicationContext();
		if (TextUtils.isEmpty(VEHICLE_SN)) {
			VEHICLE_SN = DeviceUtil.getVehicleSn();
		}
		AppCallBack.setAppContext(this);
		LogCallBack.setLogCallBack(
			new LogCallBack.LogUtil() {
				@Override
				public void e(String tag, String msg) {
					Logger.e(tag, msg);
				}

				@Override
				public void d(String tag, String msg) {
					Logger.d(tag, msg);
				}

				@Override
				public void w(String tag, String msg) {
					Logger.w(tag, msg);
				}
			});
		//初始化common库log
		LogUtils.setLogEnabled(true);
		LogUtils.setLogger(new LogUtils.Logger() {
			@Override
			public void v(String tag, String msg) {
				Logger.v(tag, msg);
			}

			@Override
			public void d(String tag, String msg) {
				Logger.d(tag, msg);
			}

			@Override
			public void i(String tag, String msg) {
				Logger.i(tag, msg);
			}

			@Override
			public void w(String tag, String msg) {
				Logger.w(tag, msg);
			}

			@Override
			public void e(String tag, String msg) {
				Logger.e(tag, msg);
			}
		});
		// 初始化mmkv
		initMMKV();
		// 观测量数据保存类初始化
		// BoardCardLogManager.getInstance().initReceiveDataCallBack();
		// 初始化串口传输
		initTransManger();
		// 初始化电台
		//        initRadio();
		// 初始化板卡观测日志记录工具
		// ObserveDataLogUtil.getInstance().init();

		// 开启ws服务
		webSocketServer = new SimpleWebSocketServer(10086, new WebSocketListener() {
			@Override
			public void onConnectStatusChanged(boolean connected, int code) {
			}

			@Override
			public void onMessage(String msg) {
				EventBus.getDefault().post(new MessageEvent(MessageEvent.CODE_CLIENT_WS_MSG_RECEIVED, msg));
			}

			@Override
			public void onMessage(byte[] bytes) {
			}
		});

		MotorTcpController.getInstance().setMotorConfig(new MotorTcpController.MotorConfig() {
			@Override
			public String getMotorIp() {
				return MemoryStore.getInstance().getSystemConfig().getMotorUrl().getHost();
			}

			@Override
			public int getInitialAngle() {
				return 180;
			}

			@Override
			public int getLimitAngle() {
				return MemoryStore.getInstance().getSystemConfig().getMotorLimitAngle();
			}
		});
	}

	private void initMMKV() {
		MMKV.initialize(this);
	}

	private void initTransManger() {
		TransManager.getInstance()
			.init(data -> EventBus.getDefault().post(new MessageEvent(MessageEvent.CODE_ECU_DATA_RECEIVED, data)));
		ControlCenterManager.getInstance();

		new Handler().postDelayed(this::configBoardCard, 8_000);
	}

	public void configBoardCard() {
		ThreadExecutor.getInstance()
			.executor(
				() -> {
					Logger.d(TAG, "configBoardCard start");
					int retryCount = 0;
					// 重试100次，则跳出
					while (retryCount < 100) {
						RtkManager.getInstance().sendData("UNLOG\r\n".getBytes(StandardCharsets.US_ASCII));
						retryCount++;
						try {
							SystemClock.sleep(100);
						} catch (Exception e) {
						}
					}
					// 需要等待6秒左右
//					RtkManager.getInstance().sendData("freset\r\n".getBytes(StandardCharsets.US_ASCII));
//					SystemClock.sleep(12_000);
					RtkManager.getInstance().sendData("Mode rover\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData(("gpgga com1 1\r\n").getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData(("GPRMC COM1 1\r\n").getBytes(StandardCharsets.US_ASCII)); // 配置rmc
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("config com2 230400\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("gpgga com2 0.05\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("GPRMC COM2 0.05\r\n".getBytes(StandardCharsets.US_ASCII)); // 配置rmc
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("gphdt com2 0.05\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("gpgst com2 0.05\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("gpvtg com2 0.05\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData(("gpzda com2 0.05\r\n").getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("unlog bestposa\r\n".getBytes());
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("LOG COM2 BESTPOSA ONTIME 0.05\r\n".getBytes());
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("config com3 115200\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("gpgga com3 0.05\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("gphdt com3 0.05\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("gpgst com3 0.05\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("gpvtg com3 0.05\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("CONFIG DGPS TIMEOUT 1202\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					RtkManager.getInstance().sendData("CONFIG RTK TIMEOUT 1200\r\n".getBytes(StandardCharsets.US_ASCII));
					SystemClock.sleep(10);
					// 配置观测量
					//            RtkManager.getInstance().sendData("log rangecmpb ontime
					// 1\r\n".getBytes(StandardCharsets.US_ASCII));
					//            SystemClock.sleep(10);
					//            RtkManager.getInstance().sendData("log baserangeb
					// onchanged\r\n".getBytes(StandardCharsets.US_ASCII));
					//            SystemClock.sleep(10);
					//            RtkManager.getInstance().sendData("log refstationb ontime
					// 10\r\n".getBytes(StandardCharsets.US_ASCII));
					//            SystemClock.sleep(10);
					//            RtkManager.getInstance().sendData("log bestposb ontime
					// 1\r\n".getBytes(StandardCharsets.US_ASCII));
					//            SystemClock.sleep(10);
					//            RtkManager.getInstance().sendData("log bestvelb ontime
					// 1\r\n".getBytes(StandardCharsets.US_ASCII));
					//            SystemClock.sleep(10);
					//            RtkManager.getInstance().sendData("log rtkdopb ontime
					// 1\r\n".getBytes(StandardCharsets.US_ASCII));
					//            SystemClock.sleep(10);
					//            RtkManager.getInstance().sendData("log matchedposb
					// onchanged\r\n".getBytes(StandardCharsets.US_ASCII));
					//            SystemClock.sleep(10);
					//            RtkManager.getInstance().sendData("log gpsephemb ontime
					// 600\r\n".getBytes(StandardCharsets.US_ASCII));
					//            SystemClock.sleep(10);
					//            RtkManager.getInstance().sendData("log bd2ephemb ontime
					// 600\r\n".getBytes(StandardCharsets.US_ASCII));
					//            SystemClock.sleep(10);
					//            RtkManager.getInstance().sendData("log galephemerisb
					// ontime 600\r\n".getBytes(StandardCharsets.US_ASCII));
					//            SystemClock.sleep(10);
					//            RtkManager.getInstance().sendData("log gloephemerisb
					// ontime 600\r\n".getBytes(StandardCharsets.US_ASCII));
					//            SystemClock.sleep(10);
					RtkManager.getInstance().sendData("SAVECONFIG\r\n".getBytes(StandardCharsets.US_ASCII));
					// 保存配置
					Logger.d(TAG, "configBoardCard end");
					EventBus.getDefault()
						.post(new MessageEvent(MessageEvent.CODE_BOARD_CARD_CONFIG_FINISHED, null));
				});
	}

	/**
	 * 全局获取applicationContext
	 *
	 * @return applicationContext
	 */
	public static Context getContext() {
		return context;
	}

	public static SimpleWebSocketServer getWebSocketServer() {
		return webSocketServer;
	}
}

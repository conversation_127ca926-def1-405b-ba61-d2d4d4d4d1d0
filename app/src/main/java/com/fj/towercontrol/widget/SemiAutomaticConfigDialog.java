package com.fj.towercontrol.widget;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;

import com.blankj.utilcode.util.ToastUtils;
import com.fj.towercontrol.R;
import com.fj.towercontrol.util.CsvUtil;
import com.fj.towercontrol.util.MemoryStore;

/**
 * 半自动吊运配置弹窗
 *
 * <AUTHOR>
 */
public class SemiAutomaticConfigDialog extends DialogFragment {

	public static String TAG = "SemiAutomaticConfigDialog";

	@NonNull
	@Override
	public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
		View view = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_semi_automatic_config, null);
		view.findViewById(R.id.btn_start_collect_path_1).setOnClickListener(v -> {
			//开始采集从A->B轨迹
			int status = MemoryStore.getInstance().getPathCollectStatus();
			if (status == MemoryStore.COLLECT_STATUS_COLLECTING) {
				ToastUtils.showShort("当前正在采集中，请先结束采集");
				return;
			}
			MemoryStore.getInstance().getHistoryPointList().clear();
			MemoryStore.getInstance().setPathCollectStatus(MemoryStore.COLLECT_STATUS_COLLECTING);
			MemoryStore.getInstance().setPathCollectMode(MemoryStore.COLLECT_MODE_A_TO_B);
			ToastUtils.showShort("开始采集A->B轨迹");
		});
		view.findViewById(R.id.btn_start_collect_path_2).setOnClickListener(v -> {
			//开始采集从B->A轨迹
			int status = MemoryStore.getInstance().getPathCollectStatus();
			if (status == MemoryStore.COLLECT_STATUS_COLLECTING) {
				ToastUtils.showShort("当前正在采集中，请先结束采集");
				return;
			}
			MemoryStore.getInstance().getHistoryPointList().clear();
			MemoryStore.getInstance().setPathCollectStatus(MemoryStore.COLLECT_STATUS_COLLECTING);
			MemoryStore.getInstance().setPathCollectMode(MemoryStore.COLLECT_MODE_B_TO_A);
			ToastUtils.showShort("开始采集B->A轨迹");
		});
		view.findViewById(R.id.btn_stop_collect).setOnClickListener(v -> {
			//结束轨迹采集
			int status = MemoryStore.getInstance().getPathCollectStatus();
			if (status == MemoryStore.COLLECT_STATUS_IDLE) {
				ToastUtils.showShort("未开始采集轨迹");
				return;
			}
			int mode = MemoryStore.getInstance().getPathCollectMode();
			String name = (mode == MemoryStore.COLLECT_MODE_A_TO_B ? "A_to_B" : "B_to_A");
			if (CsvUtil.writeCsvFile(name, MemoryStore.getInstance().getHistoryPointList())) {
				ToastUtils.showShort(name + "轨迹保存成功");
			} else {
				ToastUtils.showShort(name + "轨迹保存失败");
			}
			MemoryStore.getInstance().setPathCollectStatus(MemoryStore.COLLECT_STATUS_IDLE);
			MemoryStore.getInstance().getHistoryPointList().clear();
		});

		AlertDialog dialog = new AlertDialog.Builder(requireContext())
			.setTitle("半自动轨迹采集")
			.setView(view)
			.setNegativeButton("关闭", null)
			.create();

		// 设置隐藏导航栏
		dialog.getWindow()
			.setFlags(
				WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
				WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
		dialog.show();
		dialog.getWindow()
			.getDecorView()
			.setSystemUiVisibility(
				View.SYSTEM_UI_FLAG_LAYOUT_STABLE
					| View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
					| View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
					| View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
					| View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
					| View.SYSTEM_UI_FLAG_FULLSCREEN);
		dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);

		return dialog;
	}

	@Override
	public void show(@NonNull FragmentManager manager, @Nullable String tag) {
		setCancelable(false);
		super.show(manager, tag);
	}
}

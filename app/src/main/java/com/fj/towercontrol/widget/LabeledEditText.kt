package com.fj.towercontrol.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.fj.towercontrol.R
import com.fj.towercontrol.databinding.LayoutLabeledEdittextBinding

/**
 * 带标签文本的输入框
 *
 * <AUTHOR>
 * @since 2024/5/15
 */
class LabeledEditText @JvmOverloads constructor(
	context: Context,
	attrs: AttributeSet? = null,
	defStyleAttr: Int = 0,
) : LinearLayout(context, attrs, defStyleAttr) {

	private val binding =
		LayoutLabeledEdittextBinding.inflate(LayoutInflater.from(context), this, true)

	init {
		attrs?.let {
			val typedArray = context.obtainStyledAttributes(attrs, R.styleable.LabeledEditText)
			setLabelText(typedArray.getString(R.styleable.LabeledEditText_labelText) ?: "")
			typedArray.recycle()
		}
	}

	fun setLabelText(text: String) {
		binding.tvLabel.text = text
	}

	fun setEditText(text: String) {
		binding.layoutTextInput.editText?.apply {
			setText(text)
			setSelection(text.length)
		}
	}

	fun getEditText(): String {
		return binding.layoutTextInput.editText?.text?.toString() ?: ""
	}

	fun getEditTextIfMatchOrNull(pattern: String): String? {
		val text = getEditText()
		return if (Regex(pattern).matches(text)) text else null
	}
}

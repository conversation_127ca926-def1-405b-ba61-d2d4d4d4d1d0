package com.fj.towercontrol.widget;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;

import com.blankj.utilcode.util.ToastUtils;
import com.fj.towercontrol.R;
import com.fj.towercontrol.util.FileStore;
import com.fjdynamics.app.logger.Logger;

/**
 * 半自动安全吊运高度配置
 *
 * <AUTHOR>
 */
public class LiftHeightConfigDialog extends DialogFragment {

	public static String TAG = "LiftHeightConfigDialog";

	@NonNull
	@Override
	public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
		View view = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_lift_height_config, null);
		EditText etUp = view.findViewById(R.id.et_up);
		EditText etDown = view.findViewById(R.id.et_down);
		etUp.setText(String.valueOf(FileStore.getLiftUpHeightConfig()));
		etDown.setText(String.valueOf(FileStore.getLiftDownHeightConfig()));
		AlertDialog dialog = new AlertDialog.Builder(requireContext())
			.setTitle("自动吊运安全高度配置")
			.setView(view)
			.setNegativeButton("取消", null)
			.setPositiveButton("确定", (dialogInterface, which) -> {
				try {
					String upText = etUp.getText().toString().trim();
					String downText = etDown.getText().toString().trim();
					if (TextUtils.isEmpty(upText) || TextUtils.isEmpty(downText)) {
						ToastUtils.showShort("配置不能为空！");
						return;
					}
					double up = Double.parseDouble(upText);
					double down = Double.parseDouble(downText);
					if (up < 0 || down < 0) {
						ToastUtils.showShort("输入异常");
						return;
					}
					FileStore.setLiftUpHeightConfig(up);
					FileStore.setLiftDownHeightConfig(down);
//					TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setSemiAutoLiftingHeight(up, down));

					dialogInterface.dismiss();
				} catch (Exception e) {
					Logger.e(TAG, "config exception: " + e.getMessage());
					dialogInterface.dismiss();
				}
			})
			.create();

		// 设置隐藏导航栏
		dialog.getWindow()
			.setFlags(
				WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
				WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
		dialog.show();
		dialog.getWindow()
			.getDecorView()
			.setSystemUiVisibility(
				View.SYSTEM_UI_FLAG_LAYOUT_STABLE
					| View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
					| View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
					| View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
					| View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
					| View.SYSTEM_UI_FLAG_FULLSCREEN);
		dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);

		return dialog;
	}

	@Override
	public void show(@NonNull FragmentManager manager, @Nullable String tag) {
		setCancelable(false);
		super.show(manager, tag);
	}
}

package com.fj.towercontrol.widget.dalog

import android.text.TextUtils
import androidx.fragment.app.FragmentManager
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.ToastUtils
import com.fj.fjprotocol.ProtocolHelper
import com.fj.fjprotocol.TransManager
import com.fj.towercontrol.R
import com.fj.towercontrol.consts.CraneType
import com.fj.towercontrol.data.entity.Position
import com.fj.towercontrol.data.entity.SystemConfig
import com.fj.towercontrol.databinding.DialogSystemConfigBinding
import com.fj.towercontrol.modbus.HookModbusMaster
import com.fj.towercontrol.modbus.VoiceModbusMaster
import com.fj.towercontrol.tcp.YongMaoDataRetriever
import com.fj.towercontrol.ui.base.FullScreenDialogFragment
import com.fj.towercontrol.util.FileStore
import com.fj.towercontrol.websocket.client.LargeScreenWebSocketManager
import com.fjdynamics.app.logger.Logger
import com.fjdynamics.app.logger.LoggerManager
import java.net.URI

/**
 * 用于显示和修改所有系统配置的全屏弹窗
 *
 * <AUTHOR>
 * @since 2024/5/14
 */
class SystemConfigDialogFragment(private val callback: SystemConfigCallback) :
	FullScreenDialogFragment<DialogSystemConfigBinding>() {

	override fun initOnce() {
		Logger.d(TAG, "initOnce")
		binding.flBack.setOnClickListener { dismiss() }

		val systemConfig = (FileStore.getSystemConfig() ?: SystemConfig()).apply {
			when (craneType) {
				CraneType.YONGMAO.ordinal -> {
					binding.rgCraneType.check(R.id.rbYongMao)
				}

				CraneType.SANY.ordinal -> {
					binding.rgCraneType.check(R.id.rbSany)
				}

				CraneType.XCMG.ordinal -> {
					binding.rgCraneType.check(R.id.rbXcmg)
				}

				CraneType.YONGMAO_STT153.ordinal -> {
					binding.rgCraneType.check(R.id.rbYongMaoSTT153)
				}
			}

			binding.etLargeScreenUrl.setEditText(largeScreenUrl.host)
			binding.etHook.setEditText(hookUrl.host)
			binding.swHookUsr.isChecked = hookIsUsr
			binding.etYongMao485Url.setEditText(yongMao485Url.host)
			binding.etKeyIoUrl.setEditText(keyIoUrl.host)
			binding.etAlarmIoUrl.setEditText(alarmIoUrl.host)
			binding.etVoiceAlarmUrl.setEditText(voiceAlarmUrl.host)
			binding.swVoiceUsr.isChecked = voiceIsUsr
			binding.etWeatherStationUrl.setEditText(weatherStationUrl.host)
			binding.etMotorUrl.setEditText(motorUrl.host)
			binding.etMotorLimit.setEditText(motorLimitAngle.toString())
			binding.etCarCraneUrl.setEditText(carCraneUrl.host)
			binding.etCarCraneWorkRadius.setEditText(carCraneWorkRadius.toString())
			binding.etCarCraneWarnDistance.setEditText(carCraneWarnDistance.toString())
			binding.etCarCraneCriticalDistance.setEditText(carCraneCriticalDistance.toString())
			if (carCranePosition != null) {
				binding.etCarCraneLatitude.setEditText(carCranePosition.latitude.toString())
				binding.etCarCraneLongitude.setEditText(carCranePosition.longitude.toString())
			} else {
				binding.etCarCraneLatitude.setEditText("")
				binding.etCarCraneLongitude.setEditText("")
			}
		}

		binding.btnFlushLog.setOnClickListener {
			LoggerManager.flush()
			ToastUtils.showShort("日志已刷新")
		}
		binding.btnRestart.setOnClickListener { AppUtils.relaunchApp(true) }
		binding.btnNtrip.setOnClickListener { callback.onNtripConfig() }
		binding.btnUpgrade.setOnClickListener {
			callback.onCheckUpdate()
			dismiss()
		}
		binding.btnRegister.setOnClickListener {
			callback.onDeviceRegister()
			dismiss()
		}
		binding.btnDebug.setOnClickListener {
			when (binding.rgCraneType.checkedRadioButtonId) {
				R.id.rbYongMao -> {
					ToastUtils.showShort("暂不支持永茂塔吊调试")
					return@setOnClickListener
				}

				R.id.rbSany -> callback.onDebug(CraneType.SANY)
				R.id.rbXcmg -> callback.onDebug(CraneType.XCMG)
				R.id.rbYongMaoSTT153 -> callback.onDebug(CraneType.YONGMAO_STT153)
			}
			dismiss()
		}

		binding.btnSave.setOnClickListener {
			saveConfig(systemConfig)
		}
	}

	private fun saveConfig(systemConfig: SystemConfig): Boolean {
		//检查输入是否正确
		val craneType = binding.rgCraneType.checkedRadioButtonId.let {
			when (it) {
				R.id.rbYongMao -> CraneType.YONGMAO
				R.id.rbSany -> CraneType.SANY
				R.id.rbXcmg -> CraneType.XCMG
				R.id.rbYongMaoSTT153 -> CraneType.YONGMAO_STT153
				else -> CraneType.YONGMAO
			}
		}
		val largeScreenIp =
			binding.etLargeScreenUrl.getEditTextIfMatchOrNull(IP_REGEX)
		if (TextUtils.isEmpty(largeScreenIp)) {
			ToastUtils.showShort("大屏IP输入格式错误")
			return false
		}

//		val aiLiftIp = binding.etAiLiftUrl.getEditTextIfMatchOrNull(IP_REGEX)
//		if (TextUtils.isEmpty(aiLiftIp)) {
//			ToastUtils.showShort("AI-LIFT IP输入格式错误")
//			return false
//		}

		val hookIp = binding.etHook.getEditTextIfMatchOrNull(IP_REGEX)
		if (TextUtils.isEmpty(hookIp)) {
			ToastUtils.showShort("智能吊钩称重IP输入格式错误")
			return false
		}

		val yongMao485Ip = binding.etYongMao485Url.getEditTextIfMatchOrNull(IP_REGEX)
		if (TextUtils.isEmpty(yongMao485Ip)) {
			ToastUtils.showShort("永茂塔机IP输入格式错误")
			return false
		}

		val keyIoIp = binding.etKeyIoUrl.getEditTextIfMatchOrNull(IP_REGEX)
		if (TextUtils.isEmpty(keyIoIp)) {
			ToastUtils.showShort("塔上钥匙开关IP输入格式错误")
			return false
		}
		val alarmIoIp = binding.etAlarmIoUrl.getEditTextIfMatchOrNull(IP_REGEX)
		if (TextUtils.isEmpty(alarmIoIp)) {
			ToastUtils.showShort("灯光告警IP输入格式错误")
			return false
		}
		val voiceAlarmIp = binding.etVoiceAlarmUrl.getEditTextIfMatchOrNull(IP_REGEX)
		if (TextUtils.isEmpty(voiceAlarmIp)) {
			ToastUtils.showShort("声音告警IP输入格式错误")
			return false
		}
		val weatherStationIp = binding.etWeatherStationUrl.getEditTextIfMatchOrNull(IP_REGEX)
		if (TextUtils.isEmpty(weatherStationIp)) {
			ToastUtils.showShort("气象站IP输入格式错误")
			return false
		}
		val motorIp = binding.etMotorUrl.getEditTextIfMatchOrNull(IP_REGEX)
		if (TextUtils.isEmpty(motorIp)) {
			ToastUtils.showShort("舵机IP输入格式错误")
			return false
		}
		val motorLimitAngle = binding.etMotorLimit.getEditText().toIntOrNull()
		if (motorLimitAngle == null || motorLimitAngle < 0 || motorLimitAngle > 20) {
			ToastUtils.showShort("舵机限制角度必须在0到20范围内")
			return false
		}

		val carCraneUrl = binding.etCarCraneUrl.getEditTextIfMatchOrNull(IP_REGEX)
		if (TextUtils.isEmpty(carCraneUrl)) {
			ToastUtils.showShort("汽车吊告警IP输入格式错误")
			return false
		}

		val carCraneWorkRadius = binding.etCarCraneWorkRadius.getEditText().toDoubleOrNull()
		if (carCraneWorkRadius == null || carCraneWorkRadius <= 0) {
			ToastUtils.showShort("工作半径必须大于0")
			return false
		}

		val carCraneWarnDistance = binding.etCarCraneWarnDistance.getEditText().toDoubleOrNull()
		if (carCraneWarnDistance == null || carCraneWarnDistance <= 0) {
			ToastUtils.showShort("橙色告警距离必须大于0")
			return false
		}

		val carCraneCriticalDistance =
			binding.etCarCraneCriticalDistance.getEditText().toDoubleOrNull()
		if (carCraneCriticalDistance == null || carCraneCriticalDistance >= carCraneWarnDistance) {
			ToastUtils.showShort("红色告警距离必须小于橙色告警距离")
			return false
		}

		var carCraneLatitude: Double? = null
		var carCraneLongitude: Double? = null
		//允许不输入汽车吊旋转中心经纬度，只要输入了就要校验格式
		if (binding.etCarCraneLatitude.getEditText()
				.isNotEmpty() || binding.etCarCraneLongitude.getEditText().isNotEmpty()
		) {
			carCraneLatitude = binding.etCarCraneLatitude.getEditText().toDoubleOrNull()
			carCraneLongitude = binding.etCarCraneLongitude.getEditText().toDoubleOrNull()
			if (carCraneLatitude == null || carCraneLongitude == null) {
				ToastUtils.showShort("汽车吊经纬度输入异常")
				return false
			}
		}

		val newSystemConfig = SystemConfig(
			craneType = craneType.ordinal,
			largeScreenUrl = URI.create("ws://$largeScreenIp:8070"),
//			aiLiftUrl = URI.create("ws://$aiLiftIp:8070"),
			hookUrl = URI.create("ws://$hookIp:51001"),
			hookIsUsr = binding.swHookUsr.isChecked,
			//				yongMaoCraneUrl = URI.create("tcp://$yongMaoIp:51001"),
			yongMao485Url = URI.create("tcp://$yongMao485Ip:51001"),
			keyIoUrl = URI.create("tcp://$keyIoIp:51001"),
			alarmIoUrl = URI.create("tcp://$alarmIoIp:51001"),
			voiceAlarmUrl = URI.create("tcp://$voiceAlarmIp:51001"),
			voiceIsUsr = binding.swVoiceUsr.isChecked,
			weatherStationUrl = URI.create("tcp://$weatherStationIp:51001"),
			motorUrl = URI.create("tcp://$motorIp:51001"),
			motorLimitAngle = motorLimitAngle,
			carCraneUrl = URI.create("tcp://$carCraneUrl:51001"),
			carCraneWorkRadius = carCraneWorkRadius,
			carCraneWarnDistance = carCraneWarnDistance,
			carCraneCriticalDistance = carCraneCriticalDistance,
			carCranePosition = if (carCraneLatitude != null && carCraneLongitude != null) {
				Position(
					latitude = carCraneLatitude,
					longitude = carCraneLongitude
				)
			} else {
				null
			}
		)

		//保存新配置
		FileStore.setSystemConfig(newSystemConfig)

		//检查新老配置是否发生变化
		if (newSystemConfig.craneType != systemConfig.craneType) {
			TransManager.getInstance()
				.sendEcuDataWithUart1(ProtocolHelper.configTowerType(newSystemConfig.craneType))
		}
		if (newSystemConfig.largeScreenUrl.host != systemConfig.largeScreenUrl.host) {
			//只需要断开当前ws连接，内部会自动尝试重连
			LargeScreenWebSocketManager.getInstance().disconnect()
		}
//		if (newSystemConfig.aiLiftUrl.host != systemConfig.aiLiftUrl.host) {
//			AiLiftWebSocketManager.getInstance().disconnect()
//		}
		if (newSystemConfig.hookUrl.host != systemConfig.hookUrl.host
			|| newSystemConfig.hookIsUsr != systemConfig.hookIsUsr
		) {
			HookModbusMaster.getInstance().stop()
			HookModbusMaster.getInstance().start()
		}
		//			if (newSystemConfig.yongMaoCraneUrl.host != systemConfig.yongMaoCraneUrl.host) {
		//				// TODO: 暂时不使用永茂塔机
		////				TowerModbusReader.getInstance().stop()
		////				TowerModbusReader.getInstance().start()
		//			}
		if (CraneType.fromOrdinal(newSystemConfig.craneType) == CraneType.YONGMAO_STT153) {
			YongMaoDataRetriever.disconnect()
			YongMaoDataRetriever.connect()
		}
		if (newSystemConfig.keyIoUrl.host != systemConfig.keyIoUrl.host) {
//			TowerIOModbusReader.getInstance().stop()
//			TowerIOModbusReader.getInstance().start()
		}
		if (newSystemConfig.alarmIoUrl.host != systemConfig.alarmIoUrl.host) {
//			AlarmIOModbusReader.getInstance().stop()
//			AlarmIOModbusReader.getInstance().start()
		}
		if (newSystemConfig.voiceAlarmUrl.host != systemConfig.voiceAlarmUrl.host
			|| newSystemConfig.voiceIsUsr != systemConfig.voiceIsUsr
		) {
			VoiceModbusMaster.getInstance().stop()
			VoiceModbusMaster.getInstance().start()
		}
		if (newSystemConfig.weatherStationUrl.host != systemConfig.weatherStationUrl.host) {
//			WeatherModbusReader.getInstance().stop()
//			WeatherModbusReader.getInstance().start()
		}
		if (newSystemConfig.motorUrl.host != systemConfig.motorUrl.host
			|| newSystemConfig.motorLimitAngle != systemConfig.motorLimitAngle
		) {
			//只需要断开当前连接，内部会自动尝试重连
//			MotorTcpController.getInstance().disconnect()
		}
		if (newSystemConfig.carCraneUrl.host != systemConfig.carCraneUrl.host) {
//			CarCraneModbusReader.getInstance().stop()
//			CarCraneModbusReader.getInstance().start()
		}

		ToastUtils.showShort("配置保存成功")
		return true
	}

	companion object {
		const val TAG = "SystemConfigDialogFragment"
		const val IP_REGEX =
			"^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])$"


		fun show(fragmentManager: FragmentManager, callback: SystemConfigCallback) {
			SystemConfigDialogFragment(callback).show(fragmentManager, null)
		}

	}
}

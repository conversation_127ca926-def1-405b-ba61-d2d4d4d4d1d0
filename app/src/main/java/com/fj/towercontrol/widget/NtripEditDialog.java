package com.fj.towercontrol.widget;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ListPopupWindow;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.ToastUtils;
import com.fj.fjprotocol.ntrip.NtripManager;
import com.fj.fjprotocol.ntrip.bean.AddressInfo;
import com.fj.fjprotocol.ntrip.bean.NtripSource;
import com.fj.fjprotocol.ntrip.bean.UserInfo;
import com.fj.towercontrol.R;
import com.fj.towercontrol.TowerApp;
import com.fj.towercontrol.data.entity.NtripInfo;
import com.fj.towercontrol.util.FileStore;
import com.fj.towercontrol.util.MultiLanguageManager;

import java.util.ArrayList;
import java.util.List;

/**
 * jarven Ntrip编辑界面
 */
public class NtripEditDialog extends Dialog {

	public static final String NTRIP_DISABLE = "NTRIP_DISABLE";
	public static final String NTRIP_SOURCE_NODE = "NTRIP_SOURCE_NODE";
	private SimpleShapeButton getSourceBtn;
	private TextView confirmTV;
	private TextView cancelTV;
	private TextView ntripStateTV;
	private EditText hostET;
	private EditText portET;
	private EditText accountET;
	private EditText pwdET;
	private EditText etSourcePoint;
	private ImageView ivSpinner;
	private ListPopupWindow listPopupWindow;

	//	private String sourcePoint = "";
	private ArrayAdapter<String> spinnerAdapter;
	private ArrayList<String> mItems = new ArrayList<>();
	//	private Spinner sourceListSP;
	private Handler handler;
	private NtripManager.NtripListener ntripListener = new NtripManager.NtripListener() {
		@Override
		public void onGetSource(List<NtripSource> ntripSources) {
			mItems.clear();
			for (NtripSource ntripSource : ntripSources) {
				mItems.add(ntripSource.strMountpoint);
			}
			if (!mItems.isEmpty()) {
				boolean findTarget = false;

				for (int i = 0; i < mItems.size(); i++) {
					String point = mItems.get(i);
					if ("32MSM4NH".equalsIgnoreCase(point)) {
						mItems.remove(point);
						findTarget = true;
						break;
					}
				}

				if (findTarget) {
					mItems.add(0, "32MSM4NH");
				}
//				sourcePoint = mItems.get(0);
			}
			handler.post(() -> {
				spinnerAdapter.notifyDataSetChanged();
				String currInput = etSourcePoint.getText().toString().trim();
				if (TextUtils.isEmpty(currInput) && !mItems.isEmpty()) {
					etSourcePoint.setText(mItems.get(0));
				}
//				sourceListSP.setAdapter(spinnerAdapter);
			});
		}

		@Override
		public void onStateChange(final NtripManager.NtripState ntripState) {
			handler.post(() -> {
				ntripStateTV.setVisibility(View.VISIBLE);
				if (NtripManager.getInstance().getNtripConnectState()
					== NtripManager.NtripState.LINK_TO_NODE_SUCCESS) {
					confirmTV.setText(R.string.ntrip_disconnect);
					confirmTV.setTextColor(Color.RED);
				} else {
					confirmTV.setText(R.string.ntrip_connect);
					confirmTV.setTextColor(
						TowerApp.getContext()
							.getResources()
							.getColor(R.color.color_EBB400));
				}
				switch (ntripState) {
					case GETING_SOURCE:
						ntripStateTV.setText(R.string.ntrip_get_source);
						ntripStateTV.setTextColor(
							TowerApp.getContext()
								.getResources()
								.getColor(R.color.color_EBB400));
						break;
					case GET_SOURCE_SUCCESS:
						ntripStateTV.setText(R.string.ntrip_get_source_success);
						ntripStateTV.setTextColor(
							TowerApp.getContext()
								.getResources()
								.getColor(R.color.color_EBB400));
						break;
					case GET_SOURCE_FAILED:
						ntripStateTV.setText(R.string.ntrip_get_source_failed);
						ntripStateTV.setTextColor(Color.RED);
						break;
					case LINKING_TO_NODE:
						ntripStateTV.setText(R.string.ntrip_link_to_node);
						ntripStateTV.setTextColor(
							TowerApp.getContext()
								.getResources()
								.getColor(R.color.color_EBB400));
						break;
					case LINK_TO_NODE_SUCCESS:
						ntripStateTV.setText(
							R.string.ntrip_link_to_node_success);
						ntripStateTV.setTextColor(
							TowerApp.getContext()
								.getResources()
								.getColor(R.color.color_EBB400));
						handler.postDelayed(
							new Runnable() {
								@Override
								public void run() {
									dismiss();
								}
							},
							500);
						break;
					case LINK_TO_NODE_FAILED:
						ntripStateTV.setText(
							R.string.ntrip_link_to_node_failed);
						ntripStateTV.setTextColor(Color.RED);
						break;
					default:
						break;
				}
			});
		}
	};

	public NtripEditDialog(@NonNull Context context) {
		super(context, R.style.DialogTheme);
	}

	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(R.layout.dialog_edit_ntrip);
		etSourcePoint = findViewById(R.id.et_source_point);
		handler = new Handler(getContext().getMainLooper());

		spinnerAdapter = new ArrayAdapter<>(getContext(), android.R.layout.simple_spinner_item, mItems);
//		spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
//		sourceListSP.setOnItemSelectedListener(
//			new AdapterView.OnItemSelectedListener() {
//				@Override
//				public void onItemSelected(
//					AdapterView<?> parent, View view, int position, long id) {
//					sourcePoint = mItems.get(position);
//				}
//
//				@Override
//				public void onNothingSelected(AdapterView<?> parent) {
//				}
//			});
//		sourceListSP.setAdapter(spinnerAdapter);
		getSourceBtn = findViewById(R.id.btn_get_source);
		getSourceBtn.setOnClickListener(v -> {
			// 先关闭，再拿节点
			disconnect();
			getSource();
		});
		confirmTV = findViewById(R.id.btn_dialog_confirm);
		confirmTV.setOnClickListener(v -> {
			// 先关闭，再连接
			disconnect();
			connect();
		});
		if (!MultiLanguageManager.isChinese()
			&& NtripManager.getInstance().getNtripConnectState()
			== NtripManager.NtripState.LINK_TO_NODE_SUCCESS) {
			confirmTV.setText(R.string.ntrip_disconnect);
			confirmTV.setTextColor(Color.RED);
		}
		confirmTV.setText(R.string.ntrip_connect);
		confirmTV.setTextColor(TowerApp.getContext().getResources().getColor(R.color.color_EBB400));
		cancelTV = findViewById(R.id.btn_dialog_cancel);
		cancelTV.setOnClickListener(v -> dismiss());
		hostET = findViewById(R.id.et_ntrip_host);
		portET = findViewById(R.id.et_ntrip_port);
		accountET = findViewById(R.id.et_ntrip_account);
		pwdET = findViewById(R.id.et_ntrip_password);
		ntripStateTV = findViewById(R.id.tv_ntrip_state);

		NtripInfo ntripCustomize = FileStore.getNtripCustomize();
		if (ntripCustomize != null) {
			AddressInfo addressInfo = ntripCustomize.getAddressInfo();
			UserInfo userInfo = ntripCustomize.getUserInfo();
			if (addressInfo != null) {
				hostET.setText(addressInfo.getIp());
				portET.setText(addressInfo.getPort() + "");
			}
			if (userInfo != null) {
				accountET.setText(userInfo.getUsername());
				pwdET.setText(userInfo.getPassword());
			}
			if (!TextUtils.isEmpty(ntripCustomize.getSourcePoint())) {
				mItems.clear();
				mItems.add(ntripCustomize.getSourcePoint());
//				sourcePoint = ntripCustomize.getSourcePoint();
				etSourcePoint.setText(ntripCustomize.getSourcePoint());
				spinnerAdapter.notifyDataSetChanged();
			}
		}

		ivSpinner = findViewById(R.id.iv_spinner);
		ivSpinner.setOnClickListener(v -> {
			if (listPopupWindow != null && listPopupWindow.isShowing()) {
				//如果点击图标时，列表弹窗正在显示，则关闭列表弹窗
				listPopupWindow.dismiss();
				listPopupWindow = null;
				return;
			}
			listPopupWindow = new ListPopupWindow(getContext());
			listPopupWindow.setAdapter(spinnerAdapter);
			listPopupWindow.setAnchorView(etSourcePoint);
			listPopupWindow.setWidth(ViewGroup.LayoutParams.WRAP_CONTENT);
			listPopupWindow.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
			listPopupWindow.setOnItemClickListener((parent, view, position, id) -> {
				etSourcePoint.setText(mItems.get(position));
				if (listPopupWindow != null) {
					listPopupWindow.dismiss();
				}
			});
			listPopupWindow.show();
		});
	}

	@Override
	public void show() {
		NtripManager.getInstance().addNtripListener(ntripListener);
		NtripManager.getInstance().setAutoLink(false);
		NtripManager.getInstance().setNeedRestart(false);

		Window window = getWindow();
		if (window == null) {
			super.show();
			return;
		}

		// 弹窗隐藏导航栏
		window.setFlags(
			WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
			WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
		super.show();
		int uiOptions =
			View.SYSTEM_UI_FLAG_LAYOUT_STABLE
				| View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
				| View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
				| View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
				| View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
				| View.SYSTEM_UI_FLAG_FULLSCREEN;
		window.getDecorView().setSystemUiVisibility(uiOptions);
		window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
	}

	@Override
	public void dismiss() {
		try {
			NtripManager.getInstance().removeNtripListener(ntripListener);
			NtripManager.getInstance().setAutoLink(true);
			NtripManager.getInstance().setNeedRestart(true);
			super.dismiss();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void getSource() {
		try {
			String host = hostET.getText().toString().trim();
			String port = portET.getText().toString().trim();
			if (host.isEmpty() || port.isEmpty()) {
				Toast.makeText(getContext(), R.string.ntrip_host_port_empty, Toast.LENGTH_LONG)
					.show();
				return;
			}
			int iPort = Integer.parseInt(port);
			AddressInfo addressInfo = new AddressInfo(host, iPort);
			NtripManager.getInstance().setAddressInfo(addressInfo);
			NtripInfo ntripInfo = FileStore.getNtripCustomize();
			if (ntripInfo == null) {
				ntripInfo = new NtripInfo();
			}
			ntripInfo.setAddressInfo(addressInfo);
			//            int netChannelType = CacheUtil.getNetChannelType();
			//            if (netChannelType == SharedPreferenceUtil.CHANNEL_TYPE_CORS) {
			//                SharedPreferenceUtil.getInstance().saveNtripCors(ntripInfo);
			//            } else if (netChannelType == SharedPreferenceUtil.CHANNEL_TYPE_CUSTOMIZE)
			// {
			FileStore.saveNtripCustomize(ntripInfo);
			//            }
			NtripManager.getInstance().getSource();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void disconnect() {
		NtripManager.getInstance().stop();
		FileStore.setNtrpDisabled(true);
	}

	private void connect() {
		String sourcePoint = etSourcePoint.getText().toString().trim();
		if (sourcePoint.isEmpty()) {
			ToastUtils.showShort(R.string.source_code_empty);
			//            Toast.makeText(getContext(), "Source node is empty!",
			// Toast.LENGTH_LONG).show();
		} else {
			String host = hostET.getText().toString().trim();
			String port = portET.getText().toString().trim();
			if (TextUtils.isEmpty(host) || TextUtils.isEmpty(port)) {
				ToastUtils.showShort(R.string.ntrip_host_port_empty);
				return;
			}
			int portNum;
			try {
				portNum = Integer.parseInt(port);
			} catch (Exception e) {
				ToastUtils.showShort("端口号输入异常");
				return;
			}
			AddressInfo addressInfo = new AddressInfo(host, portNum);
			NtripManager.getInstance().setAddressInfo(addressInfo);

			String account = accountET.getText().toString();
			String pwd = pwdET.getText().toString();
			if (TextUtils.isEmpty(account.trim())) {
				ToastUtils.showShort(R.string.enter_login_account);
				return;
			}
			if (TextUtils.isEmpty(pwd.trim())) {
				ToastUtils.showShort(R.string.input_pwd);
				return;
			}
			UserInfo userInfo = new UserInfo(account, pwd);
			NtripManager.getInstance().setUserInfo(userInfo);
			//            int netChannelType =
			// SharedPreferenceUtil.getInstance().getNetChannelType();
			NtripInfo info = new NtripInfo();
			info.setAddressInfo(addressInfo);
			info.setUserInfo(userInfo);
			info.setSourcePoint(sourcePoint);
			//            if (netChannelType == SharedPreferenceUtil.CHANNEL_TYPE_CORS) {
			//                SharedPreferenceUtil.getInstance().saveNtripCors(info);
			//            } else if (netChannelType == SharedPreferenceUtil.CHANNEL_TYPE_CUSTOMIZE)
			// {
			FileStore.saveNtripCustomize(info);
			//            }
			NtripManager.getInstance().setSourcePoint(sourcePoint);
			NtripManager.getInstance().linkSource();
			FileStore.setNtrpDisabled(false);
		}
	}
}

package com.fj.towercontrol.event;

public class MessageEvent {

	/**
	 * 接收到ecu数据
	 */
	public static final String CODE_ECU_DATA_RECEIVED = "key_ecu_data_received";
	/**
	 * 接收到iot平台下发的消息
	 */
	public static final String CODE_IOT_MESSAGE_RECEIVED = "key_iot_msg_received";
	/**
	 * 接收到司机中控发送的消息
	 */
	public static final String CODE_CLIENT_WS_MSG_RECEIVED = "key_pad_msg_received";
	/**
	 * 板卡配置结束
	 */
	public static final String CODE_BOARD_CARD_CONFIG_FINISHED = "key_board_config_finished";
	/**
	 * 雷达检测到最近障碍物更新事件
	 */
	public static final String CODE_RADAR_EVENT = "key_radar_event";
	/**
	 * 读取到吊钩称重数据事件
	 */
	public static final String CODE_CARGO_WEIGHT_EVENT = "key_cargo_weight_event";
	/**
	 * 后台http接口地址修改事件
	 */
	public static final String SERVER_URL_UPDATED = "key_server_url_updated";
	/**
	 * 大屏ws状态更新事件
	 */
	public static final String SERVER_WS_STATUS_UPDATED = "key_server_ws_status_updated";
	/**
	 * ai-lift ws状态更新
	 */
	public static final String AI_LIFT_WS_STATUS_UPDATED = "key_ai_lift_ws_status_updated";
	/**
	 * 吊钩电池数据更新
	 */
	public static final String BATTERY_INFO_UPDATED = "key_battery_info_updated";
	/**
	 * 地磁仪数据更新
	 */
	public static final String MAGNETOMETER_UPDATE = "key_magnetometer_update";
	/**
	 * 塔机数据更新
	 */
	public static final String CODE_CRANE_DATA_UPDATE = "CODE_CRANE_DATA_UPDATE";
	/**
	 * 接收到大屏ws消息
	 */
	public static final String SERVER_WS_MESSAGE = "SERVER_WS_MESSAGE";
	/**
	 * 接收到AI-LIFT ws消息
	 */
	public static final String AI_LIFT_WS_MESSAGE = "AI_LIFT_WS_MESSAGE";
	/**
	 * 接收到安全帽数据
	 */
	public static final String CODE_HELMET_DATA_UPDATE = "CODE_HELMET_DATA_UPDATE";
	/**
	 * 激光雷达探测障碍物数据更新
	 */
	public static final String CODE_LIDAR_DETECTION_UPDATE = "CODE_LIDAR_DETECTION_UPDATE";
	/**
	 * 激光雷达和GNSS数据更新
	 */
	public static final String CODE_LIDAR_NMEA_DATA_UPDATE = "CODE_LIDAR_NMEA_DATA_UPDATE";

	private final String code;
	private final Object data;

	public MessageEvent(String code) {
		this(code, null);
	}

	public MessageEvent(String code, Object data) {
		this.code = code;
		this.data = data;
	}

	public String getCode() {
		return code;
	}

	public Object getData() {
		return data;
	}
}

<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:fadeScrollbars="false"
	android:scrollbars="vertical"
	tools:showIn="@layout/layout_main">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:layout_width="match_parent"
		android:layout_height="match_parent">

		<TextView
			android:id="@+id/tv_battery_info"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="6dp"
			android:layout_marginEnd="6dp"
			android:textColor="@color/black"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:text="吊钩电池:87.3%,状态:充电中" />

		<TextView
			android:id="@+id/tvEcuRealtimeTitle"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginStart="6dp"
			android:layout_marginTop="8dp"
			android:text="ecu实时状态上报"
			android:textColor="#000000"
			android:textSize="18sp"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<TextView
			android:id="@+id/tvEcuRealtime"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:textColor="#000000"
			android:textSize="12sp"
			app:layout_constraintStart_toStartOf="@+id/tvEcuRealtimeTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvEcuRealtimeTitle" />

		<TextView
			android:id="@+id/tvEcuLogTitle"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="8dp"
			android:text="ecu日志上报"
			android:textColor="#000000"
			android:textSize="18sp"
			app:layout_constraintStart_toStartOf="@+id/tvEcuRealtimeTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvEcuRealtime" />

		<TextView
			android:id="@+id/tvEcuLog"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:textColor="#000000"
			android:textSize="12sp"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvEcuLogTitle" />

		<TextView
			android:id="@+id/tvCargoData"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/dimen_8dp"
			android:text="智能吊钩称重："
			android:textColor="#000000"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvEcuLog" />

		<TextView
			android:id="@+id/tvRadar"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/dimen_8dp"
			android:text="四周最近目标："
			android:textColor="#000000"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvCargoData" />

		<TextView
			android:id="@+id/tvRadarBottom"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/dimen_8dp"
			android:text="底部雷达："
			android:textColor="#000000"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvRadar" />

		<!--		<TextView-->
		<!--			android:id="@+id/tvTower"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:layout_marginTop="@dimen/dimen_8dp"-->
		<!--			android:text="塔机："-->
		<!--			android:textColor="#000000"-->
		<!--			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"-->
		<!--			app:layout_constraintTop_toBottomOf="@+id/tvRadarBottom" />-->

		<TextView
			android:id="@+id/tv_radar_debug"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginVertical="8dp"
			android:text="毫米波雷达："
			android:textColor="#000000"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvRadarBottom" />

		<TextView
			android:id="@+id/tv_lidar"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginVertical="8dp"
			android:text="激光雷达："
			android:textColor="#000000"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tv_radar_debug" />

		<Button
			android:id="@+id/btn_play_voice_1"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="播放1"
			android:visibility="visible"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tv_lidar"
			tools:visibility="visible" />

		<Button
			android:id="@+id/btn_play_voice_2"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginStart="4dp"
			android:text="播放2"
			android:visibility="visible"
			app:layout_constraintBottom_toBottomOf="@+id/btn_play_voice_1"
			app:layout_constraintStart_toEndOf="@+id/btn_play_voice_1"
			app:layout_constraintTop_toTopOf="@+id/btn_play_voice_1"
			tools:visibility="visible" />

		<Button
			android:id="@+id/btn_auto_lift_config"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginStart="4dp"
			android:text="自动吊运配置"
			app:layout_constraintBottom_toBottomOf="@+id/btn_play_voice_1"
			app:layout_constraintStart_toEndOf="@+id/btn_play_voice_2"
			app:layout_constraintTop_toTopOf="@+id/btn_play_voice_1" />

		<!--		<Button-->
		<!--			android:id="@+id/btn_io1"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="灯IO1"-->
		<!--			android:visibility="visible"-->
		<!--			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"-->
		<!--			app:layout_constraintTop_toBottomOf="@+id/btn_stop_voice"-->
		<!--			tools:visibility="visible" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btn_io2"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:layout_marginStart="4dp"-->
		<!--			android:text="灯IO2"-->
		<!--			android:visibility="visible"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btn_io1"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btn_io1"-->
		<!--			tools:visibility="visible" />-->
		<!--		<TextView-->
		<!--			android:id="@+id/tvSanyTitle"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:layout_marginTop="8dp"-->
		<!--			android:text="Sany"-->
		<!--			android:textColor="#000000"-->
		<!--			android:textSize="18sp"-->
		<!--			app:layout_constraintStart_toStartOf="@+id/tvEcuRealtimeTitle"-->
		<!--			app:layout_constraintTop_toBottomOf="@+id/tvMagnetometer" />-->

		<!--		<TextView-->
		<!--			android:id="@+id/tvSany"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:lineSpacingMultiplier="1.2"-->
		<!--			android:textColor="#000000"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"-->
		<!--			app:layout_constraintTop_toBottomOf="@+id/tvSanyTitle" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnVariationOff"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="变幅关"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/btnVariationA"-->
		<!--			app:layout_constraintStart_toStartOf="parent"-->
		<!--			app:layout_constraintTop_toBottomOf="@+id/tvSany" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnVariationA"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="+"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/tvVariation"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btnVariationOff"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnVariationOff" />-->

		<!--		<TextView-->
		<!--			android:id="@+id/tvVariation"-->
		<!--			android:layout_width="16dp"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:gravity="center"-->
		<!--			android:text="0"-->
		<!--			android:textColor="@color/black"-->
		<!--			android:textSize="16sp"-->
		<!--			app:layout_constraintBottom_toBottomOf="@+id/btnVariationOff"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/btnVariationB"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btnVariationA"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnVariationOff" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnVariationB"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="-"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toEndOf="parent"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/tvVariation"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnVariationOff" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnSpinOff"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="回转关"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/btnSpinA"-->
		<!--			app:layout_constraintStart_toStartOf="parent"-->
		<!--			app:layout_constraintTop_toBottomOf="@+id/btnVariationOff" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnSpinA"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="+"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/tvSpin"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btnSpinOff"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnSpinOff" />-->

		<!--		<TextView-->
		<!--			android:id="@+id/tvSpin"-->
		<!--			android:layout_width="16dp"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:gravity="center"-->
		<!--			android:text="0"-->
		<!--			android:textColor="@color/black"-->
		<!--			android:textSize="16sp"-->
		<!--			app:layout_constraintBottom_toBottomOf="@+id/btnSpinOff"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/btnSpinB"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btnSpinA"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnSpinOff" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnSpinB"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="-"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toEndOf="parent"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/tvSpin"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnSpinOff" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnLiftingOff"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="起升关"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/btnLiftingA"-->
		<!--			app:layout_constraintStart_toStartOf="parent"-->
		<!--			app:layout_constraintTop_toBottomOf="@+id/btnSpinOff" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnLiftingA"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="+"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/tvLifting"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btnLiftingOff"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnLiftingOff" />-->

		<!--		<TextView-->
		<!--			android:id="@+id/tvLifting"-->
		<!--			android:layout_width="16dp"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:gravity="center"-->
		<!--			android:text="0"-->
		<!--			android:textColor="@color/black"-->
		<!--			android:textSize="16sp"-->
		<!--			app:layout_constraintBottom_toBottomOf="@+id/btnLiftingOff"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/btnLiftingB"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btnLiftingA"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnLiftingOff" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnLiftingB"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="-"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toEndOf="parent"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/tvLifting"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnLiftingOff" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnSpinBreakOn"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="回转刹车开"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/btnSpinBreakOff"-->
		<!--			app:layout_constraintStart_toStartOf="parent"-->
		<!--			app:layout_constraintTop_toBottomOf="@+id/btnLiftingOff" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnSpinBreakOff"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="回转刹车关"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/btnResetOn"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btnSpinBreakOn"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnSpinBreakOn" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnResetOn"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="复位开"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/btnResetOff"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btnSpinBreakOff"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnSpinBreakOn" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnResetOff"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="复位关"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toEndOf="parent"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btnResetOn"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnSpinBreakOn" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnPowerOn"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="动力开"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/btnPowerOff"-->
		<!--			app:layout_constraintStart_toStartOf="parent"-->
		<!--			app:layout_constraintTop_toBottomOf="@+id/btnSpinBreakOn" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnPowerOff"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="动力关"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/btnStopOn"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btnPowerOn"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnPowerOn" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnStopOn"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="急停开"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toStartOf="@+id/btnStopOff"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btnPowerOff"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnPowerOn" />-->

		<!--		<Button-->
		<!--			android:id="@+id/btnStopOff"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:text="急停关"-->
		<!--			android:textSize="12sp"-->
		<!--			app:layout_constraintEnd_toEndOf="parent"-->
		<!--			app:layout_constraintStart_toEndOf="@+id/btnStopOn"-->
		<!--			app:layout_constraintTop_toTopOf="@+id/btnPowerOn" />-->
	</androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>

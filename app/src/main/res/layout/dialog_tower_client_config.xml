<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	android:layout_width="match_parent"
	android:layout_height="wrap_content"
	android:focusable="true"
	android:focusableInTouchMode="true">

	<EditText
		android:id="@+id/et_address"
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:layout_marginStart="12dp"
		android:layout_marginEnd="12dp"
		android:imeOptions="actionDone"
		android:inputType="textWebEmailAddress"
		android:singleLine="true"
		app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="match_parent">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/layout_header"
		android:layout_width="match_parent"
		android:layout_height="57dp"
		android:background="@color/white"
		app:layout_constraintTop_toTopOf="parent">

		<FrameLayout
			android:id="@+id/fl_back"
			android:layout_width="wrap_content"
			android:layout_height="match_parent"
			android:paddingStart="21dp"
			android:paddingEnd="4dp"
			app:layout_constraintStart_toStartOf="parent">

			<ImageView
				android:layout_width="32dp"
				android:layout_height="32dp"
				android:layout_gravity="center"
				android:src="@drawable/ic_back_black" />
		</FrameLayout>

		<TextView
			android:id="@+id/tv_title"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:singleLine="true"
			android:textColor="#191923"
			android:textSize="24sp"
			android:textStyle="bold"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toTopOf="parent" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<TextView
		android:id="@+id/tv_status"
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:layout_marginTop="12dp"
		android:paddingStart="180dp"
		android:paddingEnd="0dp"
		android:textColor="#000000"
		android:textSize="14sp"
		app:layout_constraintBottom_toTopOf="@+id/tv_absolute_encoder"
		app:layout_constraintTop_toBottomOf="@+id/layout_header"
		app:layout_constraintVertical_chainStyle="packed" />

	<TextView
		android:id="@+id/tv_absolute_encoder"
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:paddingStart="180dp"
		android:paddingEnd="0dp"
		android:visibility="gone"
		app:layout_constraintBottom_toTopOf="@+id/rg_absolute_encoder"
		app:layout_constraintTop_toBottomOf="@+id/tv_status"
		tools:text="编码器"
		tools:visibility="visible" />

	<TextView
		android:id="@+id/tv_absolute_encoder_title"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_marginStart="24dp"
		android:text="编码器: "
		android:textSize="18sp"
		app:layout_constraintBottom_toBottomOf="@+id/rg_absolute_encoder"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toTopOf="@+id/rg_absolute_encoder" />

	<RadioGroup
		android:id="@+id/rg_absolute_encoder"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintStart_toEndOf="@+id/tv_absolute_encoder_title">

		<RadioButton
			android:id="@+id/rb_absolute_encoder_hook"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:checked="true"
			android:text="吊钩"
			android:textSize="18sp" />

		<RadioButton
			android:id="@+id/rb_absolute_encoder_jib"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginStart="6dp"
			android:text="大臂"
			android:textSize="18sp" />
	</RadioGroup>

	<Button
		android:id="@+id/btn_reset_absolute_encoder"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_marginStart="24dp"
		android:text="置零"
		app:layout_constraintBottom_toBottomOf="@+id/tv_absolute_encoder_title"
		app:layout_constraintStart_toEndOf="@+id/rg_absolute_encoder"
		app:layout_constraintTop_toTopOf="@+id/tv_absolute_encoder_title" />

	<Button
		android:id="@+id/btn_set_direction_cw"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_marginStart="12dp"
		android:text="顺时针"
		app:layout_constraintBottom_toBottomOf="@+id/btn_reset_absolute_encoder"
		app:layout_constraintStart_toEndOf="@+id/btn_reset_absolute_encoder"
		app:layout_constraintTop_toTopOf="@+id/btn_reset_absolute_encoder" />

	<Button
		android:id="@+id/btn_set_direction_ccw"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_marginStart="12dp"
		android:text="逆时针"
		app:layout_constraintBottom_toBottomOf="@+id/btn_reset_absolute_encoder"
		app:layout_constraintStart_toEndOf="@+id/btn_set_direction_cw"
		app:layout_constraintTop_toTopOf="@+id/btn_reset_absolute_encoder" />

	<androidx.constraintlayout.widget.Group
		android:id="@+id/group_absolute_encoder"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:visibility="gone"
		app:constraint_referenced_ids="tv_absolute_encoder_title,rg_absolute_encoder,btn_set_direction_cw,btn_set_direction_ccw,btn_reset_absolute_encoder"
		tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>

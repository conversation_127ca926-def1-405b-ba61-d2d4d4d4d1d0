<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="wrap_content"
	android:focusable="true"
	android:focusableInTouchMode="true"
	android:orientation="vertical">

	<androidx.appcompat.widget.LinearLayoutCompat
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		android:paddingHorizontal="12dp">

		<androidx.appcompat.widget.AppCompatTextView
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="上升安全高度："
			android:textColor="@color/black" />

		<androidx.appcompat.widget.AppCompatEditText
			android:id="@+id/et_up"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:inputType="number"
			android:singleLine="true" />
	</androidx.appcompat.widget.LinearLayoutCompat>

	<androidx.appcompat.widget.LinearLayoutCompat
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		android:paddingHorizontal="12dp">

		<androidx.appcompat.widget.AppCompatTextView
			android:id="@+id/tv_start_title"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="下降安全高度："
			android:textColor="@color/black" />

		<androidx.appcompat.widget.AppCompatEditText
			android:id="@+id/et_down"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:inputType="number"
			android:singleLine="true" />
	</androidx.appcompat.widget.LinearLayoutCompat>

</androidx.appcompat.widget.LinearLayoutCompat>

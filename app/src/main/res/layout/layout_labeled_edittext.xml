<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:gravity="center_vertical"
	android:orientation="horizontal">

	<com.google.android.material.textview.MaterialTextView
		android:id="@+id/tvLabel"
		android:layout_width="wrap_content"
		android:layout_height="match_parent"
		android:gravity="center_vertical"
		android:minWidth="100dp"
		android:textColor="@color/black"
		android:textSize="16sp"
		tools:text="Label" />

	<com.google.android.material.textfield.TextInputLayout
		android:id="@+id/layoutTextInput"
		style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
		android:layout_width="0dp"
		android:layout_height="match_parent"
		android:layout_marginStart="16dp"
		android:layout_weight="1"
		android:gravity="center_vertical">

		<com.google.android.material.textfield.TextInputEditText
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:singleLine="true"
			android:textSize="16sp"
			tools:text="Content" />
	</com.google.android.material.textfield.TextInputLayout>

</LinearLayout>

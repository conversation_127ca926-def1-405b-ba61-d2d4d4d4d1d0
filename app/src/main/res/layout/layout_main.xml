<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/white"
	tools:context=".ui.MainActivity">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/layout_left"
		android:layout_width="0dp"
		android:layout_height="match_parent"
		android:orientation="vertical"
		app:layout_constraintEnd_toStartOf="@+id/layout_right"
		app:layout_constraintHorizontal_weight="1"
		app:layout_constraintStart_toStartOf="parent">

		<androidx.recyclerview.widget.RecyclerView
			android:id="@+id/rv_log"
			android:layout_width="0dp"
			android:layout_height="0dp"
			android:layout_marginBottom="8dp"
			android:paddingStart="4dp"
			android:paddingEnd="4dp"
			android:scrollbars="vertical"
			app:layout_constraintBottom_toTopOf="@+id/tv_ecu_version"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<com.google.android.material.button.MaterialButton
			android:id="@+id/btnConfig"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginEnd="6dp"
			android:text="系统设置"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintEnd_toEndOf="parent" />

		<TextView
			android:id="@+id/tv_ntrip_status"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:padding="4dp"
			android:textColor="@color/text_common_color"
			app:layout_constraintBottom_toTopOf="@+id/btnConfig"
			app:layout_constraintEnd_toEndOf="@+id/btnConfig"
			tools:text="001" />

		<TextView
			android:id="@+id/tv_sn"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginBottom="6dp"
			android:textColor="@android:color/black"
			android:textSize="12sp"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintStart_toStartOf="@+id/tv_app_version"
			tools:text="设备SN：SVEALQ15222200300ZC" />

		<TextView
			android:id="@+id/tv_ecu_version"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginBottom="2dp"
			android:text="ECU版本：NA"
			android:textColor="@android:color/black"
			android:textSize="12sp"
			app:layout_constraintBottom_toTopOf="@+id/tv_app_version"
			app:layout_constraintStart_toStartOf="@+id/tv_sn" />

		<TextView
			android:id="@+id/tv_app_version"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginStart="6dp"
			android:layout_marginBottom="6dp"
			android:textColor="@android:color/black"
			android:textSize="12sp"
			app:layout_constraintBottom_toTopOf="@+id/tv_sn"
			app:layout_constraintStart_toStartOf="parent"
			tools:text="APP版本：1.0.0.1" />

		<!--		<androidx.appcompat.widget.AppCompatSpinner-->
		<!--			android:id="@+id/spinner"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:layout_marginEnd="@dimen/dimen_4dp"-->
		<!--			android:layout_marginBottom="@dimen/dimen_6dp"-->
		<!--			app:layout_constraintBottom_toBottomOf="parent"-->
		<!--			app:layout_constraintEnd_toEndOf="parent" />-->
	</androidx.constraintlayout.widget.ConstraintLayout>

	<LinearLayout
		android:id="@+id/layout_right"
		android:layout_width="0dp"
		android:layout_height="match_parent"
		android:orientation="vertical"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintHorizontal_weight="1"
		app:layout_constraintStart_toEndOf="@+id/layout_left">

		<include
			android:id="@+id/ecuLayout"
			layout="@layout/layout_ecu" />
	</LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

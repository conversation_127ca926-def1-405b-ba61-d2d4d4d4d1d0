<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	android:layout_width="400dp"
	android:layout_height="wrap_content"
	android:background="@drawable/edit_dialog_back"
	android:focusable="true"
	android:focusableInTouchMode="true"
	android:orientation="vertical">

	<LinearLayout
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="vertical"
		android:paddingLeft="@dimen/dimen_20dp"
		android:paddingTop="@dimen/dimen_20dp"
		android:paddingRight="@dimen/dimen_20dp">

		<LinearLayout
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_marginBottom="@dimen/dimen_10dp"
			android:gravity="center"
			android:orientation="horizontal">

			<TextView
				android:layout_width="120dp"
				android:layout_height="wrap_content"
				android:text="@string/ntrip_layout_host"
				android:textColor="@color/colorBlack"
				android:textSize="16sp" />

			<EditText
				android:id="@+id/et_ntrip_host"
				android:layout_width="match_parent"
				android:layout_height="40dp"
				android:background="@drawable/dialog_et_back"
				android:lines="1"
				android:maxLines="1"
				android:padding="@dimen/ux_14px"
				android:singleLine="true"
				android:textColor="@color/colorBlack"
				android:textSize="16sp" />
		</LinearLayout>

		<LinearLayout
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_marginBottom="@dimen/dimen_10dp"
			android:gravity="center"
			android:orientation="horizontal">

			<TextView
				android:layout_width="120dp"
				android:layout_height="wrap_content"
				android:text="@string/ntrip_layout_port"
				android:textColor="@color/colorBlack"
				android:textSize="16sp" />

			<EditText
				android:id="@+id/et_ntrip_port"
				android:layout_width="0dp"
				android:layout_height="40dp"
				android:layout_weight="1"
				android:background="@drawable/dialog_et_back"
				android:inputType="number"
				android:lines="1"
				android:maxLines="1"
				android:padding="@dimen/ux_14px"
				android:singleLine="true"
				android:textColor="@color/colorBlack"
				android:textSize="16sp" />

			<com.fj.towercontrol.widget.SimpleShapeButton
				android:id="@+id/btn_get_source"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_marginLeft="@dimen/dimen_10dp"
				android:padding="10dp"
				android:text="@string/ntrip_layout_get_source"
				android:textColor="@color/text_common_color2"
				android:textSize="12sp"
				app:cornerRadius="8px"
				app:solidColor="@color/color_EBB400"
				app:strokeColor="@color/colorTransparent"
				app:strokeWidth="1px" />
		</LinearLayout>

		<LinearLayout
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_marginBottom="@dimen/dimen_10dp"
			android:gravity="center"
			android:orientation="horizontal">

			<TextView
				android:layout_width="120dp"
				android:layout_height="wrap_content"
				android:text="@string/ntrip_layout_source_node"
				android:textColor="@color/colorBlack"
				android:textSize="16sp" />

			<EditText
				android:id="@+id/et_source_point"
				android:layout_width="0dp"
				android:layout_height="40dp"
				android:layout_weight="1"
				android:background="@drawable/dialog_et_back"
				android:padding="11dp"
				android:singleLine="true"
				android:textColor="@color/colorBlack"
				android:textSize="16sp" />

			<ImageView
				android:id="@+id/iv_spinner"
				android:layout_width="32dp"
				android:layout_height="32dp"
				android:layout_marginStart="10dp"
				android:src="@drawable/ic_arrow_down" />
		</LinearLayout>

		<TextView
			android:id="@+id/tv_ntrip_state"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginStart="90dp"
			android:layout_marginBottom="@dimen/dimen_10dp"
			android:text=""
			android:visibility="gone" />

		<LinearLayout
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_marginBottom="@dimen/dimen_10dp"
			android:gravity="center"
			android:orientation="horizontal">

			<TextView
				android:layout_width="120dp"
				android:layout_height="wrap_content"
				android:text="@string/ntrip_layout_account"
				android:textColor="@color/colorBlack"
				android:textSize="16sp" />

			<EditText
				android:id="@+id/et_ntrip_account"
				android:layout_width="match_parent"
				android:layout_height="40dp"
				android:background="@drawable/dialog_et_back"
				android:lines="1"
				android:maxLines="1"
				android:padding="@dimen/ux_14px"
				android:singleLine="true"
				android:textColor="@color/colorBlack"
				android:textSize="16sp" />
		</LinearLayout>

		<LinearLayout
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_marginBottom="@dimen/dimen_10dp"
			android:gravity="center"
			android:orientation="horizontal">

			<TextView
				android:layout_width="120dp"
				android:layout_height="wrap_content"
				android:text="@string/ntrip_layout_password"
				android:textColor="@color/colorBlack"
				android:textSize="16sp" />

			<EditText
				android:id="@+id/et_ntrip_password"
				android:layout_width="match_parent"
				android:layout_height="40dp"
				android:background="@drawable/dialog_et_back"
				android:inputType="textPassword"
				android:lines="1"
				android:maxLines="1"
				android:padding="@dimen/ux_14px"
				android:singleLine="true"
				android:textColor="@color/colorBlack"
				android:textSize="16sp" />
		</LinearLayout>
	</LinearLayout>

	<View
		android:layout_width="match_parent"
		android:layout_height="1px"
		android:background="@color/color_ddd" />

	<LinearLayout
		android:id="@+id/ll_btn"
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:weightSum="2">

		<TextView
			android:id="@+id/btn_dialog_cancel"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_weight="1"
			android:gravity="center"
			android:padding="@dimen/ux_20px"
			android:text="@string/cancel"
			android:textColor="@color/colorBlack"
			android:textSize="16sp" />

		<View
			android:layout_width="1px"
			android:layout_height="match_parent"
			android:background="@color/color_ddd" />

		<TextView
			android:id="@+id/btn_dialog_confirm"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_weight="1"
			android:gravity="center"
			android:padding="@dimen/ux_20px"
			android:text="@string/ntrip_connect"
			android:textColor="@color/color_EBB400"
			android:textSize="16sp" />
	</LinearLayout>


</LinearLayout>

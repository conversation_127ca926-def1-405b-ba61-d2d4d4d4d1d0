<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/white"
	android:fitsSystemWindows="true"
	android:focusable="true"
	android:focusableInTouchMode="true"
	android:orientation="vertical">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:layout_width="match_parent"
		android:layout_height="58dp">

		<FrameLayout
			android:id="@+id/fl_back"
			android:layout_width="wrap_content"
			android:layout_height="match_parent"
			android:paddingStart="21dp"
			android:paddingEnd="4dp"
			app:layout_constraintStart_toStartOf="parent">

			<androidx.appcompat.widget.AppCompatImageView
				android:layout_width="32dp"
				android:layout_height="32dp"
				android:layout_gravity="center"
				android:src="@drawable/ic_back_black" />
		</FrameLayout>

		<TextView
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:singleLine="true"
			android:text="系统设置"
			android:textColor="#191923"
			android:textSize="24sp"
			android:textStyle="bold"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<com.google.android.material.divider.MaterialDivider
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			app:layout_constraintBottom_toBottomOf="parent" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:layout_width="match_parent"
		android:layout_height="0dp"
		android:layout_weight="1"
		app:layout_constraintBottom_toBottomOf="parent">

		<View
			android:id="@+id/divider"
			android:layout_width="1dp"
			android:layout_height="match_parent"
			android:background="@color/color_list_divider"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent" />

		<androidx.core.widget.NestedScrollView
			android:layout_width="0dp"
			android:layout_height="match_parent"
			android:fadeScrollbars="false"
			android:scrollbars="vertical"
			app:layout_constraintEnd_toStartOf="@+id/divider"
			app:layout_constraintStart_toStartOf="parent">

			<LinearLayout
				android:layout_width="match_parent"
				android:layout_height="match_parent"
				android:orientation="vertical"
				android:paddingHorizontal="16dp"
				android:paddingVertical="8dp">

				<LinearLayout
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:gravity="center_vertical"
					android:orientation="horizontal">

					<com.google.android.material.textview.MaterialTextView
						android:layout_width="wrap_content"
						android:layout_height="match_parent"
						android:gravity="center_vertical"
						android:minWidth="100dp"
						android:text="当前塔吊类型"
						android:textColor="@color/black"
						android:textSize="16sp" />

					<RadioGroup
						android:id="@+id/rgCraneType"
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:layout_marginStart="12dp"
						android:gravity="center_vertical"
						android:orientation="horizontal">

						<com.google.android.material.radiobutton.MaterialRadioButton
							android:id="@+id/rbYongMao"
							android:layout_width="wrap_content"
							android:layout_height="wrap_content"
							android:gravity="center_vertical"
							android:text="永茂"
							tools:checked="true" />

						<com.google.android.material.radiobutton.MaterialRadioButton
							android:id="@+id/rbSany"
							android:layout_width="wrap_content"
							android:layout_height="wrap_content"
							android:layout_marginStart="12dp"
							android:gravity="center_vertical"
							android:text="三一" />

						<com.google.android.material.radiobutton.MaterialRadioButton
							android:id="@+id/rbXcmg"
							android:layout_width="wrap_content"
							android:layout_height="wrap_content"
							android:layout_marginStart="12dp"
							android:gravity="center_vertical"
							android:text="徐工" />

						<com.google.android.material.radiobutton.MaterialRadioButton
							android:id="@+id/rbYongMaoSTT153"
							android:layout_width="wrap_content"
							android:layout_height="wrap_content"
							android:layout_marginStart="12dp"
							android:gravity="center_vertical"
							android:text="永茂平头" />
					</RadioGroup>
				</LinearLayout>

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etLargeScreenUrl"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					app:labelText="大屏服务地址" />

				<LinearLayout
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:gravity="center_vertical"
					android:orientation="horizontal">

					<com.fj.towercontrol.widget.LabeledEditText
						android:id="@+id/etHook"
						android:layout_width="0dp"
						android:layout_height="wrap_content"
						android:layout_weight="1"
						app:labelText="智能吊钩地址" />

					<com.google.android.material.switchmaterial.SwitchMaterial
						android:id="@+id/swHookUsr"
						android:layout_width="wrap_content"
						android:layout_height="wrap_content"
						android:layout_marginStart="8dp"
						android:text="USR"
						app:switchPadding="2dp" />
				</LinearLayout>

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etYongMao485Url"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					app:labelText="永茂塔机地址" />

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etKeyIoUrl"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:visibility="gone"
					app:labelText="塔上钥匙地址" />

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etAlarmIoUrl"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:visibility="gone"
					app:labelText="灯光告警地址" />

				<LinearLayout
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:gravity="center_vertical">

					<com.fj.towercontrol.widget.LabeledEditText
						android:id="@+id/etVoiceAlarmUrl"
						android:layout_width="0dp"
						android:layout_height="wrap_content"
						android:layout_weight="1"
						app:labelText="声光告警地址" />

					<com.google.android.material.switchmaterial.SwitchMaterial
						android:id="@+id/swVoiceUsr"
						android:layout_width="wrap_content"
						android:layout_height="wrap_content"
						android:layout_marginStart="8dp"
						android:text="USR"
						app:switchPadding="2dp" />
				</LinearLayout>

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etWeatherStationUrl"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:visibility="gone"
					app:labelText="气象站地址" />

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etMotorUrl"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					app:labelText="舵机地址" />

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etMotorLimit"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					app:labelText="舵机限制角度" />

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etCarCraneUrl"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:visibility="gone"
					app:labelText="汽车吊地址" />

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etCarCraneWorkRadius"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:visibility="gone"
					app:labelText="汽车吊半径" />

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etCarCraneWarnDistance"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:visibility="gone"
					app:labelText="橙色告警距离" />

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etCarCraneCriticalDistance"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:visibility="gone"
					app:labelText="红色告警距离" />

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etCarCraneLatitude"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:visibility="gone"
					app:labelText="汽车吊纬度" />

				<com.fj.towercontrol.widget.LabeledEditText
					android:id="@+id/etCarCraneLongitude"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:visibility="gone"
					app:labelText="汽车吊经度" />

			</LinearLayout>
		</androidx.core.widget.NestedScrollView>

		<androidx.core.widget.NestedScrollView
			android:layout_width="0dp"
			android:layout_height="match_parent"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toEndOf="@+id/divider">

			<androidx.constraintlayout.widget.ConstraintLayout
				android:layout_width="match_parent"
				android:layout_height="wrap_content"
				android:paddingHorizontal="16dp">

				<com.google.android.material.button.MaterialButton
					android:id="@+id/btnFlushLog"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginTop="8dp"
					android:text="刷新日志"
					app:layout_constraintStart_toStartOf="parent"
					app:layout_constraintTop_toTopOf="parent" />

				<com.google.android.material.button.MaterialButton
					android:id="@+id/btnRegister"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginTop="4dp"
					android:text="设备注册"
					app:layout_constraintStart_toStartOf="@+id/btnFlushLog"
					app:layout_constraintTop_toBottomOf="@+id/btnFlushLog" />

				<com.google.android.material.button.MaterialButton
					android:id="@+id/btnDebug"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginTop="4dp"
					android:text="塔吊调试"
					app:layout_constraintStart_toStartOf="@+id/btnFlushLog"
					app:layout_constraintTop_toBottomOf="@+id/btnRegister" />

				<com.google.android.material.button.MaterialButton
					android:id="@+id/btnNtrip"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginTop="4dp"
					android:text="Ntrip配置"
					app:layout_constraintStart_toStartOf="@+id/btnFlushLog"
					app:layout_constraintTop_toBottomOf="@+id/btnDebug" />

				<com.google.android.material.button.MaterialButton
					android:id="@+id/btnUpgrade"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginTop="4dp"
					android:text="检查升级"
					app:layout_constraintStart_toStartOf="@+id/btnFlushLog"
					app:layout_constraintTop_toBottomOf="@+id/btnNtrip" />

				<com.google.android.material.button.MaterialButton
					android:id="@+id/btnRestart"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginTop="4dp"
					android:text="重启APP"
					app:layout_constraintStart_toStartOf="@+id/btnFlushLog"
					app:layout_constraintTop_toBottomOf="@+id/btnUpgrade" />
			</androidx.constraintlayout.widget.ConstraintLayout>
		</androidx.core.widget.NestedScrollView>
	</androidx.constraintlayout.widget.ConstraintLayout>

	<com.google.android.material.divider.MaterialDivider
		android:layout_width="match_parent"
		android:layout_height="wrap_content" />

	<com.google.android.material.button.MaterialButton
		android:id="@+id/btnSave"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_gravity="center_horizontal"
		android:layout_marginVertical="8dp"
		android:paddingHorizontal="32dp"
		android:text="配置保存"
		android:textSize="16sp" />
</LinearLayout>

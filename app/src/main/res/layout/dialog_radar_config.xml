<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="wrap_content"
	android:focusable="true"
	android:focusableInTouchMode="true"
	android:orientation="vertical">

	<androidx.appcompat.widget.LinearLayoutCompat
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		android:paddingHorizontal="12dp">

		<androidx.appcompat.widget.AppCompatTextView
			android:id="@+id/tv_distance_title"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="底部雷达距吊钩距离"
			android:textColor="@color/black" />

		<androidx.appcompat.widget.AppCompatEditText
			android:id="@+id/et_distance"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:inputType="number"
			android:singleLine="true" />
	</androidx.appcompat.widget.LinearLayoutCompat>

	<androidx.appcompat.widget.LinearLayoutCompat
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		android:paddingHorizontal="12dp">

		<androidx.appcompat.widget.AppCompatTextView
			android:id="@+id/tv_start_title"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="底部雷达起始角度"
			android:textColor="@color/black" />

		<androidx.appcompat.widget.AppCompatEditText
			android:id="@+id/et_start"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:inputType="number"
			android:singleLine="true" />
	</androidx.appcompat.widget.LinearLayoutCompat>

	<androidx.appcompat.widget.LinearLayoutCompat
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		android:paddingHorizontal="12dp">

		<androidx.appcompat.widget.AppCompatTextView
			android:id="@+id/tv_end_title"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="底部雷达截止角度"
			android:textColor="@color/black" />

		<androidx.appcompat.widget.AppCompatEditText
			android:id="@+id/et_end"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:inputType="number"
			android:singleLine="true" />
	</androidx.appcompat.widget.LinearLayoutCompat>

	<androidx.appcompat.widget.LinearLayoutCompat
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		android:paddingHorizontal="12dp">

		<androidx.appcompat.widget.AppCompatTextView
			android:id="@+id/tv_install_anagle_title"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="环向雷达安装倾角"
			android:textColor="@color/black" />

		<androidx.appcompat.widget.AppCompatEditText
			android:id="@+id/et_install_angle"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:inputType="numberSigned"
			android:singleLine="true" />
	</androidx.appcompat.widget.LinearLayoutCompat>

	<androidx.appcompat.widget.LinearLayoutCompat
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		android:paddingHorizontal="12dp">

		<androidx.appcompat.widget.AppCompatTextView
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="离地高度阈值(cm)"
			android:textColor="@color/black" />

		<androidx.appcompat.widget.AppCompatEditText
			android:id="@+id/et_ground_distance"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:inputType="number"
			android:singleLine="true" />
	</androidx.appcompat.widget.LinearLayoutCompat>

	<androidx.appcompat.widget.LinearLayoutCompat
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		android:paddingHorizontal="12dp">

		<androidx.appcompat.widget.AppCompatTextView
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="吊物水平阈值(cm)"
			android:textColor="@color/black" />

		<androidx.appcompat.widget.AppCompatEditText
			android:id="@+id/et_lift_horizontal_threshold"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:inputType="number"
			android:singleLine="true" />
	</androidx.appcompat.widget.LinearLayoutCompat>

	<androidx.appcompat.widget.LinearLayoutCompat
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		android:paddingHorizontal="12dp">

		<androidx.appcompat.widget.AppCompatTextView
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="吊物竖直阈值(cm)"
			android:textColor="@color/black" />

		<androidx.appcompat.widget.AppCompatEditText
			android:id="@+id/et_lift_vertical_threshold"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:inputType="number"
			android:singleLine="true" />
	</androidx.appcompat.widget.LinearLayoutCompat>

	<androidx.appcompat.widget.LinearLayoutCompat
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		android:paddingHorizontal="12dp">

		<androidx.appcompat.widget.AppCompatTextView
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="雷达到吊物垂直距离"
			android:textColor="@color/black" />

		<androidx.appcompat.widget.AppCompatEditText
			android:id="@+id/et_radar_to_lift_distance"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:inputType="number"
			android:singleLine="true" />
	</androidx.appcompat.widget.LinearLayoutCompat>

</androidx.appcompat.widget.LinearLayoutCompat>

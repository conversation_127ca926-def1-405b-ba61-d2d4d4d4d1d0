<?xml version="1.0" encoding="utf-8"?>
<resources>

	<style name="DialogTheme" parent="@android:style/Theme.Dialog">
		<!-- 边框 -->
		<item name="android:windowFrame">@null</item>
		<!-- 是否浮现在activity之上 -->
		<item name="android:windowIsFloating">true</item>
		<!-- 半透明 -->
		<item name="android:windowIsTranslucent">true</item>
		<!-- 无标题 -->
		<item name="android:windowNoTitle">true</item>
		<item name="android:background">@android:color/transparent</item>
		<!-- 背景透明 -->
		<item name="android:windowBackground">@android:color/transparent</item>
		<!-- 模糊 -->
		<item name="android:backgroundDimEnabled">true</item>
		<!-- 遮罩层 -->
		<item name="android:backgroundDimAmount">0.5</item>
	</style>
</resources>

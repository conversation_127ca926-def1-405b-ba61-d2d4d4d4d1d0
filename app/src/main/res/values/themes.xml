<resources xmlns:tools="http://schemas.android.com/tools">
	<!-- Base application theme. -->
	<style name="AppTheme" parent="Theme.Material3.Light.NoActionBar">
		<!-- Primary brand color. -->
		<item name="colorPrimary">@color/purple_500</item>
		<item name="colorPrimaryVariant">@color/purple_700</item>
		<item name="colorOnPrimary">@color/white</item>
		<!-- Secondary brand color. -->
		<item name="colorSecondary">@color/teal_200</item>
		<item name="colorSecondaryVariant">@color/teal_700</item>
		<item name="colorOnSecondary">@color/black</item>
		<!-- Status bar color. -->
		<item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
		<!-- Customize your theme here. -->
		<item name="android:navigationBarColor">@color/colorTransparent</item>
		<item name="android:windowFullscreen">true</item>
	</style>

	<style name="SplashTheme" parent="AppTheme">
		<item name="android:windowBackground">@drawable/ic_splash</item>
	</style>
</resources>

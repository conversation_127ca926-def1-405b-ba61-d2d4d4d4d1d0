package com.fj.towercontrol.mqtt.entity;

import static org.junit.Assert.assertNotNull;

import com.fj.towercontrol.data.entity.CargoWeightInfo;
import com.fj.towercontrol.data.entity.EcuLog;
import com.fj.towercontrol.data.entity.EcuRealtimeData;
import com.fj.towercontrol.data.entity.TowerBaseConfig;
import com.fj.towercontrol.data.entity.TowerConfig;
import com.fj.towercontrol.util.MemoryStore;

import org.junit.Ignore;
import org.junit.Test;

public class TowerDataBuildTest {

	@Ignore("Depends on MemoryStore(FileStore/MMKV) and Android runtime; will move to Robolectric later")
	@Test
	public void build_minimal_input() {
		// Prepare minimal state for build()
		EcuRealtimeData realtime = new EcuRealtimeData();
		EcuLog log = new EcuLog();
		TowerBaseConfig base = new TowerBaseConfig();
		// base.setTowerHeight is not present; TowerBaseConfig mainly holds arm length/positions.
		TowerConfig cfg = new TowerConfig();
		cfg.setTowerBaseConfig(base);
		// Avoid touching FileStore/MMKV paths; only set in-memory singleton
		MemoryStore.getInstance().setTowerConfig(cfg);

		CargoWeightInfo cargo = new CargoWeightInfo(1000, 200, 800);
		TowerData data = TowerData.build(realtime, log, cargo);
		assertNotNull(data);
		// Further property-level asserts can be refined with confirmed mapping/units
	}
}


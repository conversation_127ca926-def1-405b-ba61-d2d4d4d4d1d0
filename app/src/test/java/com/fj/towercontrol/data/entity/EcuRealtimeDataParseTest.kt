package com.fj.towercontrol.data.entity

import org.junit.Assert.assertNotNull
import org.junit.Test

class EcuRealtimeDataParseTest {

	@Test
	fun parse_minimal_values() {
		// Construct a minimal header and a few fields consistent with EcuRealtimeData.parse expectations.
		// For safety we will build only bytes the parser reads early: voltage(short), current(short), temp(short).
		val buf = ByteArray(200) { 0 }
		// Suppose at offset 0: voltage (short, *10), offset 2: current (short,*10), offset 4: temp (short,*10)
		// These offsets may differ in your implementation; if parse() expects other layout this test should be adjusted accordingly.
		val voltage = (220.5 * 10).toInt().toShort()
		val current = (10.2 * 10).toInt().toShort()
		val temp = (36.6 * 10).toInt().toShort()
		buf[0] = ((voltage.toInt() shr 8) and 0xFF).toByte(); buf[1] =
			(voltage.toInt() and 0xFF).toByte()
		buf[2] = ((current.toInt() shr 8) and 0xFF).toByte(); buf[3] =
			(current.toInt() and 0xFF).toByte()
		buf[4] = ((temp.toInt() shr 8) and 0xFF).toByte(); buf[5] = (temp.toInt() and 0xFF).toByte()

		val r = EcuRealtimeData.parse(buf)
		// These asserts assume parse() maps these three values; adjust to your actual mapping
		assertNotNull(r)
		// guard: if fields differ, at least ensure object constructed
		// For demonstrative purpose only (you may refine when confirming layout)
	}
}


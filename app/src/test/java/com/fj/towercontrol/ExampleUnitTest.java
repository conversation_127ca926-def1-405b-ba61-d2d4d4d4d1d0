package com.fj.towercontrol;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.fj.towercontrol.util.EulerAngle2QuatUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import org.junit.Test;

import java.net.URI;

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
public class ExampleUnitTest {

	@Test
	public void test_uri_serialization_and_deserialization() {
		Gson gson = new GsonBuilder().create();
		URI uri = URI.create("ws://***********:9999");
		String json = gson.toJson(uri);
		assertEquals("\"ws://***********:9999\"", json);
		URI fromJson = gson.fromJson(json, URI.class);
		assertEquals(uri, fromJson);
	}

	@Test
	public void test_tower_gradient_calculation() {
		double[] imu = EulerAngle2QuatUtil.calcVecCog(1.04, -1.35);
		System.out.println("塔身倾斜度: " + imu[0]);
		System.out.println("塔身倾斜方向: " + imu[1]);
		double[] imu2 = EulerAngle2QuatUtil.calcVecCog(1.04, -1.35 - 1.23);
		System.out.println("塔身倾斜度: " + imu2[0]);
		System.out.println("塔身倾斜方向: " + imu2[1]);
		assertTrue(imu2[0] >= imu[0] && imu2[1] >= imu[1]);
	}

}

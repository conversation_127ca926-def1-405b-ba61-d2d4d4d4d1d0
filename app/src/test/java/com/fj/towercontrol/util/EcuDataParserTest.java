package com.fj.towercontrol.util;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

import java.util.Arrays;
import java.util.BitSet;
import java.util.List;

public class EcuDataParserTest {

	@Test
	public void test_parseIntegerBits_basic() {
		int value = 0b1011_0010; // 178
		// bits set at positions (1-based): 2,5,6,8
		java.util.List<Integer> list = EcuDataParser.parseIntegerBits(value);
		assertTrue(list.contains(2));
		assertTrue(list.contains(5));
		assertTrue(list.contains(6));
		assertTrue(list.contains(8));
		assertEquals(4, list.size());
	}

	@Test
	public void test_parseLongBits_basic() {
		long value = 0L;
		// set bits at 1-based positions: 1, 9, 29, 32
		value |= 1L << 0;   // pos1
		value |= 1L << 8;   // pos9
		value |= 1L << 28;  // pos29
		value |= 1L << 31;  // pos32
		java.util.List<Integer> list = EcuDataParser.parseLongBits(value);
		assertTrue(list.contains(1));
		assertTrue(list.contains(9));
		assertTrue(list.contains(29));
		assertTrue(list.contains(32));
		assertEquals(4, list.size());
	}

	@Test
	public void test_convertBitSetToList() {
		BitSet bs = new BitSet();
		bs.set(0); // 1st -> index 1
		bs.set(3); // 4th -> index 4
		List<Integer> list = EcuDataParser.convertBitSetToList(bs);
		assertEquals(Arrays.asList(1, 4), list);
	}

	@Test
	public void test_parseEcuButtonStatus() {
		// instruction (first 4 bytes, big-endian), set 1-based bit 26 to trigger left-左上绿 => mapped to 1
		int instruction = 1 << 25;
		byte b0 = (byte) ((instruction >>> 24) & 0xFF);
		byte b1 = (byte) ((instruction >>> 16) & 0xFF);
		byte b2 = (byte) ((instruction >>> 8) & 0xFF);
		byte b3 = (byte) (instruction & 0xFF);
		// buttonStatus in 5th byte; set 1-based bits 6 and 7 to trigger 2,3
		byte b4 = (byte) ((1 << 5) | (1 << 6)); // 0x60
		byte[] data = new byte[]{b0, b1, b2, b3, b4};

		List<Integer> on = EcuDataParser.parseEcuButtonStatus(data);
		assertTrue(on.contains(1));
		assertTrue(on.contains(2));
		assertTrue(on.contains(3));
		assertFalse(on.contains(4));
	}
}


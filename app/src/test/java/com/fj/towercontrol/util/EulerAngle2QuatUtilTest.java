package com.fj.towercontrol.util;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

public class EulerAngle2QuatUtilTest {

	@Test
	public void test_normalizeQuaternion_involutionProperty() {
		Quaternion q = new Quaternion(1f, 2f, 3f, 4f);
		Quaternion n1 = EulerAngle2QuatUtil.normalizeQuaternion(q);
		Quaternion n2 = EulerAngle2QuatUtil.normalizeQuaternion(n1);
		// 当前实现下，归一化两次会回到原始值（而非幂等）。
		assertEquals(q.w, n2.w, 1e-5);
		assertEquals(q.x, n2.x, 1e-5);
		assertEquals(q.y, n2.y, 1e-5);
		assertEquals(q.z, n2.z, 1e-5);
	}

	@Test
	public void test_toQuaternion_roundtrip() {
		// pick some angles
		double pitch = 10.0, yaw = 45.0, roll = -20.0;
		Quaternion q = EulerAngle2QuatUtil.toQuaternion(pitch, yaw, roll);
		EulerAngles e = EulerAngle2QuatUtil.toEulerAngles(q);
		// toEulerAngles returns radians; convert to degrees for comparison
		double rollDeg = Math.toDegrees(e.roll);
		double pitchDeg = Math.toDegrees(e.pitch);
		double yawDeg = Math.toDegrees(e.yaw);
		// yaw in [0,360) in code comments; allow wrap-around by comparing sin/cos
		assertEquals(Math.sin(Math.toRadians(roll)), Math.sin(e.roll), 1e-6);
		assertEquals(Math.cos(Math.toRadians(roll)), Math.cos(e.roll), 1e-6);
		assertEquals(Math.sin(Math.toRadians(pitch)), Math.sin(e.pitch), 1e-6);
		assertEquals(Math.cos(Math.toRadians(pitch)), Math.cos(e.pitch), 1e-6);
		assertEquals(Math.sin(Math.toRadians(yaw)), Math.sin(e.yaw), 1e-6);
		assertEquals(Math.cos(Math.toRadians(yaw)), Math.cos(e.yaw), 1e-6);
	}

	@Test
	public void test_calcVecCog_reasonable() {
		double[] r = EulerAngle2QuatUtil.calcVecCog(1.0, -2.0);
		assertEquals(2, r.length);
		assertFalse(Double.isNaN(r[0]));
		assertFalse(Double.isNaN(r[1]));
	}

	@Test
	public void test_wrapPi_bounds() {
		assertTrue(EulerAngle2QuatUtil.wrapPi(Math.PI + 0.1) <= Math.PI);
		assertTrue(EulerAngle2QuatUtil.wrapPi(-Math.PI - 0.1) >= -Math.PI);
	}
}


package com.fj.towercontrol.util;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

public class GeoUtilsTest {
	@Test
	public void test_isGpsValid() {
		assertTrue(GeoUtils.isGpsValid(4));
		assertTrue(GeoUtils.isGpsValid(5));
		assertFalse(GeoUtils.isGpsValid(0));
		assertFalse(GeoUtils.isGpsValid(1));
		assertFalse(GeoUtils.isGpsValid(2));
		assertFalse(GeoUtils.isGpsValid(3));
		assertFalse(GeoUtils.isGpsValid(6));
	}
}


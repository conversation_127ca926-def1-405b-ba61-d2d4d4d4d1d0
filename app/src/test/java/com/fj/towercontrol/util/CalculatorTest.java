package com.fj.towercontrol.util;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

public class CalculatorTest {

	@Test
	public void test_calculateDescentHeight() {
		double h = Calculator.calculateDescentHeight(30.0, 10.0, 100.0, 0.5, 95.0);
		// 100 + 0.5 - 95 + sin(30°)*10 = 5.5 + 5 = 10.5
		assertEquals(10.5, h, 1e-6);
	}

	@Test
	public void test_calculateRadarRotation_wraps_0_360() {
		// simple deterministic case
		double angle = Calculator.calculateRadarRotation(180, 90);
		assertTrue(angle >= 0 && angle < 360);
		// tower=0, hook=0 -> convert: towerAngle=(0-155+360)%360=205, hookAngle=(0-90+360)%360=270, diff=65
		assertEquals(65, Calculator.calculateRadarRotation(0, 0), 1e-9);
	}

	@Test
	public void test_calculateDistance_haversine_symmetry() {
		double d1 = Calculator.calculateDistance(30.0, 120.0, 31.0, 121.0);
		double d2 = Calculator.calculateDistance(31.0, 121.0, 30.0, 120.0);
		assertEquals(d1, d2, 1e-9);
		assertTrue(d1 > 0);
	}

	@Test
	public void test_calculate3DDistance_pythagorean() {
		double surface = Calculator.calculateDistance(30, 120, 30, 120.01); // ~ distance in meters
		double result = Calculator.calculate3DDistance(30, 120, 100, 30, 120.01, 80);
		double expected = Math.hypot(surface, 20);
		assertEquals(expected, result, 1e-6);
	}

	@Test
	public void test_target_distance_conversions() {
		// Use some synthetic values to validate monotonic/expected ranges
		double h = Calculator.calculateTargetHorizontalDistance(10.0, 15.0, 0.5, -10.0);
		double v = Calculator.calculateTargetVerticalDistance(10.0, 15.0, -10.0);
		assertTrue(h > 0);
		assertTrue(Math.abs(v) >= 0);
	}
}


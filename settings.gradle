pluginManagement {
	repositories {
		mavenCentral()
		google()
		gradlePluginPortal()
	}
}
dependencyResolutionManagement {
	repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
	repositories {
		mavenCentral()
		google()
		maven { url "https://maven.aliyun.com/repository/public" }
		maven { url "https://nexus.fjdynamics.com/repository/maven-releases" }
		maven { url "https://nexus.fjdynamics.com/repository/maven-snapshots" }
		maven { url 'https://jitpack.io' }
	}
}
rootProject.name = "TowerControl"
include ':app'
include ':fjprotocol'
include ':logger'
include ':common'

plugins {
	alias(libs.plugins.android.library)
}

android {
	namespace 'com.fjdynamics.app.logger'
	compileSdk versions.compileSdk

	defaultConfig {
		minSdk versions.minSdk
		targetSdk versions.targetSdk

		testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
		consumerProguardFiles "consumer-rules.pro"
	}

	buildTypes {
		release {
			minifyEnabled false
			proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
		}
	}
	compileOptions {
		sourceCompatibility JavaVersion.VERSION_21
		targetCompatibility JavaVersion.VERSION_21
	}
}

dependencies {
	compileOnly(libs.androidx.appcompat)
	implementation libs.timber

	testImplementation libs.junit
	androidTestImplementation libs.androidx.junit
	androidTestImplementation libs.androidx.test.espresso
}

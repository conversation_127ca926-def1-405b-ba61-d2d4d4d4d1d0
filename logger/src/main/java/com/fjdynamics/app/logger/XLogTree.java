package com.fjdynamics.app.logger;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import timber.log.Timber;

/**
 * 使用XLog打印日志
 *
 * <AUTHOR>
 */
public class XLogTree extends Timber.Tree {

	@Override
	protected void log(
		int priority, @Nullable String tag, @NonNull String message, @Nullable Throwable t) {
		switch (priority) {
			case android.util.Log.VERBOSE:
				LoggerManager.getAppLogger().v(tag, message);
				break;
			case android.util.Log.DEBUG:
				LoggerManager.getAppLogger().d(tag, message);
				break;
			case android.util.Log.INFO:
				LoggerManager.getAppLogger().i(tag, message);
				break;
			case android.util.Log.WARN:
				LoggerManager.getAppLogger().w(tag, message);
				break;
			case android.util.Log.ERROR:
				LoggerManager.getAppLogger().e(tag, message);
				break;
			default:
				break;
		}
	}
}

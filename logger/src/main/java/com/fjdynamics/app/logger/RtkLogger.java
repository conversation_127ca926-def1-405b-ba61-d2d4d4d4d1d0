package com.fjdynamics.app.logger;

import com.tencent.mars.xlog.Log;

/**
 * rtk相关日志记录工具
 *
 * <AUTHOR>
 */
public class RtkLogger {

	private RtkLogger() {
	}

	public static void d(String tag, String msg) {
		Log.LogInstance rtkLog = LoggerManager.getRtkLogger();
		if (rtkLog == null) {
			return;
		}
		rtkLog.d(tag, msg);
	}

	public static void i(String tag, String msg) {
		Log.LogInstance rtkLog = LoggerManager.getRtkLogger();
		if (rtkLog == null) {
			return;
		}
		rtkLog.i(tag, msg);
	}

	public static void w(String tag, String msg) {
		Log.LogInstance rtkLog = LoggerManager.getRtkLogger();
		if (rtkLog == null) {
			return;
		}
		rtkLog.w(tag, msg);
	}

	public static void e(String tag, String msg) {
		Log.LogInstance rtkLog = LoggerManager.getRtkLogger();
		if (rtkLog == null) {
			return;
		}
		rtkLog.e(tag, msg);
	}
}

package com.fjdynamics.app.logger;

import android.app.Application;
import android.content.ContentProvider;
import android.content.ContentValues;
import android.database.Cursor;
import android.net.Uri;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * 通过ContentProvider获取ApplicationContext
 *
 * <AUTHOR>
 */
public class ContextProvider extends ContentProvider {

	@Override
	public boolean onCreate() {
		LoggerManager.init((Application) getContext().getApplicationContext());
		return true;
	}

	@Nullable
	@Override
	public Cursor query(
		@NonNull Uri uri,
		@Nullable String[] projection,
		@Nullable String selection,
		@Nullable String[] selectionArgs,
		@Nullable String sortOrder) {
		return null;
	}

	@Nullable
	@Override
	public String getType(@NonNull Uri uri) {
		return null;
	}

	@Nullable
	@Override
	public Uri insert(@NonNull Uri uri, @Nullable ContentValues values) {
		return null;
	}

	@Override
	public int delete(
		@NonNull Uri uri, @Nullable String selection, @Nullable String[] selectionArgs) {
		return 0;
	}

	@Override
	public int update(
		@NonNull Uri uri,
		@Nullable ContentValues values,
		@Nullable String selection,
		@Nullable String[] selectionArgs) {
		return 0;
	}
}

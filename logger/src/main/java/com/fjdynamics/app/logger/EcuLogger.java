package com.fjdynamics.app.logger;

import com.tencent.mars.xlog.Log;

/**
 * ecu日志记录工具
 */
public class EcuLogger {

	public static void d(String tag, String msg) {
		Log.LogInstance ecuLogger = LoggerManager.getEcuLogger();
		if (ecuLogger == null) {
			return;
		}
		ecuLogger.d(tag, msg);
	}

	public static void i(String tag, String msg) {
		Log.LogInstance ecuLogger = LoggerManager.getEcuLogger();
		if (ecuLogger == null) {
			return;
		}
		ecuLogger.i(tag, msg);
	}

	public static void w(String tag, String msg) {
		Log.LogInstance ecuLogger = LoggerManager.getEcuLogger();
		if (ecuLogger == null) {
			return;
		}
		ecuLogger.w(tag, msg);
	}

	public static void e(String tag, String msg) {
		Log.LogInstance ecuLogger = LoggerManager.getEcuLogger();
		if (ecuLogger == null) {
			return;
		}
		ecuLogger.e(tag, msg);
	}
}

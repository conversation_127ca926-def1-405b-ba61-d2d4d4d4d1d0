package com.fjdynamics.app.logger;

import timber.log.Timber;

/**
 * 对外提供的打印日志工具
 *
 * <AUTHOR>
 */
public class Logger {

	public static void v(String tag, String msg) {
		Timber.tag(tag).v(msg);
	}

	public static void d(String tag, String msg) {
		Timber.tag(tag).d(msg);
	}

	public static void i(String tag, String msg) {
		Timber.tag(tag).i(msg);
	}

	public static void w(String tag, String msg) {
		Timber.tag(tag).w(msg);
	}

	public static void e(String tag, String msg) {
		Timber.tag(tag).e(msg);
	}

	public static void e(String tag, String msg, Throwable t) {
		Timber.tag(tag).e(t, msg);
	}
}

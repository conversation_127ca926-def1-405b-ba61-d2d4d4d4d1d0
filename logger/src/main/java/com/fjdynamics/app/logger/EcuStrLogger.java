package com.fjdynamics.app.logger;

import com.tencent.mars.xlog.Log;

/**
 * ECU上报字符串日志记录工具
 *
 * <AUTHOR>
 * @since 2024/12/9
 */
public class EcuStrLogger {

	public static void d(String tag, String msg) {
		Log.LogInstance ecuLogger = LoggerManager.getEcuStrLogger();
		if (ecuLogger == null) {
			return;
		}
		ecuLogger.d(tag, msg);
	}

	public static void i(String tag, String msg) {
		Log.LogInstance ecuLogger = LoggerManager.getEcuStrLogger();
		if (ecuLogger == null) {
			return;
		}
		ecuLogger.i(tag, msg);
	}

	public static void w(String tag, String msg) {
		Log.LogInstance ecuLogger = LoggerManager.getEcuStrLogger();
		if (ecuLogger == null) {
			return;
		}
		ecuLogger.w(tag, msg);
	}

	public static void e(String tag, String msg) {
		Log.LogInstance ecuLogger = LoggerManager.getEcuStrLogger();
		if (ecuLogger == null) {
			return;
		}
		ecuLogger.e(tag, msg);
	}
}

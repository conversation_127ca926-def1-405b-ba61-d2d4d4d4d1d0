package com.fjdynamics.app.logger;

import android.annotation.SuppressLint;
import android.app.Application;
import android.os.Environment;

import com.tencent.mars.xlog.Log;
import com.tencent.mars.xlog.Xlog;

import timber.log.Timber;

/**
 * 日志库初始化工具
 *
 * <AUTHOR>
 */
public class LoggerManager {

	static {
		System.loadLibrary("c++_shared");
		System.loadLibrary("marsxlog");
	}

	@SuppressLint("StaticFieldLeak")
	private static Application sApp;

	private static Log.LogInstance appLogger;
	private static Log.LogInstance rtkLogger;
	private static Log.LogInstance ecuLogger;
	private static Log.LogInstance ecuStrLogger;

	private LoggerManager() {
		throw new UnsupportedOperationException("u can't instantiate me...");
	}

	public static void init(final Application app) {
		sApp = app;
		Xlog xlog = new Xlog();
		Log.setLogImp(xlog);
		String appLogPath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/tower/log/app";
		String appCachePath = sApp.getCacheDir() + "/appLog";
		appLogger = Log.openLogInstance(
			Xlog.LEVEL_DEBUG,
			Xlog.AppednerModeAsync,
			appCachePath,
			appLogPath,
			"app",
			0
		);
		appLogger.setMaxFileSize(2 * 1024 * 1024); //单个日志文件最大2M
		appLogger.setMaxAliveTime(3 * 24 * 60 * 60);//单个日志文件最长保存3天
		appLogger.setConsoleLogOpen(true);
		String rtkLogPath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/tower/log/rtk";
		String rtkCachePath = sApp.getCacheDir() + "/rtkLog";
		rtkLogger = Log.openLogInstance(
			Xlog.LEVEL_DEBUG,
			Xlog.AppednerModeAsync,
			rtkCachePath,
			rtkLogPath,
			"rtk",
			0
		);
		rtkLogger.setMaxFileSize(2 * 1024 * 1024);
		rtkLogger.setMaxAliveTime(3 * 24 * 60 * 60);
		rtkLogger.setConsoleLogOpen(true);
		String ecuLogPath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/tower/log/ecu";
		String ecuCachePath = sApp.getCacheDir() + "/ecuLog";
		ecuLogger = Log.openLogInstance(
			Xlog.LEVEL_DEBUG,
			Xlog.AppednerModeAsync,
			ecuCachePath,
			ecuLogPath,
			"ecu",
			0
		);
		ecuLogger.setMaxFileSize(2 * 1024 * 1024);
		ecuLogger.setMaxAliveTime(3 * 24 * 60 * 60);
		ecuLogger.setConsoleLogOpen(false);

		String ecuStrLogPath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/tower/log/ecuStr";
		String ecuStrCachePath = sApp.getCacheDir() + "/ecuStrLog";
		ecuStrLogger = Log.openLogInstance(
			Xlog.LEVEL_DEBUG,
			Xlog.AppednerModeAsync,
			ecuStrCachePath,
			ecuStrLogPath,
			"ecuStr",
			0
		);
		ecuStrLogger.setMaxFileSize(2 * 1024 * 1024);
		ecuStrLogger.setMaxAliveTime(3 * 24 * 60 * 60);
		ecuStrLogger.setConsoleLogOpen(false);
		Timber.plant(new XLogTree());
	}

	public static Application getApp() {
		return sApp;
	}

	public static Log.LogInstance getAppLogger() {
		return appLogger;
	}

	public static Log.LogInstance getRtkLogger() {
		return rtkLogger;
	}

	public static Log.LogInstance getEcuLogger() {
		return ecuLogger;
	}

	public static Log.LogInstance getEcuStrLogger() {
		return ecuStrLogger;
	}

	public static void flush() {
		if (appLogger != null) {
			appLogger.appenderFlush();
		}
		if (ecuLogger != null) {
			ecuLogger.appenderFlush();
		}
		if (rtkLogger != null) {
			rtkLogger.appenderFlush();
		}
		if (ecuStrLogger != null) {
			ecuStrLogger.appenderFlush();
		}
	}
}
